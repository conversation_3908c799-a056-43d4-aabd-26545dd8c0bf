import React, { useState, useRef, useEffect } from 'react'
import { useTheme } from '../../context/ThemeContext'
import SwipeIndicator from './SwipeIndicator'

function SwipeableLayout({ 
  leftContent, 
  rightContent, 
  className = "",
  rightContentTitle = "Additional Info"
}) {
  const { theme } = useTheme()
  const [currentView, setCurrentView] = useState('left') // 'left' or 'right'
  const [showIndicator, setShowIndicator] = useState(true)
  const [touchStart, setTouchStart] = useState(null)
  const [touchEnd, setTouchEnd] = useState(null)
  const [touchStartY, setTouchStartY] = useState(null)
  const containerRef = useRef(null)

  // Minimum swipe distance (in px)
  const minSwipeDistance = 50

  // Hide indicator after 5 seconds or after first swipe
  useEffect(() => {
    const timer = setTimeout(() => {
      setShowIndicator(false)
    }, 5000)

    return () => clearTimeout(timer)
  }, [])

  const onTouchStart = (e) => {
    setTouchEnd(null) // Reset touch end
    setTouchStart(e.targetTouches[0].clientX)
    setTouchStartY(e.targetTouches[0].clientY)
  }

  const onTouchMove = (e) => {
    const touch = e.targetTouches[0]
    setTouchEnd(touch.clientX)

    // Calculate horizontal and vertical distances
    const horizontalDistance = Math.abs(touch.clientX - touchStart)
    const verticalDistance = Math.abs(touch.clientY - (touchStartY || touch.clientY))

    // If vertical movement is greater than horizontal, allow default scrolling
    if (verticalDistance > horizontalDistance) {
      return
    }

    // Only prevent default for horizontal swipes
    if (horizontalDistance > 10) {
      e.preventDefault()
    }
  }

  const onTouchEnd = () => {
    if (!touchStart || !touchEnd) return
    
    const distance = touchStart - touchEnd
    const isLeftSwipe = distance > minSwipeDistance
    const isRightSwipe = distance < -minSwipeDistance

    if (isLeftSwipe && currentView === 'left') {
      // Swipe left to show right content
      setCurrentView('right')
      setShowIndicator(false)
    } else if (isRightSwipe && currentView === 'right') {
      // Swipe right to show left content
      setCurrentView('left')
    }
  }

  // Handle back button or escape key to return to left view
  useEffect(() => {
    const handleKeyDown = (e) => {
      if (e.key === 'Escape' && currentView === 'right') {
        setCurrentView('left')
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [currentView])

  return (
    <div className={`${className}`}>
      {/* Desktop Layout - Normal two-column grid */}
      <div className="hidden lg:grid lg:grid-cols-2 gap-6 min-h-full">
        <div>{leftContent}</div>
        <div>{rightContent}</div>
      </div>

      {/* Mobile Layout - Swipeable single view */}
      <div className="lg:hidden relative min-h-full">
        {/* Swipe Indicator */}
        {showIndicator && currentView === 'left' && (
          <SwipeIndicator 
            onDismiss={() => setShowIndicator(false)}
            message="Swipe left for more info"
          />
        )}

        {/* Back indicator when on right view */}
        {currentView === 'right' && (
          <div className="absolute top-4 left-4 z-20">
            <button
              onClick={() => setCurrentView('left')}
              className={`flex items-center gap-2 ${theme.primary} text-white px-3 py-2 rounded-full shadow-lg backdrop-blur-sm`}
            >
              <span className="text-sm">←</span>
              <span className="text-xs font-medium">Back</span>
            </button>
          </div>
        )}

        {/* Swipeable Container */}
        <div
          ref={containerRef}
          className="relative w-full swipeable-container"
          onTouchStart={onTouchStart}
          onTouchMove={onTouchMove}
          onTouchEnd={onTouchEnd}
          style={{ touchAction: 'pan-y' }}
        >
          {/* Content Container */}
          <div
            className={`flex w-[200%] swipe-transition swipeable-content ${
              currentView === 'right' ? '-translate-x-1/2' : 'translate-x-0'
            }`}
          >
            {/* Left Content */}
            <div className="w-1/2 pr-2">
              <div className="pb-20">
                {leftContent}
              </div>
            </div>

            {/* Right Content */}
            <div className="w-1/2 pl-2">
              <div className="pb-20">
                {/* Right content header */}
                <div className={`${theme.card} rounded-2xl shadow-lg p-4 border ${theme.border} mb-4`}>
                  <h3 className={`text-lg font-semibold ${theme.text} text-center`}>
                    {rightContentTitle}
                  </h3>
                </div>
                {rightContent}
              </div>
            </div>
          </div>
        </div>

        {/* Page Indicator Dots */}
        <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2 z-20">
          <div 
            className={`w-2 h-2 rounded-full transition-colors ${
              currentView === 'left' ? theme.primary : 'bg-gray-400'
            }`}
          />
          <div 
            className={`w-2 h-2 rounded-full transition-colors ${
              currentView === 'right' ? theme.primary : 'bg-gray-400'
            }`}
          />
        </div>
      </div>
    </div>
  )
}

export default SwipeableLayout
