var Bg=Object.defineProperty;var Hg=(t,e,n)=>e in t?Bg(t,e,{enumerable:!0,configurable:!0,writable:!0,value:n}):t[e]=n;var F=(t,e,n)=>Hg(t,typeof e!="symbol"?e+"":e,n);function Wg(t,e){for(var n=0;n<e.length;n++){const r=e[n];if(typeof r!="string"&&!Array.isArray(r)){for(const i in r)if(i!=="default"&&!(i in t)){const s=Object.getOwnPropertyDescriptor(r,i);s&&Object.defineProperty(t,i,s.get?s:{enumerable:!0,get:()=>r[i]})}}}return Object.freeze(Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}))}(function(){const e=document.createElement("link").relList;if(e&&e.supports&&e.supports("modulepreload"))return;for(const i of document.querySelectorAll('link[rel="modulepreload"]'))r(i);new MutationObserver(i=>{for(const s of i)if(s.type==="childList")for(const a of s.addedNodes)a.tagName==="LINK"&&a.rel==="modulepreload"&&r(a)}).observe(document,{childList:!0,subtree:!0});function n(i){const s={};return i.integrity&&(s.integrity=i.integrity),i.referrerPolicy&&(s.referrerPolicy=i.referrerPolicy),i.crossOrigin==="use-credentials"?s.credentials="include":i.crossOrigin==="anonymous"?s.credentials="omit":s.credentials="same-origin",s}function r(i){if(i.ep)return;i.ep=!0;const s=n(i);fetch(i.href,s)}})();function Vg(t){return t&&t.__esModule&&Object.prototype.hasOwnProperty.call(t,"default")?t.default:t}var xf={exports:{}},Wa={},vf={exports:{}},I={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var es=Symbol.for("react.element"),Yg=Symbol.for("react.portal"),Xg=Symbol.for("react.fragment"),Kg=Symbol.for("react.strict_mode"),Qg=Symbol.for("react.profiler"),Gg=Symbol.for("react.provider"),qg=Symbol.for("react.context"),Zg=Symbol.for("react.forward_ref"),Jg=Symbol.for("react.suspense"),e0=Symbol.for("react.memo"),t0=Symbol.for("react.lazy"),Nu=Symbol.iterator;function n0(t){return t===null||typeof t!="object"?null:(t=Nu&&t[Nu]||t["@@iterator"],typeof t=="function"?t:null)}var yf={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},bf=Object.assign,wf={};function Ir(t,e,n){this.props=t,this.context=e,this.refs=wf,this.updater=n||yf}Ir.prototype.isReactComponent={};Ir.prototype.setState=function(t,e){if(typeof t!="object"&&typeof t!="function"&&t!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,t,e,"setState")};Ir.prototype.forceUpdate=function(t){this.updater.enqueueForceUpdate(this,t,"forceUpdate")};function Sf(){}Sf.prototype=Ir.prototype;function uc(t,e,n){this.props=t,this.context=e,this.refs=wf,this.updater=n||yf}var dc=uc.prototype=new Sf;dc.constructor=uc;bf(dc,Ir.prototype);dc.isPureReactComponent=!0;var _u=Array.isArray,Nf=Object.prototype.hasOwnProperty,hc={current:null},_f={key:!0,ref:!0,__self:!0,__source:!0};function kf(t,e,n){var r,i={},s=null,a=null;if(e!=null)for(r in e.ref!==void 0&&(a=e.ref),e.key!==void 0&&(s=""+e.key),e)Nf.call(e,r)&&!_f.hasOwnProperty(r)&&(i[r]=e[r]);var o=arguments.length-2;if(o===1)i.children=n;else if(1<o){for(var l=Array(o),u=0;u<o;u++)l[u]=arguments[u+2];i.children=l}if(t&&t.defaultProps)for(r in o=t.defaultProps,o)i[r]===void 0&&(i[r]=o[r]);return{$$typeof:es,type:t,key:s,ref:a,props:i,_owner:hc.current}}function r0(t,e){return{$$typeof:es,type:t.type,key:e,ref:t.ref,props:t.props,_owner:t._owner}}function fc(t){return typeof t=="object"&&t!==null&&t.$$typeof===es}function i0(t){var e={"=":"=0",":":"=2"};return"$"+t.replace(/[=:]/g,function(n){return e[n]})}var ku=/\/+/g;function mo(t,e){return typeof t=="object"&&t!==null&&t.key!=null?i0(""+t.key):e.toString(36)}function Is(t,e,n,r,i){var s=typeof t;(s==="undefined"||s==="boolean")&&(t=null);var a=!1;if(t===null)a=!0;else switch(s){case"string":case"number":a=!0;break;case"object":switch(t.$$typeof){case es:case Yg:a=!0}}if(a)return a=t,i=i(a),t=r===""?"."+mo(a,0):r,_u(i)?(n="",t!=null&&(n=t.replace(ku,"$&/")+"/"),Is(i,e,n,"",function(u){return u})):i!=null&&(fc(i)&&(i=r0(i,n+(!i.key||a&&a.key===i.key?"":(""+i.key).replace(ku,"$&/")+"/")+t)),e.push(i)),1;if(a=0,r=r===""?".":r+":",_u(t))for(var o=0;o<t.length;o++){s=t[o];var l=r+mo(s,o);a+=Is(s,e,n,l,i)}else if(l=n0(t),typeof l=="function")for(t=l.call(t),o=0;!(s=t.next()).done;)s=s.value,l=r+mo(s,o++),a+=Is(s,e,n,l,i);else if(s==="object")throw e=String(t),Error("Objects are not valid as a React child (found: "+(e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e)+"). If you meant to render a collection of children, use an array instead.");return a}function cs(t,e,n){if(t==null)return t;var r=[],i=0;return Is(t,r,"","",function(s){return e.call(n,s,i++)}),r}function s0(t){if(t._status===-1){var e=t._result;e=e(),e.then(function(n){(t._status===0||t._status===-1)&&(t._status=1,t._result=n)},function(n){(t._status===0||t._status===-1)&&(t._status=2,t._result=n)}),t._status===-1&&(t._status=0,t._result=e)}if(t._status===1)return t._result.default;throw t._result}var Le={current:null},Us={transition:null},a0={ReactCurrentDispatcher:Le,ReactCurrentBatchConfig:Us,ReactCurrentOwner:hc};function jf(){throw Error("act(...) is not supported in production builds of React.")}I.Children={map:cs,forEach:function(t,e,n){cs(t,function(){e.apply(this,arguments)},n)},count:function(t){var e=0;return cs(t,function(){e++}),e},toArray:function(t){return cs(t,function(e){return e})||[]},only:function(t){if(!fc(t))throw Error("React.Children.only expected to receive a single React element child.");return t}};I.Component=Ir;I.Fragment=Xg;I.Profiler=Qg;I.PureComponent=uc;I.StrictMode=Kg;I.Suspense=Jg;I.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=a0;I.act=jf;I.cloneElement=function(t,e,n){if(t==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+t+".");var r=bf({},t.props),i=t.key,s=t.ref,a=t._owner;if(e!=null){if(e.ref!==void 0&&(s=e.ref,a=hc.current),e.key!==void 0&&(i=""+e.key),t.type&&t.type.defaultProps)var o=t.type.defaultProps;for(l in e)Nf.call(e,l)&&!_f.hasOwnProperty(l)&&(r[l]=e[l]===void 0&&o!==void 0?o[l]:e[l])}var l=arguments.length-2;if(l===1)r.children=n;else if(1<l){o=Array(l);for(var u=0;u<l;u++)o[u]=arguments[u+2];r.children=o}return{$$typeof:es,type:t.type,key:i,ref:s,props:r,_owner:a}};I.createContext=function(t){return t={$$typeof:qg,_currentValue:t,_currentValue2:t,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},t.Provider={$$typeof:Gg,_context:t},t.Consumer=t};I.createElement=kf;I.createFactory=function(t){var e=kf.bind(null,t);return e.type=t,e};I.createRef=function(){return{current:null}};I.forwardRef=function(t){return{$$typeof:Zg,render:t}};I.isValidElement=fc;I.lazy=function(t){return{$$typeof:t0,_payload:{_status:-1,_result:t},_init:s0}};I.memo=function(t,e){return{$$typeof:e0,type:t,compare:e===void 0?null:e}};I.startTransition=function(t){var e=Us.transition;Us.transition={};try{t()}finally{Us.transition=e}};I.unstable_act=jf;I.useCallback=function(t,e){return Le.current.useCallback(t,e)};I.useContext=function(t){return Le.current.useContext(t)};I.useDebugValue=function(){};I.useDeferredValue=function(t){return Le.current.useDeferredValue(t)};I.useEffect=function(t,e){return Le.current.useEffect(t,e)};I.useId=function(){return Le.current.useId()};I.useImperativeHandle=function(t,e,n){return Le.current.useImperativeHandle(t,e,n)};I.useInsertionEffect=function(t,e){return Le.current.useInsertionEffect(t,e)};I.useLayoutEffect=function(t,e){return Le.current.useLayoutEffect(t,e)};I.useMemo=function(t,e){return Le.current.useMemo(t,e)};I.useReducer=function(t,e,n){return Le.current.useReducer(t,e,n)};I.useRef=function(t){return Le.current.useRef(t)};I.useState=function(t){return Le.current.useState(t)};I.useSyncExternalStore=function(t,e,n){return Le.current.useSyncExternalStore(t,e,n)};I.useTransition=function(){return Le.current.useTransition()};I.version="18.3.1";vf.exports=I;var _=vf.exports;const bt=Vg(_),o0=Wg({__proto__:null,default:bt},[_]);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var l0=_,c0=Symbol.for("react.element"),u0=Symbol.for("react.fragment"),d0=Object.prototype.hasOwnProperty,h0=l0.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,f0={key:!0,ref:!0,__self:!0,__source:!0};function Cf(t,e,n){var r,i={},s=null,a=null;n!==void 0&&(s=""+n),e.key!==void 0&&(s=""+e.key),e.ref!==void 0&&(a=e.ref);for(r in e)d0.call(e,r)&&!f0.hasOwnProperty(r)&&(i[r]=e[r]);if(t&&t.defaultProps)for(r in e=t.defaultProps,e)i[r]===void 0&&(i[r]=e[r]);return{$$typeof:c0,type:t,key:s,ref:a,props:i,_owner:h0.current}}Wa.Fragment=u0;Wa.jsx=Cf;Wa.jsxs=Cf;xf.exports=Wa;var c=xf.exports,tl={},Pf={exports:{}},Je={},Ef={exports:{}},Tf={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(t){function e(T,L){var A=T.length;T.push(L);e:for(;0<A;){var R=A-1>>>1,H=T[R];if(0<i(H,L))T[R]=L,T[A]=H,A=R;else break e}}function n(T){return T.length===0?null:T[0]}function r(T){if(T.length===0)return null;var L=T[0],A=T.pop();if(A!==L){T[0]=A;e:for(var R=0,H=T.length,ze=H>>>1;R<ze;){var pe=2*(R+1)-1,dt=T[pe],Se=pe+1,ls=T[Se];if(0>i(dt,A))Se<H&&0>i(ls,dt)?(T[R]=ls,T[Se]=A,R=Se):(T[R]=dt,T[pe]=A,R=pe);else if(Se<H&&0>i(ls,A))T[R]=ls,T[Se]=A,R=Se;else break e}}return L}function i(T,L){var A=T.sortIndex-L.sortIndex;return A!==0?A:T.id-L.id}if(typeof performance=="object"&&typeof performance.now=="function"){var s=performance;t.unstable_now=function(){return s.now()}}else{var a=Date,o=a.now();t.unstable_now=function(){return a.now()-o}}var l=[],u=[],d=1,h=null,f=3,m=!1,x=!1,v=!1,w=typeof setTimeout=="function"?setTimeout:null,g=typeof clearTimeout=="function"?clearTimeout:null,p=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function y(T){for(var L=n(u);L!==null;){if(L.callback===null)r(u);else if(L.startTime<=T)r(u),L.sortIndex=L.expirationTime,e(l,L);else break;L=n(u)}}function b(T){if(v=!1,y(T),!x)if(n(l)!==null)x=!0,B(S);else{var L=n(u);L!==null&&Y(b,L.startTime-T)}}function S(T,L){x=!1,v&&(v=!1,g(k),k=-1),m=!0;var A=f;try{for(y(L),h=n(l);h!==null&&(!(h.expirationTime>L)||T&&!M());){var R=h.callback;if(typeof R=="function"){h.callback=null,f=h.priorityLevel;var H=R(h.expirationTime<=L);L=t.unstable_now(),typeof H=="function"?h.callback=H:h===n(l)&&r(l),y(L)}else r(l);h=n(l)}if(h!==null)var ze=!0;else{var pe=n(u);pe!==null&&Y(b,pe.startTime-L),ze=!1}return ze}finally{h=null,f=A,m=!1}}var j=!1,N=null,k=-1,C=5,P=-1;function M(){return!(t.unstable_now()-P<C)}function D(){if(N!==null){var T=t.unstable_now();P=T;var L=!0;try{L=N(!0,T)}finally{L?$():(j=!1,N=null)}}else j=!1}var $;if(typeof p=="function")$=function(){p(D)};else if(typeof MessageChannel<"u"){var U=new MessageChannel,z=U.port2;U.port1.onmessage=D,$=function(){z.postMessage(null)}}else $=function(){w(D,0)};function B(T){N=T,j||(j=!0,$())}function Y(T,L){k=w(function(){T(t.unstable_now())},L)}t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(T){T.callback=null},t.unstable_continueExecution=function(){x||m||(x=!0,B(S))},t.unstable_forceFrameRate=function(T){0>T||125<T?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):C=0<T?Math.floor(1e3/T):5},t.unstable_getCurrentPriorityLevel=function(){return f},t.unstable_getFirstCallbackNode=function(){return n(l)},t.unstable_next=function(T){switch(f){case 1:case 2:case 3:var L=3;break;default:L=f}var A=f;f=L;try{return T()}finally{f=A}},t.unstable_pauseExecution=function(){},t.unstable_requestPaint=function(){},t.unstable_runWithPriority=function(T,L){switch(T){case 1:case 2:case 3:case 4:case 5:break;default:T=3}var A=f;f=T;try{return L()}finally{f=A}},t.unstable_scheduleCallback=function(T,L,A){var R=t.unstable_now();switch(typeof A=="object"&&A!==null?(A=A.delay,A=typeof A=="number"&&0<A?R+A:R):A=R,T){case 1:var H=-1;break;case 2:H=250;break;case 5:H=**********;break;case 4:H=1e4;break;default:H=5e3}return H=A+H,T={id:d++,callback:L,priorityLevel:T,startTime:A,expirationTime:H,sortIndex:-1},A>R?(T.sortIndex=A,e(u,T),n(l)===null&&T===n(u)&&(v?(g(k),k=-1):v=!0,Y(b,A-R))):(T.sortIndex=H,e(l,T),x||m||(x=!0,B(S))),T},t.unstable_shouldYield=M,t.unstable_wrapCallback=function(T){var L=f;return function(){var A=f;f=L;try{return T.apply(this,arguments)}finally{f=A}}}})(Tf);Ef.exports=Tf;var m0=Ef.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var p0=_,Ze=m0;function E(t){for(var e="https://reactjs.org/docs/error-decoder.html?invariant="+t,n=1;n<arguments.length;n++)e+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+t+"; visit "+e+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var Mf=new Set,Ei={};function Zn(t,e){Er(t,e),Er(t+"Capture",e)}function Er(t,e){for(Ei[t]=e,t=0;t<e.length;t++)Mf.add(e[t])}var Wt=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),nl=Object.prototype.hasOwnProperty,g0=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,ju={},Cu={};function x0(t){return nl.call(Cu,t)?!0:nl.call(ju,t)?!1:g0.test(t)?Cu[t]=!0:(ju[t]=!0,!1)}function v0(t,e,n,r){if(n!==null&&n.type===0)return!1;switch(typeof e){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(t=t.toLowerCase().slice(0,5),t!=="data-"&&t!=="aria-");default:return!1}}function y0(t,e,n,r){if(e===null||typeof e>"u"||v0(t,e,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!e;case 4:return e===!1;case 5:return isNaN(e);case 6:return isNaN(e)||1>e}return!1}function Ae(t,e,n,r,i,s,a){this.acceptsBooleans=e===2||e===3||e===4,this.attributeName=r,this.attributeNamespace=i,this.mustUseProperty=n,this.propertyName=t,this.type=e,this.sanitizeURL=s,this.removeEmptyString=a}var ke={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(t){ke[t]=new Ae(t,0,!1,t,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(t){var e=t[0];ke[e]=new Ae(e,1,!1,t[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(t){ke[t]=new Ae(t,2,!1,t.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(t){ke[t]=new Ae(t,2,!1,t,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(t){ke[t]=new Ae(t,3,!1,t.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(t){ke[t]=new Ae(t,3,!0,t,null,!1,!1)});["capture","download"].forEach(function(t){ke[t]=new Ae(t,4,!1,t,null,!1,!1)});["cols","rows","size","span"].forEach(function(t){ke[t]=new Ae(t,6,!1,t,null,!1,!1)});["rowSpan","start"].forEach(function(t){ke[t]=new Ae(t,5,!1,t.toLowerCase(),null,!1,!1)});var mc=/[\-:]([a-z])/g;function pc(t){return t[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(t){var e=t.replace(mc,pc);ke[e]=new Ae(e,1,!1,t,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(t){var e=t.replace(mc,pc);ke[e]=new Ae(e,1,!1,t,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(t){var e=t.replace(mc,pc);ke[e]=new Ae(e,1,!1,t,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(t){ke[t]=new Ae(t,1,!1,t.toLowerCase(),null,!1,!1)});ke.xlinkHref=new Ae("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(t){ke[t]=new Ae(t,1,!1,t.toLowerCase(),null,!0,!0)});function gc(t,e,n,r){var i=ke.hasOwnProperty(e)?ke[e]:null;(i!==null?i.type!==0:r||!(2<e.length)||e[0]!=="o"&&e[0]!=="O"||e[1]!=="n"&&e[1]!=="N")&&(y0(e,n,i,r)&&(n=null),r||i===null?x0(e)&&(n===null?t.removeAttribute(e):t.setAttribute(e,""+n)):i.mustUseProperty?t[i.propertyName]=n===null?i.type===3?!1:"":n:(e=i.attributeName,r=i.attributeNamespace,n===null?t.removeAttribute(e):(i=i.type,n=i===3||i===4&&n===!0?"":""+n,r?t.setAttributeNS(r,e,n):t.setAttribute(e,n))))}var Qt=p0.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,us=Symbol.for("react.element"),or=Symbol.for("react.portal"),lr=Symbol.for("react.fragment"),xc=Symbol.for("react.strict_mode"),rl=Symbol.for("react.profiler"),$f=Symbol.for("react.provider"),Rf=Symbol.for("react.context"),vc=Symbol.for("react.forward_ref"),il=Symbol.for("react.suspense"),sl=Symbol.for("react.suspense_list"),yc=Symbol.for("react.memo"),Jt=Symbol.for("react.lazy"),Df=Symbol.for("react.offscreen"),Pu=Symbol.iterator;function Xr(t){return t===null||typeof t!="object"?null:(t=Pu&&t[Pu]||t["@@iterator"],typeof t=="function"?t:null)}var ae=Object.assign,po;function li(t){if(po===void 0)try{throw Error()}catch(n){var e=n.stack.trim().match(/\n( *(at )?)/);po=e&&e[1]||""}return`
`+po+t}var go=!1;function xo(t,e){if(!t||go)return"";go=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(e)if(e=function(){throw Error()},Object.defineProperty(e.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(e,[])}catch(u){var r=u}Reflect.construct(t,[],e)}else{try{e.call()}catch(u){r=u}t.call(e.prototype)}else{try{throw Error()}catch(u){r=u}t()}}catch(u){if(u&&r&&typeof u.stack=="string"){for(var i=u.stack.split(`
`),s=r.stack.split(`
`),a=i.length-1,o=s.length-1;1<=a&&0<=o&&i[a]!==s[o];)o--;for(;1<=a&&0<=o;a--,o--)if(i[a]!==s[o]){if(a!==1||o!==1)do if(a--,o--,0>o||i[a]!==s[o]){var l=`
`+i[a].replace(" at new "," at ");return t.displayName&&l.includes("<anonymous>")&&(l=l.replace("<anonymous>",t.displayName)),l}while(1<=a&&0<=o);break}}}finally{go=!1,Error.prepareStackTrace=n}return(t=t?t.displayName||t.name:"")?li(t):""}function b0(t){switch(t.tag){case 5:return li(t.type);case 16:return li("Lazy");case 13:return li("Suspense");case 19:return li("SuspenseList");case 0:case 2:case 15:return t=xo(t.type,!1),t;case 11:return t=xo(t.type.render,!1),t;case 1:return t=xo(t.type,!0),t;default:return""}}function al(t){if(t==null)return null;if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t;switch(t){case lr:return"Fragment";case or:return"Portal";case rl:return"Profiler";case xc:return"StrictMode";case il:return"Suspense";case sl:return"SuspenseList"}if(typeof t=="object")switch(t.$$typeof){case Rf:return(t.displayName||"Context")+".Consumer";case $f:return(t._context.displayName||"Context")+".Provider";case vc:var e=t.render;return t=t.displayName,t||(t=e.displayName||e.name||"",t=t!==""?"ForwardRef("+t+")":"ForwardRef"),t;case yc:return e=t.displayName||null,e!==null?e:al(t.type)||"Memo";case Jt:e=t._payload,t=t._init;try{return al(t(e))}catch{}}return null}function w0(t){var e=t.type;switch(t.tag){case 24:return"Cache";case 9:return(e.displayName||"Context")+".Consumer";case 10:return(e._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return t=e.render,t=t.displayName||t.name||"",e.displayName||(t!==""?"ForwardRef("+t+")":"ForwardRef");case 7:return"Fragment";case 5:return e;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return al(e);case 8:return e===xc?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e}return null}function wn(t){switch(typeof t){case"boolean":case"number":case"string":case"undefined":return t;case"object":return t;default:return""}}function Of(t){var e=t.type;return(t=t.nodeName)&&t.toLowerCase()==="input"&&(e==="checkbox"||e==="radio")}function S0(t){var e=Of(t)?"checked":"value",n=Object.getOwnPropertyDescriptor(t.constructor.prototype,e),r=""+t[e];if(!t.hasOwnProperty(e)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var i=n.get,s=n.set;return Object.defineProperty(t,e,{configurable:!0,get:function(){return i.call(this)},set:function(a){r=""+a,s.call(this,a)}}),Object.defineProperty(t,e,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(a){r=""+a},stopTracking:function(){t._valueTracker=null,delete t[e]}}}}function ds(t){t._valueTracker||(t._valueTracker=S0(t))}function Lf(t){if(!t)return!1;var e=t._valueTracker;if(!e)return!0;var n=e.getValue(),r="";return t&&(r=Of(t)?t.checked?"true":"false":t.value),t=r,t!==n?(e.setValue(t),!0):!1}function ra(t){if(t=t||(typeof document<"u"?document:void 0),typeof t>"u")return null;try{return t.activeElement||t.body}catch{return t.body}}function ol(t,e){var n=e.checked;return ae({},e,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??t._wrapperState.initialChecked})}function Eu(t,e){var n=e.defaultValue==null?"":e.defaultValue,r=e.checked!=null?e.checked:e.defaultChecked;n=wn(e.value!=null?e.value:n),t._wrapperState={initialChecked:r,initialValue:n,controlled:e.type==="checkbox"||e.type==="radio"?e.checked!=null:e.value!=null}}function Af(t,e){e=e.checked,e!=null&&gc(t,"checked",e,!1)}function ll(t,e){Af(t,e);var n=wn(e.value),r=e.type;if(n!=null)r==="number"?(n===0&&t.value===""||t.value!=n)&&(t.value=""+n):t.value!==""+n&&(t.value=""+n);else if(r==="submit"||r==="reset"){t.removeAttribute("value");return}e.hasOwnProperty("value")?cl(t,e.type,n):e.hasOwnProperty("defaultValue")&&cl(t,e.type,wn(e.defaultValue)),e.checked==null&&e.defaultChecked!=null&&(t.defaultChecked=!!e.defaultChecked)}function Tu(t,e,n){if(e.hasOwnProperty("value")||e.hasOwnProperty("defaultValue")){var r=e.type;if(!(r!=="submit"&&r!=="reset"||e.value!==void 0&&e.value!==null))return;e=""+t._wrapperState.initialValue,n||e===t.value||(t.value=e),t.defaultValue=e}n=t.name,n!==""&&(t.name=""),t.defaultChecked=!!t._wrapperState.initialChecked,n!==""&&(t.name=n)}function cl(t,e,n){(e!=="number"||ra(t.ownerDocument)!==t)&&(n==null?t.defaultValue=""+t._wrapperState.initialValue:t.defaultValue!==""+n&&(t.defaultValue=""+n))}var ci=Array.isArray;function yr(t,e,n,r){if(t=t.options,e){e={};for(var i=0;i<n.length;i++)e["$"+n[i]]=!0;for(n=0;n<t.length;n++)i=e.hasOwnProperty("$"+t[n].value),t[n].selected!==i&&(t[n].selected=i),i&&r&&(t[n].defaultSelected=!0)}else{for(n=""+wn(n),e=null,i=0;i<t.length;i++){if(t[i].value===n){t[i].selected=!0,r&&(t[i].defaultSelected=!0);return}e!==null||t[i].disabled||(e=t[i])}e!==null&&(e.selected=!0)}}function ul(t,e){if(e.dangerouslySetInnerHTML!=null)throw Error(E(91));return ae({},e,{value:void 0,defaultValue:void 0,children:""+t._wrapperState.initialValue})}function Mu(t,e){var n=e.value;if(n==null){if(n=e.children,e=e.defaultValue,n!=null){if(e!=null)throw Error(E(92));if(ci(n)){if(1<n.length)throw Error(E(93));n=n[0]}e=n}e==null&&(e=""),n=e}t._wrapperState={initialValue:wn(n)}}function Ff(t,e){var n=wn(e.value),r=wn(e.defaultValue);n!=null&&(n=""+n,n!==t.value&&(t.value=n),e.defaultValue==null&&t.defaultValue!==n&&(t.defaultValue=n)),r!=null&&(t.defaultValue=""+r)}function $u(t){var e=t.textContent;e===t._wrapperState.initialValue&&e!==""&&e!==null&&(t.value=e)}function zf(t){switch(t){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function dl(t,e){return t==null||t==="http://www.w3.org/1999/xhtml"?zf(e):t==="http://www.w3.org/2000/svg"&&e==="foreignObject"?"http://www.w3.org/1999/xhtml":t}var hs,If=function(t){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(e,n,r,i){MSApp.execUnsafeLocalFunction(function(){return t(e,n,r,i)})}:t}(function(t,e){if(t.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in t)t.innerHTML=e;else{for(hs=hs||document.createElement("div"),hs.innerHTML="<svg>"+e.valueOf().toString()+"</svg>",e=hs.firstChild;t.firstChild;)t.removeChild(t.firstChild);for(;e.firstChild;)t.appendChild(e.firstChild)}});function Ti(t,e){if(e){var n=t.firstChild;if(n&&n===t.lastChild&&n.nodeType===3){n.nodeValue=e;return}}t.textContent=e}var xi={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},N0=["Webkit","ms","Moz","O"];Object.keys(xi).forEach(function(t){N0.forEach(function(e){e=e+t.charAt(0).toUpperCase()+t.substring(1),xi[e]=xi[t]})});function Uf(t,e,n){return e==null||typeof e=="boolean"||e===""?"":n||typeof e!="number"||e===0||xi.hasOwnProperty(t)&&xi[t]?(""+e).trim():e+"px"}function Bf(t,e){t=t.style;for(var n in e)if(e.hasOwnProperty(n)){var r=n.indexOf("--")===0,i=Uf(n,e[n],r);n==="float"&&(n="cssFloat"),r?t.setProperty(n,i):t[n]=i}}var _0=ae({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function hl(t,e){if(e){if(_0[t]&&(e.children!=null||e.dangerouslySetInnerHTML!=null))throw Error(E(137,t));if(e.dangerouslySetInnerHTML!=null){if(e.children!=null)throw Error(E(60));if(typeof e.dangerouslySetInnerHTML!="object"||!("__html"in e.dangerouslySetInnerHTML))throw Error(E(61))}if(e.style!=null&&typeof e.style!="object")throw Error(E(62))}}function fl(t,e){if(t.indexOf("-")===-1)return typeof e.is=="string";switch(t){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var ml=null;function bc(t){return t=t.target||t.srcElement||window,t.correspondingUseElement&&(t=t.correspondingUseElement),t.nodeType===3?t.parentNode:t}var pl=null,br=null,wr=null;function Ru(t){if(t=rs(t)){if(typeof pl!="function")throw Error(E(280));var e=t.stateNode;e&&(e=Qa(e),pl(t.stateNode,t.type,e))}}function Hf(t){br?wr?wr.push(t):wr=[t]:br=t}function Wf(){if(br){var t=br,e=wr;if(wr=br=null,Ru(t),e)for(t=0;t<e.length;t++)Ru(e[t])}}function Vf(t,e){return t(e)}function Yf(){}var vo=!1;function Xf(t,e,n){if(vo)return t(e,n);vo=!0;try{return Vf(t,e,n)}finally{vo=!1,(br!==null||wr!==null)&&(Yf(),Wf())}}function Mi(t,e){var n=t.stateNode;if(n===null)return null;var r=Qa(n);if(r===null)return null;n=r[e];e:switch(e){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(t=t.type,r=!(t==="button"||t==="input"||t==="select"||t==="textarea")),t=!r;break e;default:t=!1}if(t)return null;if(n&&typeof n!="function")throw Error(E(231,e,typeof n));return n}var gl=!1;if(Wt)try{var Kr={};Object.defineProperty(Kr,"passive",{get:function(){gl=!0}}),window.addEventListener("test",Kr,Kr),window.removeEventListener("test",Kr,Kr)}catch{gl=!1}function k0(t,e,n,r,i,s,a,o,l){var u=Array.prototype.slice.call(arguments,3);try{e.apply(n,u)}catch(d){this.onError(d)}}var vi=!1,ia=null,sa=!1,xl=null,j0={onError:function(t){vi=!0,ia=t}};function C0(t,e,n,r,i,s,a,o,l){vi=!1,ia=null,k0.apply(j0,arguments)}function P0(t,e,n,r,i,s,a,o,l){if(C0.apply(this,arguments),vi){if(vi){var u=ia;vi=!1,ia=null}else throw Error(E(198));sa||(sa=!0,xl=u)}}function Jn(t){var e=t,n=t;if(t.alternate)for(;e.return;)e=e.return;else{t=e;do e=t,e.flags&4098&&(n=e.return),t=e.return;while(t)}return e.tag===3?n:null}function Kf(t){if(t.tag===13){var e=t.memoizedState;if(e===null&&(t=t.alternate,t!==null&&(e=t.memoizedState)),e!==null)return e.dehydrated}return null}function Du(t){if(Jn(t)!==t)throw Error(E(188))}function E0(t){var e=t.alternate;if(!e){if(e=Jn(t),e===null)throw Error(E(188));return e!==t?null:t}for(var n=t,r=e;;){var i=n.return;if(i===null)break;var s=i.alternate;if(s===null){if(r=i.return,r!==null){n=r;continue}break}if(i.child===s.child){for(s=i.child;s;){if(s===n)return Du(i),t;if(s===r)return Du(i),e;s=s.sibling}throw Error(E(188))}if(n.return!==r.return)n=i,r=s;else{for(var a=!1,o=i.child;o;){if(o===n){a=!0,n=i,r=s;break}if(o===r){a=!0,r=i,n=s;break}o=o.sibling}if(!a){for(o=s.child;o;){if(o===n){a=!0,n=s,r=i;break}if(o===r){a=!0,r=s,n=i;break}o=o.sibling}if(!a)throw Error(E(189))}}if(n.alternate!==r)throw Error(E(190))}if(n.tag!==3)throw Error(E(188));return n.stateNode.current===n?t:e}function Qf(t){return t=E0(t),t!==null?Gf(t):null}function Gf(t){if(t.tag===5||t.tag===6)return t;for(t=t.child;t!==null;){var e=Gf(t);if(e!==null)return e;t=t.sibling}return null}var qf=Ze.unstable_scheduleCallback,Ou=Ze.unstable_cancelCallback,T0=Ze.unstable_shouldYield,M0=Ze.unstable_requestPaint,ue=Ze.unstable_now,$0=Ze.unstable_getCurrentPriorityLevel,wc=Ze.unstable_ImmediatePriority,Zf=Ze.unstable_UserBlockingPriority,aa=Ze.unstable_NormalPriority,R0=Ze.unstable_LowPriority,Jf=Ze.unstable_IdlePriority,Va=null,Tt=null;function D0(t){if(Tt&&typeof Tt.onCommitFiberRoot=="function")try{Tt.onCommitFiberRoot(Va,t,void 0,(t.current.flags&128)===128)}catch{}}var wt=Math.clz32?Math.clz32:A0,O0=Math.log,L0=Math.LN2;function A0(t){return t>>>=0,t===0?32:31-(O0(t)/L0|0)|0}var fs=64,ms=4194304;function ui(t){switch(t&-t){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return t&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return t}}function oa(t,e){var n=t.pendingLanes;if(n===0)return 0;var r=0,i=t.suspendedLanes,s=t.pingedLanes,a=n&268435455;if(a!==0){var o=a&~i;o!==0?r=ui(o):(s&=a,s!==0&&(r=ui(s)))}else a=n&~i,a!==0?r=ui(a):s!==0&&(r=ui(s));if(r===0)return 0;if(e!==0&&e!==r&&!(e&i)&&(i=r&-r,s=e&-e,i>=s||i===16&&(s&4194240)!==0))return e;if(r&4&&(r|=n&16),e=t.entangledLanes,e!==0)for(t=t.entanglements,e&=r;0<e;)n=31-wt(e),i=1<<n,r|=t[n],e&=~i;return r}function F0(t,e){switch(t){case 1:case 2:case 4:return e+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function z0(t,e){for(var n=t.suspendedLanes,r=t.pingedLanes,i=t.expirationTimes,s=t.pendingLanes;0<s;){var a=31-wt(s),o=1<<a,l=i[a];l===-1?(!(o&n)||o&r)&&(i[a]=F0(o,e)):l<=e&&(t.expiredLanes|=o),s&=~o}}function vl(t){return t=t.pendingLanes&-1073741825,t!==0?t:t&1073741824?1073741824:0}function em(){var t=fs;return fs<<=1,!(fs&4194240)&&(fs=64),t}function yo(t){for(var e=[],n=0;31>n;n++)e.push(t);return e}function ts(t,e,n){t.pendingLanes|=e,e!==536870912&&(t.suspendedLanes=0,t.pingedLanes=0),t=t.eventTimes,e=31-wt(e),t[e]=n}function I0(t,e){var n=t.pendingLanes&~e;t.pendingLanes=e,t.suspendedLanes=0,t.pingedLanes=0,t.expiredLanes&=e,t.mutableReadLanes&=e,t.entangledLanes&=e,e=t.entanglements;var r=t.eventTimes;for(t=t.expirationTimes;0<n;){var i=31-wt(n),s=1<<i;e[i]=0,r[i]=-1,t[i]=-1,n&=~s}}function Sc(t,e){var n=t.entangledLanes|=e;for(t=t.entanglements;n;){var r=31-wt(n),i=1<<r;i&e|t[r]&e&&(t[r]|=e),n&=~i}}var G=0;function tm(t){return t&=-t,1<t?4<t?t&268435455?16:536870912:4:1}var nm,Nc,rm,im,sm,yl=!1,ps=[],cn=null,un=null,dn=null,$i=new Map,Ri=new Map,tn=[],U0="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Lu(t,e){switch(t){case"focusin":case"focusout":cn=null;break;case"dragenter":case"dragleave":un=null;break;case"mouseover":case"mouseout":dn=null;break;case"pointerover":case"pointerout":$i.delete(e.pointerId);break;case"gotpointercapture":case"lostpointercapture":Ri.delete(e.pointerId)}}function Qr(t,e,n,r,i,s){return t===null||t.nativeEvent!==s?(t={blockedOn:e,domEventName:n,eventSystemFlags:r,nativeEvent:s,targetContainers:[i]},e!==null&&(e=rs(e),e!==null&&Nc(e)),t):(t.eventSystemFlags|=r,e=t.targetContainers,i!==null&&e.indexOf(i)===-1&&e.push(i),t)}function B0(t,e,n,r,i){switch(e){case"focusin":return cn=Qr(cn,t,e,n,r,i),!0;case"dragenter":return un=Qr(un,t,e,n,r,i),!0;case"mouseover":return dn=Qr(dn,t,e,n,r,i),!0;case"pointerover":var s=i.pointerId;return $i.set(s,Qr($i.get(s)||null,t,e,n,r,i)),!0;case"gotpointercapture":return s=i.pointerId,Ri.set(s,Qr(Ri.get(s)||null,t,e,n,r,i)),!0}return!1}function am(t){var e=An(t.target);if(e!==null){var n=Jn(e);if(n!==null){if(e=n.tag,e===13){if(e=Kf(n),e!==null){t.blockedOn=e,sm(t.priority,function(){rm(n)});return}}else if(e===3&&n.stateNode.current.memoizedState.isDehydrated){t.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}t.blockedOn=null}function Bs(t){if(t.blockedOn!==null)return!1;for(var e=t.targetContainers;0<e.length;){var n=bl(t.domEventName,t.eventSystemFlags,e[0],t.nativeEvent);if(n===null){n=t.nativeEvent;var r=new n.constructor(n.type,n);ml=r,n.target.dispatchEvent(r),ml=null}else return e=rs(n),e!==null&&Nc(e),t.blockedOn=n,!1;e.shift()}return!0}function Au(t,e,n){Bs(t)&&n.delete(e)}function H0(){yl=!1,cn!==null&&Bs(cn)&&(cn=null),un!==null&&Bs(un)&&(un=null),dn!==null&&Bs(dn)&&(dn=null),$i.forEach(Au),Ri.forEach(Au)}function Gr(t,e){t.blockedOn===e&&(t.blockedOn=null,yl||(yl=!0,Ze.unstable_scheduleCallback(Ze.unstable_NormalPriority,H0)))}function Di(t){function e(i){return Gr(i,t)}if(0<ps.length){Gr(ps[0],t);for(var n=1;n<ps.length;n++){var r=ps[n];r.blockedOn===t&&(r.blockedOn=null)}}for(cn!==null&&Gr(cn,t),un!==null&&Gr(un,t),dn!==null&&Gr(dn,t),$i.forEach(e),Ri.forEach(e),n=0;n<tn.length;n++)r=tn[n],r.blockedOn===t&&(r.blockedOn=null);for(;0<tn.length&&(n=tn[0],n.blockedOn===null);)am(n),n.blockedOn===null&&tn.shift()}var Sr=Qt.ReactCurrentBatchConfig,la=!0;function W0(t,e,n,r){var i=G,s=Sr.transition;Sr.transition=null;try{G=1,_c(t,e,n,r)}finally{G=i,Sr.transition=s}}function V0(t,e,n,r){var i=G,s=Sr.transition;Sr.transition=null;try{G=4,_c(t,e,n,r)}finally{G=i,Sr.transition=s}}function _c(t,e,n,r){if(la){var i=bl(t,e,n,r);if(i===null)Eo(t,e,r,ca,n),Lu(t,r);else if(B0(i,t,e,n,r))r.stopPropagation();else if(Lu(t,r),e&4&&-1<U0.indexOf(t)){for(;i!==null;){var s=rs(i);if(s!==null&&nm(s),s=bl(t,e,n,r),s===null&&Eo(t,e,r,ca,n),s===i)break;i=s}i!==null&&r.stopPropagation()}else Eo(t,e,r,null,n)}}var ca=null;function bl(t,e,n,r){if(ca=null,t=bc(r),t=An(t),t!==null)if(e=Jn(t),e===null)t=null;else if(n=e.tag,n===13){if(t=Kf(e),t!==null)return t;t=null}else if(n===3){if(e.stateNode.current.memoizedState.isDehydrated)return e.tag===3?e.stateNode.containerInfo:null;t=null}else e!==t&&(t=null);return ca=t,null}function om(t){switch(t){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch($0()){case wc:return 1;case Zf:return 4;case aa:case R0:return 16;case Jf:return 536870912;default:return 16}default:return 16}}var rn=null,kc=null,Hs=null;function lm(){if(Hs)return Hs;var t,e=kc,n=e.length,r,i="value"in rn?rn.value:rn.textContent,s=i.length;for(t=0;t<n&&e[t]===i[t];t++);var a=n-t;for(r=1;r<=a&&e[n-r]===i[s-r];r++);return Hs=i.slice(t,1<r?1-r:void 0)}function Ws(t){var e=t.keyCode;return"charCode"in t?(t=t.charCode,t===0&&e===13&&(t=13)):t=e,t===10&&(t=13),32<=t||t===13?t:0}function gs(){return!0}function Fu(){return!1}function et(t){function e(n,r,i,s,a){this._reactName=n,this._targetInst=i,this.type=r,this.nativeEvent=s,this.target=a,this.currentTarget=null;for(var o in t)t.hasOwnProperty(o)&&(n=t[o],this[o]=n?n(s):s[o]);return this.isDefaultPrevented=(s.defaultPrevented!=null?s.defaultPrevented:s.returnValue===!1)?gs:Fu,this.isPropagationStopped=Fu,this}return ae(e.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=gs)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=gs)},persist:function(){},isPersistent:gs}),e}var Ur={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(t){return t.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},jc=et(Ur),ns=ae({},Ur,{view:0,detail:0}),Y0=et(ns),bo,wo,qr,Ya=ae({},ns,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Cc,button:0,buttons:0,relatedTarget:function(t){return t.relatedTarget===void 0?t.fromElement===t.srcElement?t.toElement:t.fromElement:t.relatedTarget},movementX:function(t){return"movementX"in t?t.movementX:(t!==qr&&(qr&&t.type==="mousemove"?(bo=t.screenX-qr.screenX,wo=t.screenY-qr.screenY):wo=bo=0,qr=t),bo)},movementY:function(t){return"movementY"in t?t.movementY:wo}}),zu=et(Ya),X0=ae({},Ya,{dataTransfer:0}),K0=et(X0),Q0=ae({},ns,{relatedTarget:0}),So=et(Q0),G0=ae({},Ur,{animationName:0,elapsedTime:0,pseudoElement:0}),q0=et(G0),Z0=ae({},Ur,{clipboardData:function(t){return"clipboardData"in t?t.clipboardData:window.clipboardData}}),J0=et(Z0),ex=ae({},Ur,{data:0}),Iu=et(ex),tx={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},nx={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},rx={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function ix(t){var e=this.nativeEvent;return e.getModifierState?e.getModifierState(t):(t=rx[t])?!!e[t]:!1}function Cc(){return ix}var sx=ae({},ns,{key:function(t){if(t.key){var e=tx[t.key]||t.key;if(e!=="Unidentified")return e}return t.type==="keypress"?(t=Ws(t),t===13?"Enter":String.fromCharCode(t)):t.type==="keydown"||t.type==="keyup"?nx[t.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Cc,charCode:function(t){return t.type==="keypress"?Ws(t):0},keyCode:function(t){return t.type==="keydown"||t.type==="keyup"?t.keyCode:0},which:function(t){return t.type==="keypress"?Ws(t):t.type==="keydown"||t.type==="keyup"?t.keyCode:0}}),ax=et(sx),ox=ae({},Ya,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Uu=et(ox),lx=ae({},ns,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Cc}),cx=et(lx),ux=ae({},Ur,{propertyName:0,elapsedTime:0,pseudoElement:0}),dx=et(ux),hx=ae({},Ya,{deltaX:function(t){return"deltaX"in t?t.deltaX:"wheelDeltaX"in t?-t.wheelDeltaX:0},deltaY:function(t){return"deltaY"in t?t.deltaY:"wheelDeltaY"in t?-t.wheelDeltaY:"wheelDelta"in t?-t.wheelDelta:0},deltaZ:0,deltaMode:0}),fx=et(hx),mx=[9,13,27,32],Pc=Wt&&"CompositionEvent"in window,yi=null;Wt&&"documentMode"in document&&(yi=document.documentMode);var px=Wt&&"TextEvent"in window&&!yi,cm=Wt&&(!Pc||yi&&8<yi&&11>=yi),Bu=" ",Hu=!1;function um(t,e){switch(t){case"keyup":return mx.indexOf(e.keyCode)!==-1;case"keydown":return e.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function dm(t){return t=t.detail,typeof t=="object"&&"data"in t?t.data:null}var cr=!1;function gx(t,e){switch(t){case"compositionend":return dm(e);case"keypress":return e.which!==32?null:(Hu=!0,Bu);case"textInput":return t=e.data,t===Bu&&Hu?null:t;default:return null}}function xx(t,e){if(cr)return t==="compositionend"||!Pc&&um(t,e)?(t=lm(),Hs=kc=rn=null,cr=!1,t):null;switch(t){case"paste":return null;case"keypress":if(!(e.ctrlKey||e.altKey||e.metaKey)||e.ctrlKey&&e.altKey){if(e.char&&1<e.char.length)return e.char;if(e.which)return String.fromCharCode(e.which)}return null;case"compositionend":return cm&&e.locale!=="ko"?null:e.data;default:return null}}var vx={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Wu(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e==="input"?!!vx[t.type]:e==="textarea"}function hm(t,e,n,r){Hf(r),e=ua(e,"onChange"),0<e.length&&(n=new jc("onChange","change",null,n,r),t.push({event:n,listeners:e}))}var bi=null,Oi=null;function yx(t){Nm(t,0)}function Xa(t){var e=hr(t);if(Lf(e))return t}function bx(t,e){if(t==="change")return e}var fm=!1;if(Wt){var No;if(Wt){var _o="oninput"in document;if(!_o){var Vu=document.createElement("div");Vu.setAttribute("oninput","return;"),_o=typeof Vu.oninput=="function"}No=_o}else No=!1;fm=No&&(!document.documentMode||9<document.documentMode)}function Yu(){bi&&(bi.detachEvent("onpropertychange",mm),Oi=bi=null)}function mm(t){if(t.propertyName==="value"&&Xa(Oi)){var e=[];hm(e,Oi,t,bc(t)),Xf(yx,e)}}function wx(t,e,n){t==="focusin"?(Yu(),bi=e,Oi=n,bi.attachEvent("onpropertychange",mm)):t==="focusout"&&Yu()}function Sx(t){if(t==="selectionchange"||t==="keyup"||t==="keydown")return Xa(Oi)}function Nx(t,e){if(t==="click")return Xa(e)}function _x(t,e){if(t==="input"||t==="change")return Xa(e)}function kx(t,e){return t===e&&(t!==0||1/t===1/e)||t!==t&&e!==e}var Nt=typeof Object.is=="function"?Object.is:kx;function Li(t,e){if(Nt(t,e))return!0;if(typeof t!="object"||t===null||typeof e!="object"||e===null)return!1;var n=Object.keys(t),r=Object.keys(e);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var i=n[r];if(!nl.call(e,i)||!Nt(t[i],e[i]))return!1}return!0}function Xu(t){for(;t&&t.firstChild;)t=t.firstChild;return t}function Ku(t,e){var n=Xu(t);t=0;for(var r;n;){if(n.nodeType===3){if(r=t+n.textContent.length,t<=e&&r>=e)return{node:n,offset:e-t};t=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=Xu(n)}}function pm(t,e){return t&&e?t===e?!0:t&&t.nodeType===3?!1:e&&e.nodeType===3?pm(t,e.parentNode):"contains"in t?t.contains(e):t.compareDocumentPosition?!!(t.compareDocumentPosition(e)&16):!1:!1}function gm(){for(var t=window,e=ra();e instanceof t.HTMLIFrameElement;){try{var n=typeof e.contentWindow.location.href=="string"}catch{n=!1}if(n)t=e.contentWindow;else break;e=ra(t.document)}return e}function Ec(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e&&(e==="input"&&(t.type==="text"||t.type==="search"||t.type==="tel"||t.type==="url"||t.type==="password")||e==="textarea"||t.contentEditable==="true")}function jx(t){var e=gm(),n=t.focusedElem,r=t.selectionRange;if(e!==n&&n&&n.ownerDocument&&pm(n.ownerDocument.documentElement,n)){if(r!==null&&Ec(n)){if(e=r.start,t=r.end,t===void 0&&(t=e),"selectionStart"in n)n.selectionStart=e,n.selectionEnd=Math.min(t,n.value.length);else if(t=(e=n.ownerDocument||document)&&e.defaultView||window,t.getSelection){t=t.getSelection();var i=n.textContent.length,s=Math.min(r.start,i);r=r.end===void 0?s:Math.min(r.end,i),!t.extend&&s>r&&(i=r,r=s,s=i),i=Ku(n,s);var a=Ku(n,r);i&&a&&(t.rangeCount!==1||t.anchorNode!==i.node||t.anchorOffset!==i.offset||t.focusNode!==a.node||t.focusOffset!==a.offset)&&(e=e.createRange(),e.setStart(i.node,i.offset),t.removeAllRanges(),s>r?(t.addRange(e),t.extend(a.node,a.offset)):(e.setEnd(a.node,a.offset),t.addRange(e)))}}for(e=[],t=n;t=t.parentNode;)t.nodeType===1&&e.push({element:t,left:t.scrollLeft,top:t.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<e.length;n++)t=e[n],t.element.scrollLeft=t.left,t.element.scrollTop=t.top}}var Cx=Wt&&"documentMode"in document&&11>=document.documentMode,ur=null,wl=null,wi=null,Sl=!1;function Qu(t,e,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Sl||ur==null||ur!==ra(r)||(r=ur,"selectionStart"in r&&Ec(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),wi&&Li(wi,r)||(wi=r,r=ua(wl,"onSelect"),0<r.length&&(e=new jc("onSelect","select",null,e,n),t.push({event:e,listeners:r}),e.target=ur)))}function xs(t,e){var n={};return n[t.toLowerCase()]=e.toLowerCase(),n["Webkit"+t]="webkit"+e,n["Moz"+t]="moz"+e,n}var dr={animationend:xs("Animation","AnimationEnd"),animationiteration:xs("Animation","AnimationIteration"),animationstart:xs("Animation","AnimationStart"),transitionend:xs("Transition","TransitionEnd")},ko={},xm={};Wt&&(xm=document.createElement("div").style,"AnimationEvent"in window||(delete dr.animationend.animation,delete dr.animationiteration.animation,delete dr.animationstart.animation),"TransitionEvent"in window||delete dr.transitionend.transition);function Ka(t){if(ko[t])return ko[t];if(!dr[t])return t;var e=dr[t],n;for(n in e)if(e.hasOwnProperty(n)&&n in xm)return ko[t]=e[n];return t}var vm=Ka("animationend"),ym=Ka("animationiteration"),bm=Ka("animationstart"),wm=Ka("transitionend"),Sm=new Map,Gu="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function _n(t,e){Sm.set(t,e),Zn(e,[t])}for(var jo=0;jo<Gu.length;jo++){var Co=Gu[jo],Px=Co.toLowerCase(),Ex=Co[0].toUpperCase()+Co.slice(1);_n(Px,"on"+Ex)}_n(vm,"onAnimationEnd");_n(ym,"onAnimationIteration");_n(bm,"onAnimationStart");_n("dblclick","onDoubleClick");_n("focusin","onFocus");_n("focusout","onBlur");_n(wm,"onTransitionEnd");Er("onMouseEnter",["mouseout","mouseover"]);Er("onMouseLeave",["mouseout","mouseover"]);Er("onPointerEnter",["pointerout","pointerover"]);Er("onPointerLeave",["pointerout","pointerover"]);Zn("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));Zn("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));Zn("onBeforeInput",["compositionend","keypress","textInput","paste"]);Zn("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));Zn("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));Zn("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var di="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Tx=new Set("cancel close invalid load scroll toggle".split(" ").concat(di));function qu(t,e,n){var r=t.type||"unknown-event";t.currentTarget=n,P0(r,e,void 0,t),t.currentTarget=null}function Nm(t,e){e=(e&4)!==0;for(var n=0;n<t.length;n++){var r=t[n],i=r.event;r=r.listeners;e:{var s=void 0;if(e)for(var a=r.length-1;0<=a;a--){var o=r[a],l=o.instance,u=o.currentTarget;if(o=o.listener,l!==s&&i.isPropagationStopped())break e;qu(i,o,u),s=l}else for(a=0;a<r.length;a++){if(o=r[a],l=o.instance,u=o.currentTarget,o=o.listener,l!==s&&i.isPropagationStopped())break e;qu(i,o,u),s=l}}}if(sa)throw t=xl,sa=!1,xl=null,t}function J(t,e){var n=e[Cl];n===void 0&&(n=e[Cl]=new Set);var r=t+"__bubble";n.has(r)||(_m(e,t,2,!1),n.add(r))}function Po(t,e,n){var r=0;e&&(r|=4),_m(n,t,r,e)}var vs="_reactListening"+Math.random().toString(36).slice(2);function Ai(t){if(!t[vs]){t[vs]=!0,Mf.forEach(function(n){n!=="selectionchange"&&(Tx.has(n)||Po(n,!1,t),Po(n,!0,t))});var e=t.nodeType===9?t:t.ownerDocument;e===null||e[vs]||(e[vs]=!0,Po("selectionchange",!1,e))}}function _m(t,e,n,r){switch(om(e)){case 1:var i=W0;break;case 4:i=V0;break;default:i=_c}n=i.bind(null,e,n,t),i=void 0,!gl||e!=="touchstart"&&e!=="touchmove"&&e!=="wheel"||(i=!0),r?i!==void 0?t.addEventListener(e,n,{capture:!0,passive:i}):t.addEventListener(e,n,!0):i!==void 0?t.addEventListener(e,n,{passive:i}):t.addEventListener(e,n,!1)}function Eo(t,e,n,r,i){var s=r;if(!(e&1)&&!(e&2)&&r!==null)e:for(;;){if(r===null)return;var a=r.tag;if(a===3||a===4){var o=r.stateNode.containerInfo;if(o===i||o.nodeType===8&&o.parentNode===i)break;if(a===4)for(a=r.return;a!==null;){var l=a.tag;if((l===3||l===4)&&(l=a.stateNode.containerInfo,l===i||l.nodeType===8&&l.parentNode===i))return;a=a.return}for(;o!==null;){if(a=An(o),a===null)return;if(l=a.tag,l===5||l===6){r=s=a;continue e}o=o.parentNode}}r=r.return}Xf(function(){var u=s,d=bc(n),h=[];e:{var f=Sm.get(t);if(f!==void 0){var m=jc,x=t;switch(t){case"keypress":if(Ws(n)===0)break e;case"keydown":case"keyup":m=ax;break;case"focusin":x="focus",m=So;break;case"focusout":x="blur",m=So;break;case"beforeblur":case"afterblur":m=So;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":m=zu;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":m=K0;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":m=cx;break;case vm:case ym:case bm:m=q0;break;case wm:m=dx;break;case"scroll":m=Y0;break;case"wheel":m=fx;break;case"copy":case"cut":case"paste":m=J0;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":m=Uu}var v=(e&4)!==0,w=!v&&t==="scroll",g=v?f!==null?f+"Capture":null:f;v=[];for(var p=u,y;p!==null;){y=p;var b=y.stateNode;if(y.tag===5&&b!==null&&(y=b,g!==null&&(b=Mi(p,g),b!=null&&v.push(Fi(p,b,y)))),w)break;p=p.return}0<v.length&&(f=new m(f,x,null,n,d),h.push({event:f,listeners:v}))}}if(!(e&7)){e:{if(f=t==="mouseover"||t==="pointerover",m=t==="mouseout"||t==="pointerout",f&&n!==ml&&(x=n.relatedTarget||n.fromElement)&&(An(x)||x[Vt]))break e;if((m||f)&&(f=d.window===d?d:(f=d.ownerDocument)?f.defaultView||f.parentWindow:window,m?(x=n.relatedTarget||n.toElement,m=u,x=x?An(x):null,x!==null&&(w=Jn(x),x!==w||x.tag!==5&&x.tag!==6)&&(x=null)):(m=null,x=u),m!==x)){if(v=zu,b="onMouseLeave",g="onMouseEnter",p="mouse",(t==="pointerout"||t==="pointerover")&&(v=Uu,b="onPointerLeave",g="onPointerEnter",p="pointer"),w=m==null?f:hr(m),y=x==null?f:hr(x),f=new v(b,p+"leave",m,n,d),f.target=w,f.relatedTarget=y,b=null,An(d)===u&&(v=new v(g,p+"enter",x,n,d),v.target=y,v.relatedTarget=w,b=v),w=b,m&&x)t:{for(v=m,g=x,p=0,y=v;y;y=nr(y))p++;for(y=0,b=g;b;b=nr(b))y++;for(;0<p-y;)v=nr(v),p--;for(;0<y-p;)g=nr(g),y--;for(;p--;){if(v===g||g!==null&&v===g.alternate)break t;v=nr(v),g=nr(g)}v=null}else v=null;m!==null&&Zu(h,f,m,v,!1),x!==null&&w!==null&&Zu(h,w,x,v,!0)}}e:{if(f=u?hr(u):window,m=f.nodeName&&f.nodeName.toLowerCase(),m==="select"||m==="input"&&f.type==="file")var S=bx;else if(Wu(f))if(fm)S=_x;else{S=Sx;var j=wx}else(m=f.nodeName)&&m.toLowerCase()==="input"&&(f.type==="checkbox"||f.type==="radio")&&(S=Nx);if(S&&(S=S(t,u))){hm(h,S,n,d);break e}j&&j(t,f,u),t==="focusout"&&(j=f._wrapperState)&&j.controlled&&f.type==="number"&&cl(f,"number",f.value)}switch(j=u?hr(u):window,t){case"focusin":(Wu(j)||j.contentEditable==="true")&&(ur=j,wl=u,wi=null);break;case"focusout":wi=wl=ur=null;break;case"mousedown":Sl=!0;break;case"contextmenu":case"mouseup":case"dragend":Sl=!1,Qu(h,n,d);break;case"selectionchange":if(Cx)break;case"keydown":case"keyup":Qu(h,n,d)}var N;if(Pc)e:{switch(t){case"compositionstart":var k="onCompositionStart";break e;case"compositionend":k="onCompositionEnd";break e;case"compositionupdate":k="onCompositionUpdate";break e}k=void 0}else cr?um(t,n)&&(k="onCompositionEnd"):t==="keydown"&&n.keyCode===229&&(k="onCompositionStart");k&&(cm&&n.locale!=="ko"&&(cr||k!=="onCompositionStart"?k==="onCompositionEnd"&&cr&&(N=lm()):(rn=d,kc="value"in rn?rn.value:rn.textContent,cr=!0)),j=ua(u,k),0<j.length&&(k=new Iu(k,t,null,n,d),h.push({event:k,listeners:j}),N?k.data=N:(N=dm(n),N!==null&&(k.data=N)))),(N=px?gx(t,n):xx(t,n))&&(u=ua(u,"onBeforeInput"),0<u.length&&(d=new Iu("onBeforeInput","beforeinput",null,n,d),h.push({event:d,listeners:u}),d.data=N))}Nm(h,e)})}function Fi(t,e,n){return{instance:t,listener:e,currentTarget:n}}function ua(t,e){for(var n=e+"Capture",r=[];t!==null;){var i=t,s=i.stateNode;i.tag===5&&s!==null&&(i=s,s=Mi(t,n),s!=null&&r.unshift(Fi(t,s,i)),s=Mi(t,e),s!=null&&r.push(Fi(t,s,i))),t=t.return}return r}function nr(t){if(t===null)return null;do t=t.return;while(t&&t.tag!==5);return t||null}function Zu(t,e,n,r,i){for(var s=e._reactName,a=[];n!==null&&n!==r;){var o=n,l=o.alternate,u=o.stateNode;if(l!==null&&l===r)break;o.tag===5&&u!==null&&(o=u,i?(l=Mi(n,s),l!=null&&a.unshift(Fi(n,l,o))):i||(l=Mi(n,s),l!=null&&a.push(Fi(n,l,o)))),n=n.return}a.length!==0&&t.push({event:e,listeners:a})}var Mx=/\r\n?/g,$x=/\u0000|\uFFFD/g;function Ju(t){return(typeof t=="string"?t:""+t).replace(Mx,`
`).replace($x,"")}function ys(t,e,n){if(e=Ju(e),Ju(t)!==e&&n)throw Error(E(425))}function da(){}var Nl=null,_l=null;function kl(t,e){return t==="textarea"||t==="noscript"||typeof e.children=="string"||typeof e.children=="number"||typeof e.dangerouslySetInnerHTML=="object"&&e.dangerouslySetInnerHTML!==null&&e.dangerouslySetInnerHTML.__html!=null}var jl=typeof setTimeout=="function"?setTimeout:void 0,Rx=typeof clearTimeout=="function"?clearTimeout:void 0,ed=typeof Promise=="function"?Promise:void 0,Dx=typeof queueMicrotask=="function"?queueMicrotask:typeof ed<"u"?function(t){return ed.resolve(null).then(t).catch(Ox)}:jl;function Ox(t){setTimeout(function(){throw t})}function To(t,e){var n=e,r=0;do{var i=n.nextSibling;if(t.removeChild(n),i&&i.nodeType===8)if(n=i.data,n==="/$"){if(r===0){t.removeChild(i),Di(e);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=i}while(n);Di(e)}function hn(t){for(;t!=null;t=t.nextSibling){var e=t.nodeType;if(e===1||e===3)break;if(e===8){if(e=t.data,e==="$"||e==="$!"||e==="$?")break;if(e==="/$")return null}}return t}function td(t){t=t.previousSibling;for(var e=0;t;){if(t.nodeType===8){var n=t.data;if(n==="$"||n==="$!"||n==="$?"){if(e===0)return t;e--}else n==="/$"&&e++}t=t.previousSibling}return null}var Br=Math.random().toString(36).slice(2),Et="__reactFiber$"+Br,zi="__reactProps$"+Br,Vt="__reactContainer$"+Br,Cl="__reactEvents$"+Br,Lx="__reactListeners$"+Br,Ax="__reactHandles$"+Br;function An(t){var e=t[Et];if(e)return e;for(var n=t.parentNode;n;){if(e=n[Vt]||n[Et]){if(n=e.alternate,e.child!==null||n!==null&&n.child!==null)for(t=td(t);t!==null;){if(n=t[Et])return n;t=td(t)}return e}t=n,n=t.parentNode}return null}function rs(t){return t=t[Et]||t[Vt],!t||t.tag!==5&&t.tag!==6&&t.tag!==13&&t.tag!==3?null:t}function hr(t){if(t.tag===5||t.tag===6)return t.stateNode;throw Error(E(33))}function Qa(t){return t[zi]||null}var Pl=[],fr=-1;function kn(t){return{current:t}}function te(t){0>fr||(t.current=Pl[fr],Pl[fr]=null,fr--)}function Z(t,e){fr++,Pl[fr]=t.current,t.current=e}var Sn={},$e=kn(Sn),Ve=kn(!1),Wn=Sn;function Tr(t,e){var n=t.type.contextTypes;if(!n)return Sn;var r=t.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===e)return r.__reactInternalMemoizedMaskedChildContext;var i={},s;for(s in n)i[s]=e[s];return r&&(t=t.stateNode,t.__reactInternalMemoizedUnmaskedChildContext=e,t.__reactInternalMemoizedMaskedChildContext=i),i}function Ye(t){return t=t.childContextTypes,t!=null}function ha(){te(Ve),te($e)}function nd(t,e,n){if($e.current!==Sn)throw Error(E(168));Z($e,e),Z(Ve,n)}function km(t,e,n){var r=t.stateNode;if(e=e.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var i in r)if(!(i in e))throw Error(E(108,w0(t)||"Unknown",i));return ae({},n,r)}function fa(t){return t=(t=t.stateNode)&&t.__reactInternalMemoizedMergedChildContext||Sn,Wn=$e.current,Z($e,t),Z(Ve,Ve.current),!0}function rd(t,e,n){var r=t.stateNode;if(!r)throw Error(E(169));n?(t=km(t,e,Wn),r.__reactInternalMemoizedMergedChildContext=t,te(Ve),te($e),Z($e,t)):te(Ve),Z(Ve,n)}var Ft=null,Ga=!1,Mo=!1;function jm(t){Ft===null?Ft=[t]:Ft.push(t)}function Fx(t){Ga=!0,jm(t)}function jn(){if(!Mo&&Ft!==null){Mo=!0;var t=0,e=G;try{var n=Ft;for(G=1;t<n.length;t++){var r=n[t];do r=r(!0);while(r!==null)}Ft=null,Ga=!1}catch(i){throw Ft!==null&&(Ft=Ft.slice(t+1)),qf(wc,jn),i}finally{G=e,Mo=!1}}return null}var mr=[],pr=0,ma=null,pa=0,nt=[],rt=0,Vn=null,It=1,Ut="";function $n(t,e){mr[pr++]=pa,mr[pr++]=ma,ma=t,pa=e}function Cm(t,e,n){nt[rt++]=It,nt[rt++]=Ut,nt[rt++]=Vn,Vn=t;var r=It;t=Ut;var i=32-wt(r)-1;r&=~(1<<i),n+=1;var s=32-wt(e)+i;if(30<s){var a=i-i%5;s=(r&(1<<a)-1).toString(32),r>>=a,i-=a,It=1<<32-wt(e)+i|n<<i|r,Ut=s+t}else It=1<<s|n<<i|r,Ut=t}function Tc(t){t.return!==null&&($n(t,1),Cm(t,1,0))}function Mc(t){for(;t===ma;)ma=mr[--pr],mr[pr]=null,pa=mr[--pr],mr[pr]=null;for(;t===Vn;)Vn=nt[--rt],nt[rt]=null,Ut=nt[--rt],nt[rt]=null,It=nt[--rt],nt[rt]=null}var qe=null,Ge=null,ne=!1,gt=null;function Pm(t,e){var n=it(5,null,null,0);n.elementType="DELETED",n.stateNode=e,n.return=t,e=t.deletions,e===null?(t.deletions=[n],t.flags|=16):e.push(n)}function id(t,e){switch(t.tag){case 5:var n=t.type;return e=e.nodeType!==1||n.toLowerCase()!==e.nodeName.toLowerCase()?null:e,e!==null?(t.stateNode=e,qe=t,Ge=hn(e.firstChild),!0):!1;case 6:return e=t.pendingProps===""||e.nodeType!==3?null:e,e!==null?(t.stateNode=e,qe=t,Ge=null,!0):!1;case 13:return e=e.nodeType!==8?null:e,e!==null?(n=Vn!==null?{id:It,overflow:Ut}:null,t.memoizedState={dehydrated:e,treeContext:n,retryLane:1073741824},n=it(18,null,null,0),n.stateNode=e,n.return=t,t.child=n,qe=t,Ge=null,!0):!1;default:return!1}}function El(t){return(t.mode&1)!==0&&(t.flags&128)===0}function Tl(t){if(ne){var e=Ge;if(e){var n=e;if(!id(t,e)){if(El(t))throw Error(E(418));e=hn(n.nextSibling);var r=qe;e&&id(t,e)?Pm(r,n):(t.flags=t.flags&-4097|2,ne=!1,qe=t)}}else{if(El(t))throw Error(E(418));t.flags=t.flags&-4097|2,ne=!1,qe=t}}}function sd(t){for(t=t.return;t!==null&&t.tag!==5&&t.tag!==3&&t.tag!==13;)t=t.return;qe=t}function bs(t){if(t!==qe)return!1;if(!ne)return sd(t),ne=!0,!1;var e;if((e=t.tag!==3)&&!(e=t.tag!==5)&&(e=t.type,e=e!=="head"&&e!=="body"&&!kl(t.type,t.memoizedProps)),e&&(e=Ge)){if(El(t))throw Em(),Error(E(418));for(;e;)Pm(t,e),e=hn(e.nextSibling)}if(sd(t),t.tag===13){if(t=t.memoizedState,t=t!==null?t.dehydrated:null,!t)throw Error(E(317));e:{for(t=t.nextSibling,e=0;t;){if(t.nodeType===8){var n=t.data;if(n==="/$"){if(e===0){Ge=hn(t.nextSibling);break e}e--}else n!=="$"&&n!=="$!"&&n!=="$?"||e++}t=t.nextSibling}Ge=null}}else Ge=qe?hn(t.stateNode.nextSibling):null;return!0}function Em(){for(var t=Ge;t;)t=hn(t.nextSibling)}function Mr(){Ge=qe=null,ne=!1}function $c(t){gt===null?gt=[t]:gt.push(t)}var zx=Qt.ReactCurrentBatchConfig;function Zr(t,e,n){if(t=n.ref,t!==null&&typeof t!="function"&&typeof t!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(E(309));var r=n.stateNode}if(!r)throw Error(E(147,t));var i=r,s=""+t;return e!==null&&e.ref!==null&&typeof e.ref=="function"&&e.ref._stringRef===s?e.ref:(e=function(a){var o=i.refs;a===null?delete o[s]:o[s]=a},e._stringRef=s,e)}if(typeof t!="string")throw Error(E(284));if(!n._owner)throw Error(E(290,t))}return t}function ws(t,e){throw t=Object.prototype.toString.call(e),Error(E(31,t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t))}function ad(t){var e=t._init;return e(t._payload)}function Tm(t){function e(g,p){if(t){var y=g.deletions;y===null?(g.deletions=[p],g.flags|=16):y.push(p)}}function n(g,p){if(!t)return null;for(;p!==null;)e(g,p),p=p.sibling;return null}function r(g,p){for(g=new Map;p!==null;)p.key!==null?g.set(p.key,p):g.set(p.index,p),p=p.sibling;return g}function i(g,p){return g=gn(g,p),g.index=0,g.sibling=null,g}function s(g,p,y){return g.index=y,t?(y=g.alternate,y!==null?(y=y.index,y<p?(g.flags|=2,p):y):(g.flags|=2,p)):(g.flags|=1048576,p)}function a(g){return t&&g.alternate===null&&(g.flags|=2),g}function o(g,p,y,b){return p===null||p.tag!==6?(p=Fo(y,g.mode,b),p.return=g,p):(p=i(p,y),p.return=g,p)}function l(g,p,y,b){var S=y.type;return S===lr?d(g,p,y.props.children,b,y.key):p!==null&&(p.elementType===S||typeof S=="object"&&S!==null&&S.$$typeof===Jt&&ad(S)===p.type)?(b=i(p,y.props),b.ref=Zr(g,p,y),b.return=g,b):(b=qs(y.type,y.key,y.props,null,g.mode,b),b.ref=Zr(g,p,y),b.return=g,b)}function u(g,p,y,b){return p===null||p.tag!==4||p.stateNode.containerInfo!==y.containerInfo||p.stateNode.implementation!==y.implementation?(p=zo(y,g.mode,b),p.return=g,p):(p=i(p,y.children||[]),p.return=g,p)}function d(g,p,y,b,S){return p===null||p.tag!==7?(p=Bn(y,g.mode,b,S),p.return=g,p):(p=i(p,y),p.return=g,p)}function h(g,p,y){if(typeof p=="string"&&p!==""||typeof p=="number")return p=Fo(""+p,g.mode,y),p.return=g,p;if(typeof p=="object"&&p!==null){switch(p.$$typeof){case us:return y=qs(p.type,p.key,p.props,null,g.mode,y),y.ref=Zr(g,null,p),y.return=g,y;case or:return p=zo(p,g.mode,y),p.return=g,p;case Jt:var b=p._init;return h(g,b(p._payload),y)}if(ci(p)||Xr(p))return p=Bn(p,g.mode,y,null),p.return=g,p;ws(g,p)}return null}function f(g,p,y,b){var S=p!==null?p.key:null;if(typeof y=="string"&&y!==""||typeof y=="number")return S!==null?null:o(g,p,""+y,b);if(typeof y=="object"&&y!==null){switch(y.$$typeof){case us:return y.key===S?l(g,p,y,b):null;case or:return y.key===S?u(g,p,y,b):null;case Jt:return S=y._init,f(g,p,S(y._payload),b)}if(ci(y)||Xr(y))return S!==null?null:d(g,p,y,b,null);ws(g,y)}return null}function m(g,p,y,b,S){if(typeof b=="string"&&b!==""||typeof b=="number")return g=g.get(y)||null,o(p,g,""+b,S);if(typeof b=="object"&&b!==null){switch(b.$$typeof){case us:return g=g.get(b.key===null?y:b.key)||null,l(p,g,b,S);case or:return g=g.get(b.key===null?y:b.key)||null,u(p,g,b,S);case Jt:var j=b._init;return m(g,p,y,j(b._payload),S)}if(ci(b)||Xr(b))return g=g.get(y)||null,d(p,g,b,S,null);ws(p,b)}return null}function x(g,p,y,b){for(var S=null,j=null,N=p,k=p=0,C=null;N!==null&&k<y.length;k++){N.index>k?(C=N,N=null):C=N.sibling;var P=f(g,N,y[k],b);if(P===null){N===null&&(N=C);break}t&&N&&P.alternate===null&&e(g,N),p=s(P,p,k),j===null?S=P:j.sibling=P,j=P,N=C}if(k===y.length)return n(g,N),ne&&$n(g,k),S;if(N===null){for(;k<y.length;k++)N=h(g,y[k],b),N!==null&&(p=s(N,p,k),j===null?S=N:j.sibling=N,j=N);return ne&&$n(g,k),S}for(N=r(g,N);k<y.length;k++)C=m(N,g,k,y[k],b),C!==null&&(t&&C.alternate!==null&&N.delete(C.key===null?k:C.key),p=s(C,p,k),j===null?S=C:j.sibling=C,j=C);return t&&N.forEach(function(M){return e(g,M)}),ne&&$n(g,k),S}function v(g,p,y,b){var S=Xr(y);if(typeof S!="function")throw Error(E(150));if(y=S.call(y),y==null)throw Error(E(151));for(var j=S=null,N=p,k=p=0,C=null,P=y.next();N!==null&&!P.done;k++,P=y.next()){N.index>k?(C=N,N=null):C=N.sibling;var M=f(g,N,P.value,b);if(M===null){N===null&&(N=C);break}t&&N&&M.alternate===null&&e(g,N),p=s(M,p,k),j===null?S=M:j.sibling=M,j=M,N=C}if(P.done)return n(g,N),ne&&$n(g,k),S;if(N===null){for(;!P.done;k++,P=y.next())P=h(g,P.value,b),P!==null&&(p=s(P,p,k),j===null?S=P:j.sibling=P,j=P);return ne&&$n(g,k),S}for(N=r(g,N);!P.done;k++,P=y.next())P=m(N,g,k,P.value,b),P!==null&&(t&&P.alternate!==null&&N.delete(P.key===null?k:P.key),p=s(P,p,k),j===null?S=P:j.sibling=P,j=P);return t&&N.forEach(function(D){return e(g,D)}),ne&&$n(g,k),S}function w(g,p,y,b){if(typeof y=="object"&&y!==null&&y.type===lr&&y.key===null&&(y=y.props.children),typeof y=="object"&&y!==null){switch(y.$$typeof){case us:e:{for(var S=y.key,j=p;j!==null;){if(j.key===S){if(S=y.type,S===lr){if(j.tag===7){n(g,j.sibling),p=i(j,y.props.children),p.return=g,g=p;break e}}else if(j.elementType===S||typeof S=="object"&&S!==null&&S.$$typeof===Jt&&ad(S)===j.type){n(g,j.sibling),p=i(j,y.props),p.ref=Zr(g,j,y),p.return=g,g=p;break e}n(g,j);break}else e(g,j);j=j.sibling}y.type===lr?(p=Bn(y.props.children,g.mode,b,y.key),p.return=g,g=p):(b=qs(y.type,y.key,y.props,null,g.mode,b),b.ref=Zr(g,p,y),b.return=g,g=b)}return a(g);case or:e:{for(j=y.key;p!==null;){if(p.key===j)if(p.tag===4&&p.stateNode.containerInfo===y.containerInfo&&p.stateNode.implementation===y.implementation){n(g,p.sibling),p=i(p,y.children||[]),p.return=g,g=p;break e}else{n(g,p);break}else e(g,p);p=p.sibling}p=zo(y,g.mode,b),p.return=g,g=p}return a(g);case Jt:return j=y._init,w(g,p,j(y._payload),b)}if(ci(y))return x(g,p,y,b);if(Xr(y))return v(g,p,y,b);ws(g,y)}return typeof y=="string"&&y!==""||typeof y=="number"?(y=""+y,p!==null&&p.tag===6?(n(g,p.sibling),p=i(p,y),p.return=g,g=p):(n(g,p),p=Fo(y,g.mode,b),p.return=g,g=p),a(g)):n(g,p)}return w}var $r=Tm(!0),Mm=Tm(!1),ga=kn(null),xa=null,gr=null,Rc=null;function Dc(){Rc=gr=xa=null}function Oc(t){var e=ga.current;te(ga),t._currentValue=e}function Ml(t,e,n){for(;t!==null;){var r=t.alternate;if((t.childLanes&e)!==e?(t.childLanes|=e,r!==null&&(r.childLanes|=e)):r!==null&&(r.childLanes&e)!==e&&(r.childLanes|=e),t===n)break;t=t.return}}function Nr(t,e){xa=t,Rc=gr=null,t=t.dependencies,t!==null&&t.firstContext!==null&&(t.lanes&e&&(He=!0),t.firstContext=null)}function ot(t){var e=t._currentValue;if(Rc!==t)if(t={context:t,memoizedValue:e,next:null},gr===null){if(xa===null)throw Error(E(308));gr=t,xa.dependencies={lanes:0,firstContext:t}}else gr=gr.next=t;return e}var Fn=null;function Lc(t){Fn===null?Fn=[t]:Fn.push(t)}function $m(t,e,n,r){var i=e.interleaved;return i===null?(n.next=n,Lc(e)):(n.next=i.next,i.next=n),e.interleaved=n,Yt(t,r)}function Yt(t,e){t.lanes|=e;var n=t.alternate;for(n!==null&&(n.lanes|=e),n=t,t=t.return;t!==null;)t.childLanes|=e,n=t.alternate,n!==null&&(n.childLanes|=e),n=t,t=t.return;return n.tag===3?n.stateNode:null}var en=!1;function Ac(t){t.updateQueue={baseState:t.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Rm(t,e){t=t.updateQueue,e.updateQueue===t&&(e.updateQueue={baseState:t.baseState,firstBaseUpdate:t.firstBaseUpdate,lastBaseUpdate:t.lastBaseUpdate,shared:t.shared,effects:t.effects})}function Ht(t,e){return{eventTime:t,lane:e,tag:0,payload:null,callback:null,next:null}}function fn(t,e,n){var r=t.updateQueue;if(r===null)return null;if(r=r.shared,W&2){var i=r.pending;return i===null?e.next=e:(e.next=i.next,i.next=e),r.pending=e,Yt(t,n)}return i=r.interleaved,i===null?(e.next=e,Lc(r)):(e.next=i.next,i.next=e),r.interleaved=e,Yt(t,n)}function Vs(t,e,n){if(e=e.updateQueue,e!==null&&(e=e.shared,(n&4194240)!==0)){var r=e.lanes;r&=t.pendingLanes,n|=r,e.lanes=n,Sc(t,n)}}function od(t,e){var n=t.updateQueue,r=t.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var i=null,s=null;if(n=n.firstBaseUpdate,n!==null){do{var a={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};s===null?i=s=a:s=s.next=a,n=n.next}while(n!==null);s===null?i=s=e:s=s.next=e}else i=s=e;n={baseState:r.baseState,firstBaseUpdate:i,lastBaseUpdate:s,shared:r.shared,effects:r.effects},t.updateQueue=n;return}t=n.lastBaseUpdate,t===null?n.firstBaseUpdate=e:t.next=e,n.lastBaseUpdate=e}function va(t,e,n,r){var i=t.updateQueue;en=!1;var s=i.firstBaseUpdate,a=i.lastBaseUpdate,o=i.shared.pending;if(o!==null){i.shared.pending=null;var l=o,u=l.next;l.next=null,a===null?s=u:a.next=u,a=l;var d=t.alternate;d!==null&&(d=d.updateQueue,o=d.lastBaseUpdate,o!==a&&(o===null?d.firstBaseUpdate=u:o.next=u,d.lastBaseUpdate=l))}if(s!==null){var h=i.baseState;a=0,d=u=l=null,o=s;do{var f=o.lane,m=o.eventTime;if((r&f)===f){d!==null&&(d=d.next={eventTime:m,lane:0,tag:o.tag,payload:o.payload,callback:o.callback,next:null});e:{var x=t,v=o;switch(f=e,m=n,v.tag){case 1:if(x=v.payload,typeof x=="function"){h=x.call(m,h,f);break e}h=x;break e;case 3:x.flags=x.flags&-65537|128;case 0:if(x=v.payload,f=typeof x=="function"?x.call(m,h,f):x,f==null)break e;h=ae({},h,f);break e;case 2:en=!0}}o.callback!==null&&o.lane!==0&&(t.flags|=64,f=i.effects,f===null?i.effects=[o]:f.push(o))}else m={eventTime:m,lane:f,tag:o.tag,payload:o.payload,callback:o.callback,next:null},d===null?(u=d=m,l=h):d=d.next=m,a|=f;if(o=o.next,o===null){if(o=i.shared.pending,o===null)break;f=o,o=f.next,f.next=null,i.lastBaseUpdate=f,i.shared.pending=null}}while(!0);if(d===null&&(l=h),i.baseState=l,i.firstBaseUpdate=u,i.lastBaseUpdate=d,e=i.shared.interleaved,e!==null){i=e;do a|=i.lane,i=i.next;while(i!==e)}else s===null&&(i.shared.lanes=0);Xn|=a,t.lanes=a,t.memoizedState=h}}function ld(t,e,n){if(t=e.effects,e.effects=null,t!==null)for(e=0;e<t.length;e++){var r=t[e],i=r.callback;if(i!==null){if(r.callback=null,r=n,typeof i!="function")throw Error(E(191,i));i.call(r)}}}var is={},Mt=kn(is),Ii=kn(is),Ui=kn(is);function zn(t){if(t===is)throw Error(E(174));return t}function Fc(t,e){switch(Z(Ui,e),Z(Ii,t),Z(Mt,is),t=e.nodeType,t){case 9:case 11:e=(e=e.documentElement)?e.namespaceURI:dl(null,"");break;default:t=t===8?e.parentNode:e,e=t.namespaceURI||null,t=t.tagName,e=dl(e,t)}te(Mt),Z(Mt,e)}function Rr(){te(Mt),te(Ii),te(Ui)}function Dm(t){zn(Ui.current);var e=zn(Mt.current),n=dl(e,t.type);e!==n&&(Z(Ii,t),Z(Mt,n))}function zc(t){Ii.current===t&&(te(Mt),te(Ii))}var ie=kn(0);function ya(t){for(var e=t;e!==null;){if(e.tag===13){var n=e.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return e}else if(e.tag===19&&e.memoizedProps.revealOrder!==void 0){if(e.flags&128)return e}else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return null;e=e.return}e.sibling.return=e.return,e=e.sibling}return null}var $o=[];function Ic(){for(var t=0;t<$o.length;t++)$o[t]._workInProgressVersionPrimary=null;$o.length=0}var Ys=Qt.ReactCurrentDispatcher,Ro=Qt.ReactCurrentBatchConfig,Yn=0,se=null,ge=null,be=null,ba=!1,Si=!1,Bi=0,Ix=0;function je(){throw Error(E(321))}function Uc(t,e){if(e===null)return!1;for(var n=0;n<e.length&&n<t.length;n++)if(!Nt(t[n],e[n]))return!1;return!0}function Bc(t,e,n,r,i,s){if(Yn=s,se=e,e.memoizedState=null,e.updateQueue=null,e.lanes=0,Ys.current=t===null||t.memoizedState===null?Wx:Vx,t=n(r,i),Si){s=0;do{if(Si=!1,Bi=0,25<=s)throw Error(E(301));s+=1,be=ge=null,e.updateQueue=null,Ys.current=Yx,t=n(r,i)}while(Si)}if(Ys.current=wa,e=ge!==null&&ge.next!==null,Yn=0,be=ge=se=null,ba=!1,e)throw Error(E(300));return t}function Hc(){var t=Bi!==0;return Bi=0,t}function Ct(){var t={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return be===null?se.memoizedState=be=t:be=be.next=t,be}function lt(){if(ge===null){var t=se.alternate;t=t!==null?t.memoizedState:null}else t=ge.next;var e=be===null?se.memoizedState:be.next;if(e!==null)be=e,ge=t;else{if(t===null)throw Error(E(310));ge=t,t={memoizedState:ge.memoizedState,baseState:ge.baseState,baseQueue:ge.baseQueue,queue:ge.queue,next:null},be===null?se.memoizedState=be=t:be=be.next=t}return be}function Hi(t,e){return typeof e=="function"?e(t):e}function Do(t){var e=lt(),n=e.queue;if(n===null)throw Error(E(311));n.lastRenderedReducer=t;var r=ge,i=r.baseQueue,s=n.pending;if(s!==null){if(i!==null){var a=i.next;i.next=s.next,s.next=a}r.baseQueue=i=s,n.pending=null}if(i!==null){s=i.next,r=r.baseState;var o=a=null,l=null,u=s;do{var d=u.lane;if((Yn&d)===d)l!==null&&(l=l.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),r=u.hasEagerState?u.eagerState:t(r,u.action);else{var h={lane:d,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};l===null?(o=l=h,a=r):l=l.next=h,se.lanes|=d,Xn|=d}u=u.next}while(u!==null&&u!==s);l===null?a=r:l.next=o,Nt(r,e.memoizedState)||(He=!0),e.memoizedState=r,e.baseState=a,e.baseQueue=l,n.lastRenderedState=r}if(t=n.interleaved,t!==null){i=t;do s=i.lane,se.lanes|=s,Xn|=s,i=i.next;while(i!==t)}else i===null&&(n.lanes=0);return[e.memoizedState,n.dispatch]}function Oo(t){var e=lt(),n=e.queue;if(n===null)throw Error(E(311));n.lastRenderedReducer=t;var r=n.dispatch,i=n.pending,s=e.memoizedState;if(i!==null){n.pending=null;var a=i=i.next;do s=t(s,a.action),a=a.next;while(a!==i);Nt(s,e.memoizedState)||(He=!0),e.memoizedState=s,e.baseQueue===null&&(e.baseState=s),n.lastRenderedState=s}return[s,r]}function Om(){}function Lm(t,e){var n=se,r=lt(),i=e(),s=!Nt(r.memoizedState,i);if(s&&(r.memoizedState=i,He=!0),r=r.queue,Wc(zm.bind(null,n,r,t),[t]),r.getSnapshot!==e||s||be!==null&&be.memoizedState.tag&1){if(n.flags|=2048,Wi(9,Fm.bind(null,n,r,i,e),void 0,null),we===null)throw Error(E(349));Yn&30||Am(n,e,i)}return i}function Am(t,e,n){t.flags|=16384,t={getSnapshot:e,value:n},e=se.updateQueue,e===null?(e={lastEffect:null,stores:null},se.updateQueue=e,e.stores=[t]):(n=e.stores,n===null?e.stores=[t]:n.push(t))}function Fm(t,e,n,r){e.value=n,e.getSnapshot=r,Im(e)&&Um(t)}function zm(t,e,n){return n(function(){Im(e)&&Um(t)})}function Im(t){var e=t.getSnapshot;t=t.value;try{var n=e();return!Nt(t,n)}catch{return!0}}function Um(t){var e=Yt(t,1);e!==null&&St(e,t,1,-1)}function cd(t){var e=Ct();return typeof t=="function"&&(t=t()),e.memoizedState=e.baseState=t,t={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:Hi,lastRenderedState:t},e.queue=t,t=t.dispatch=Hx.bind(null,se,t),[e.memoizedState,t]}function Wi(t,e,n,r){return t={tag:t,create:e,destroy:n,deps:r,next:null},e=se.updateQueue,e===null?(e={lastEffect:null,stores:null},se.updateQueue=e,e.lastEffect=t.next=t):(n=e.lastEffect,n===null?e.lastEffect=t.next=t:(r=n.next,n.next=t,t.next=r,e.lastEffect=t)),t}function Bm(){return lt().memoizedState}function Xs(t,e,n,r){var i=Ct();se.flags|=t,i.memoizedState=Wi(1|e,n,void 0,r===void 0?null:r)}function qa(t,e,n,r){var i=lt();r=r===void 0?null:r;var s=void 0;if(ge!==null){var a=ge.memoizedState;if(s=a.destroy,r!==null&&Uc(r,a.deps)){i.memoizedState=Wi(e,n,s,r);return}}se.flags|=t,i.memoizedState=Wi(1|e,n,s,r)}function ud(t,e){return Xs(8390656,8,t,e)}function Wc(t,e){return qa(2048,8,t,e)}function Hm(t,e){return qa(4,2,t,e)}function Wm(t,e){return qa(4,4,t,e)}function Vm(t,e){if(typeof e=="function")return t=t(),e(t),function(){e(null)};if(e!=null)return t=t(),e.current=t,function(){e.current=null}}function Ym(t,e,n){return n=n!=null?n.concat([t]):null,qa(4,4,Vm.bind(null,e,t),n)}function Vc(){}function Xm(t,e){var n=lt();e=e===void 0?null:e;var r=n.memoizedState;return r!==null&&e!==null&&Uc(e,r[1])?r[0]:(n.memoizedState=[t,e],t)}function Km(t,e){var n=lt();e=e===void 0?null:e;var r=n.memoizedState;return r!==null&&e!==null&&Uc(e,r[1])?r[0]:(t=t(),n.memoizedState=[t,e],t)}function Qm(t,e,n){return Yn&21?(Nt(n,e)||(n=em(),se.lanes|=n,Xn|=n,t.baseState=!0),e):(t.baseState&&(t.baseState=!1,He=!0),t.memoizedState=n)}function Ux(t,e){var n=G;G=n!==0&&4>n?n:4,t(!0);var r=Ro.transition;Ro.transition={};try{t(!1),e()}finally{G=n,Ro.transition=r}}function Gm(){return lt().memoizedState}function Bx(t,e,n){var r=pn(t);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},qm(t))Zm(e,n);else if(n=$m(t,e,n,r),n!==null){var i=Oe();St(n,t,r,i),Jm(n,e,r)}}function Hx(t,e,n){var r=pn(t),i={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(qm(t))Zm(e,i);else{var s=t.alternate;if(t.lanes===0&&(s===null||s.lanes===0)&&(s=e.lastRenderedReducer,s!==null))try{var a=e.lastRenderedState,o=s(a,n);if(i.hasEagerState=!0,i.eagerState=o,Nt(o,a)){var l=e.interleaved;l===null?(i.next=i,Lc(e)):(i.next=l.next,l.next=i),e.interleaved=i;return}}catch{}finally{}n=$m(t,e,i,r),n!==null&&(i=Oe(),St(n,t,r,i),Jm(n,e,r))}}function qm(t){var e=t.alternate;return t===se||e!==null&&e===se}function Zm(t,e){Si=ba=!0;var n=t.pending;n===null?e.next=e:(e.next=n.next,n.next=e),t.pending=e}function Jm(t,e,n){if(n&4194240){var r=e.lanes;r&=t.pendingLanes,n|=r,e.lanes=n,Sc(t,n)}}var wa={readContext:ot,useCallback:je,useContext:je,useEffect:je,useImperativeHandle:je,useInsertionEffect:je,useLayoutEffect:je,useMemo:je,useReducer:je,useRef:je,useState:je,useDebugValue:je,useDeferredValue:je,useTransition:je,useMutableSource:je,useSyncExternalStore:je,useId:je,unstable_isNewReconciler:!1},Wx={readContext:ot,useCallback:function(t,e){return Ct().memoizedState=[t,e===void 0?null:e],t},useContext:ot,useEffect:ud,useImperativeHandle:function(t,e,n){return n=n!=null?n.concat([t]):null,Xs(4194308,4,Vm.bind(null,e,t),n)},useLayoutEffect:function(t,e){return Xs(4194308,4,t,e)},useInsertionEffect:function(t,e){return Xs(4,2,t,e)},useMemo:function(t,e){var n=Ct();return e=e===void 0?null:e,t=t(),n.memoizedState=[t,e],t},useReducer:function(t,e,n){var r=Ct();return e=n!==void 0?n(e):e,r.memoizedState=r.baseState=e,t={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:t,lastRenderedState:e},r.queue=t,t=t.dispatch=Bx.bind(null,se,t),[r.memoizedState,t]},useRef:function(t){var e=Ct();return t={current:t},e.memoizedState=t},useState:cd,useDebugValue:Vc,useDeferredValue:function(t){return Ct().memoizedState=t},useTransition:function(){var t=cd(!1),e=t[0];return t=Ux.bind(null,t[1]),Ct().memoizedState=t,[e,t]},useMutableSource:function(){},useSyncExternalStore:function(t,e,n){var r=se,i=Ct();if(ne){if(n===void 0)throw Error(E(407));n=n()}else{if(n=e(),we===null)throw Error(E(349));Yn&30||Am(r,e,n)}i.memoizedState=n;var s={value:n,getSnapshot:e};return i.queue=s,ud(zm.bind(null,r,s,t),[t]),r.flags|=2048,Wi(9,Fm.bind(null,r,s,n,e),void 0,null),n},useId:function(){var t=Ct(),e=we.identifierPrefix;if(ne){var n=Ut,r=It;n=(r&~(1<<32-wt(r)-1)).toString(32)+n,e=":"+e+"R"+n,n=Bi++,0<n&&(e+="H"+n.toString(32)),e+=":"}else n=Ix++,e=":"+e+"r"+n.toString(32)+":";return t.memoizedState=e},unstable_isNewReconciler:!1},Vx={readContext:ot,useCallback:Xm,useContext:ot,useEffect:Wc,useImperativeHandle:Ym,useInsertionEffect:Hm,useLayoutEffect:Wm,useMemo:Km,useReducer:Do,useRef:Bm,useState:function(){return Do(Hi)},useDebugValue:Vc,useDeferredValue:function(t){var e=lt();return Qm(e,ge.memoizedState,t)},useTransition:function(){var t=Do(Hi)[0],e=lt().memoizedState;return[t,e]},useMutableSource:Om,useSyncExternalStore:Lm,useId:Gm,unstable_isNewReconciler:!1},Yx={readContext:ot,useCallback:Xm,useContext:ot,useEffect:Wc,useImperativeHandle:Ym,useInsertionEffect:Hm,useLayoutEffect:Wm,useMemo:Km,useReducer:Oo,useRef:Bm,useState:function(){return Oo(Hi)},useDebugValue:Vc,useDeferredValue:function(t){var e=lt();return ge===null?e.memoizedState=t:Qm(e,ge.memoizedState,t)},useTransition:function(){var t=Oo(Hi)[0],e=lt().memoizedState;return[t,e]},useMutableSource:Om,useSyncExternalStore:Lm,useId:Gm,unstable_isNewReconciler:!1};function mt(t,e){if(t&&t.defaultProps){e=ae({},e),t=t.defaultProps;for(var n in t)e[n]===void 0&&(e[n]=t[n]);return e}return e}function $l(t,e,n,r){e=t.memoizedState,n=n(r,e),n=n==null?e:ae({},e,n),t.memoizedState=n,t.lanes===0&&(t.updateQueue.baseState=n)}var Za={isMounted:function(t){return(t=t._reactInternals)?Jn(t)===t:!1},enqueueSetState:function(t,e,n){t=t._reactInternals;var r=Oe(),i=pn(t),s=Ht(r,i);s.payload=e,n!=null&&(s.callback=n),e=fn(t,s,i),e!==null&&(St(e,t,i,r),Vs(e,t,i))},enqueueReplaceState:function(t,e,n){t=t._reactInternals;var r=Oe(),i=pn(t),s=Ht(r,i);s.tag=1,s.payload=e,n!=null&&(s.callback=n),e=fn(t,s,i),e!==null&&(St(e,t,i,r),Vs(e,t,i))},enqueueForceUpdate:function(t,e){t=t._reactInternals;var n=Oe(),r=pn(t),i=Ht(n,r);i.tag=2,e!=null&&(i.callback=e),e=fn(t,i,r),e!==null&&(St(e,t,r,n),Vs(e,t,r))}};function dd(t,e,n,r,i,s,a){return t=t.stateNode,typeof t.shouldComponentUpdate=="function"?t.shouldComponentUpdate(r,s,a):e.prototype&&e.prototype.isPureReactComponent?!Li(n,r)||!Li(i,s):!0}function ep(t,e,n){var r=!1,i=Sn,s=e.contextType;return typeof s=="object"&&s!==null?s=ot(s):(i=Ye(e)?Wn:$e.current,r=e.contextTypes,s=(r=r!=null)?Tr(t,i):Sn),e=new e(n,s),t.memoizedState=e.state!==null&&e.state!==void 0?e.state:null,e.updater=Za,t.stateNode=e,e._reactInternals=t,r&&(t=t.stateNode,t.__reactInternalMemoizedUnmaskedChildContext=i,t.__reactInternalMemoizedMaskedChildContext=s),e}function hd(t,e,n,r){t=e.state,typeof e.componentWillReceiveProps=="function"&&e.componentWillReceiveProps(n,r),typeof e.UNSAFE_componentWillReceiveProps=="function"&&e.UNSAFE_componentWillReceiveProps(n,r),e.state!==t&&Za.enqueueReplaceState(e,e.state,null)}function Rl(t,e,n,r){var i=t.stateNode;i.props=n,i.state=t.memoizedState,i.refs={},Ac(t);var s=e.contextType;typeof s=="object"&&s!==null?i.context=ot(s):(s=Ye(e)?Wn:$e.current,i.context=Tr(t,s)),i.state=t.memoizedState,s=e.getDerivedStateFromProps,typeof s=="function"&&($l(t,e,s,n),i.state=t.memoizedState),typeof e.getDerivedStateFromProps=="function"||typeof i.getSnapshotBeforeUpdate=="function"||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(e=i.state,typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount(),e!==i.state&&Za.enqueueReplaceState(i,i.state,null),va(t,n,i,r),i.state=t.memoizedState),typeof i.componentDidMount=="function"&&(t.flags|=4194308)}function Dr(t,e){try{var n="",r=e;do n+=b0(r),r=r.return;while(r);var i=n}catch(s){i=`
Error generating stack: `+s.message+`
`+s.stack}return{value:t,source:e,stack:i,digest:null}}function Lo(t,e,n){return{value:t,source:null,stack:n??null,digest:e??null}}function Dl(t,e){try{console.error(e.value)}catch(n){setTimeout(function(){throw n})}}var Xx=typeof WeakMap=="function"?WeakMap:Map;function tp(t,e,n){n=Ht(-1,n),n.tag=3,n.payload={element:null};var r=e.value;return n.callback=function(){Na||(Na=!0,Wl=r),Dl(t,e)},n}function np(t,e,n){n=Ht(-1,n),n.tag=3;var r=t.type.getDerivedStateFromError;if(typeof r=="function"){var i=e.value;n.payload=function(){return r(i)},n.callback=function(){Dl(t,e)}}var s=t.stateNode;return s!==null&&typeof s.componentDidCatch=="function"&&(n.callback=function(){Dl(t,e),typeof r!="function"&&(mn===null?mn=new Set([this]):mn.add(this));var a=e.stack;this.componentDidCatch(e.value,{componentStack:a!==null?a:""})}),n}function fd(t,e,n){var r=t.pingCache;if(r===null){r=t.pingCache=new Xx;var i=new Set;r.set(e,i)}else i=r.get(e),i===void 0&&(i=new Set,r.set(e,i));i.has(n)||(i.add(n),t=ov.bind(null,t,e,n),e.then(t,t))}function md(t){do{var e;if((e=t.tag===13)&&(e=t.memoizedState,e=e!==null?e.dehydrated!==null:!0),e)return t;t=t.return}while(t!==null);return null}function pd(t,e,n,r,i){return t.mode&1?(t.flags|=65536,t.lanes=i,t):(t===e?t.flags|=65536:(t.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(e=Ht(-1,1),e.tag=2,fn(n,e,1))),n.lanes|=1),t)}var Kx=Qt.ReactCurrentOwner,He=!1;function Re(t,e,n,r){e.child=t===null?Mm(e,null,n,r):$r(e,t.child,n,r)}function gd(t,e,n,r,i){n=n.render;var s=e.ref;return Nr(e,i),r=Bc(t,e,n,r,s,i),n=Hc(),t!==null&&!He?(e.updateQueue=t.updateQueue,e.flags&=-2053,t.lanes&=~i,Xt(t,e,i)):(ne&&n&&Tc(e),e.flags|=1,Re(t,e,r,i),e.child)}function xd(t,e,n,r,i){if(t===null){var s=n.type;return typeof s=="function"&&!Jc(s)&&s.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(e.tag=15,e.type=s,rp(t,e,s,r,i)):(t=qs(n.type,null,r,e,e.mode,i),t.ref=e.ref,t.return=e,e.child=t)}if(s=t.child,!(t.lanes&i)){var a=s.memoizedProps;if(n=n.compare,n=n!==null?n:Li,n(a,r)&&t.ref===e.ref)return Xt(t,e,i)}return e.flags|=1,t=gn(s,r),t.ref=e.ref,t.return=e,e.child=t}function rp(t,e,n,r,i){if(t!==null){var s=t.memoizedProps;if(Li(s,r)&&t.ref===e.ref)if(He=!1,e.pendingProps=r=s,(t.lanes&i)!==0)t.flags&131072&&(He=!0);else return e.lanes=t.lanes,Xt(t,e,i)}return Ol(t,e,n,r,i)}function ip(t,e,n){var r=e.pendingProps,i=r.children,s=t!==null?t.memoizedState:null;if(r.mode==="hidden")if(!(e.mode&1))e.memoizedState={baseLanes:0,cachePool:null,transitions:null},Z(vr,Qe),Qe|=n;else{if(!(n&1073741824))return t=s!==null?s.baseLanes|n:n,e.lanes=e.childLanes=1073741824,e.memoizedState={baseLanes:t,cachePool:null,transitions:null},e.updateQueue=null,Z(vr,Qe),Qe|=t,null;e.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=s!==null?s.baseLanes:n,Z(vr,Qe),Qe|=r}else s!==null?(r=s.baseLanes|n,e.memoizedState=null):r=n,Z(vr,Qe),Qe|=r;return Re(t,e,i,n),e.child}function sp(t,e){var n=e.ref;(t===null&&n!==null||t!==null&&t.ref!==n)&&(e.flags|=512,e.flags|=2097152)}function Ol(t,e,n,r,i){var s=Ye(n)?Wn:$e.current;return s=Tr(e,s),Nr(e,i),n=Bc(t,e,n,r,s,i),r=Hc(),t!==null&&!He?(e.updateQueue=t.updateQueue,e.flags&=-2053,t.lanes&=~i,Xt(t,e,i)):(ne&&r&&Tc(e),e.flags|=1,Re(t,e,n,i),e.child)}function vd(t,e,n,r,i){if(Ye(n)){var s=!0;fa(e)}else s=!1;if(Nr(e,i),e.stateNode===null)Ks(t,e),ep(e,n,r),Rl(e,n,r,i),r=!0;else if(t===null){var a=e.stateNode,o=e.memoizedProps;a.props=o;var l=a.context,u=n.contextType;typeof u=="object"&&u!==null?u=ot(u):(u=Ye(n)?Wn:$e.current,u=Tr(e,u));var d=n.getDerivedStateFromProps,h=typeof d=="function"||typeof a.getSnapshotBeforeUpdate=="function";h||typeof a.UNSAFE_componentWillReceiveProps!="function"&&typeof a.componentWillReceiveProps!="function"||(o!==r||l!==u)&&hd(e,a,r,u),en=!1;var f=e.memoizedState;a.state=f,va(e,r,a,i),l=e.memoizedState,o!==r||f!==l||Ve.current||en?(typeof d=="function"&&($l(e,n,d,r),l=e.memoizedState),(o=en||dd(e,n,o,r,f,l,u))?(h||typeof a.UNSAFE_componentWillMount!="function"&&typeof a.componentWillMount!="function"||(typeof a.componentWillMount=="function"&&a.componentWillMount(),typeof a.UNSAFE_componentWillMount=="function"&&a.UNSAFE_componentWillMount()),typeof a.componentDidMount=="function"&&(e.flags|=4194308)):(typeof a.componentDidMount=="function"&&(e.flags|=4194308),e.memoizedProps=r,e.memoizedState=l),a.props=r,a.state=l,a.context=u,r=o):(typeof a.componentDidMount=="function"&&(e.flags|=4194308),r=!1)}else{a=e.stateNode,Rm(t,e),o=e.memoizedProps,u=e.type===e.elementType?o:mt(e.type,o),a.props=u,h=e.pendingProps,f=a.context,l=n.contextType,typeof l=="object"&&l!==null?l=ot(l):(l=Ye(n)?Wn:$e.current,l=Tr(e,l));var m=n.getDerivedStateFromProps;(d=typeof m=="function"||typeof a.getSnapshotBeforeUpdate=="function")||typeof a.UNSAFE_componentWillReceiveProps!="function"&&typeof a.componentWillReceiveProps!="function"||(o!==h||f!==l)&&hd(e,a,r,l),en=!1,f=e.memoizedState,a.state=f,va(e,r,a,i);var x=e.memoizedState;o!==h||f!==x||Ve.current||en?(typeof m=="function"&&($l(e,n,m,r),x=e.memoizedState),(u=en||dd(e,n,u,r,f,x,l)||!1)?(d||typeof a.UNSAFE_componentWillUpdate!="function"&&typeof a.componentWillUpdate!="function"||(typeof a.componentWillUpdate=="function"&&a.componentWillUpdate(r,x,l),typeof a.UNSAFE_componentWillUpdate=="function"&&a.UNSAFE_componentWillUpdate(r,x,l)),typeof a.componentDidUpdate=="function"&&(e.flags|=4),typeof a.getSnapshotBeforeUpdate=="function"&&(e.flags|=1024)):(typeof a.componentDidUpdate!="function"||o===t.memoizedProps&&f===t.memoizedState||(e.flags|=4),typeof a.getSnapshotBeforeUpdate!="function"||o===t.memoizedProps&&f===t.memoizedState||(e.flags|=1024),e.memoizedProps=r,e.memoizedState=x),a.props=r,a.state=x,a.context=l,r=u):(typeof a.componentDidUpdate!="function"||o===t.memoizedProps&&f===t.memoizedState||(e.flags|=4),typeof a.getSnapshotBeforeUpdate!="function"||o===t.memoizedProps&&f===t.memoizedState||(e.flags|=1024),r=!1)}return Ll(t,e,n,r,s,i)}function Ll(t,e,n,r,i,s){sp(t,e);var a=(e.flags&128)!==0;if(!r&&!a)return i&&rd(e,n,!1),Xt(t,e,s);r=e.stateNode,Kx.current=e;var o=a&&typeof n.getDerivedStateFromError!="function"?null:r.render();return e.flags|=1,t!==null&&a?(e.child=$r(e,t.child,null,s),e.child=$r(e,null,o,s)):Re(t,e,o,s),e.memoizedState=r.state,i&&rd(e,n,!0),e.child}function ap(t){var e=t.stateNode;e.pendingContext?nd(t,e.pendingContext,e.pendingContext!==e.context):e.context&&nd(t,e.context,!1),Fc(t,e.containerInfo)}function yd(t,e,n,r,i){return Mr(),$c(i),e.flags|=256,Re(t,e,n,r),e.child}var Al={dehydrated:null,treeContext:null,retryLane:0};function Fl(t){return{baseLanes:t,cachePool:null,transitions:null}}function op(t,e,n){var r=e.pendingProps,i=ie.current,s=!1,a=(e.flags&128)!==0,o;if((o=a)||(o=t!==null&&t.memoizedState===null?!1:(i&2)!==0),o?(s=!0,e.flags&=-129):(t===null||t.memoizedState!==null)&&(i|=1),Z(ie,i&1),t===null)return Tl(e),t=e.memoizedState,t!==null&&(t=t.dehydrated,t!==null)?(e.mode&1?t.data==="$!"?e.lanes=8:e.lanes=1073741824:e.lanes=1,null):(a=r.children,t=r.fallback,s?(r=e.mode,s=e.child,a={mode:"hidden",children:a},!(r&1)&&s!==null?(s.childLanes=0,s.pendingProps=a):s=to(a,r,0,null),t=Bn(t,r,n,null),s.return=e,t.return=e,s.sibling=t,e.child=s,e.child.memoizedState=Fl(n),e.memoizedState=Al,t):Yc(e,a));if(i=t.memoizedState,i!==null&&(o=i.dehydrated,o!==null))return Qx(t,e,a,r,o,i,n);if(s){s=r.fallback,a=e.mode,i=t.child,o=i.sibling;var l={mode:"hidden",children:r.children};return!(a&1)&&e.child!==i?(r=e.child,r.childLanes=0,r.pendingProps=l,e.deletions=null):(r=gn(i,l),r.subtreeFlags=i.subtreeFlags&14680064),o!==null?s=gn(o,s):(s=Bn(s,a,n,null),s.flags|=2),s.return=e,r.return=e,r.sibling=s,e.child=r,r=s,s=e.child,a=t.child.memoizedState,a=a===null?Fl(n):{baseLanes:a.baseLanes|n,cachePool:null,transitions:a.transitions},s.memoizedState=a,s.childLanes=t.childLanes&~n,e.memoizedState=Al,r}return s=t.child,t=s.sibling,r=gn(s,{mode:"visible",children:r.children}),!(e.mode&1)&&(r.lanes=n),r.return=e,r.sibling=null,t!==null&&(n=e.deletions,n===null?(e.deletions=[t],e.flags|=16):n.push(t)),e.child=r,e.memoizedState=null,r}function Yc(t,e){return e=to({mode:"visible",children:e},t.mode,0,null),e.return=t,t.child=e}function Ss(t,e,n,r){return r!==null&&$c(r),$r(e,t.child,null,n),t=Yc(e,e.pendingProps.children),t.flags|=2,e.memoizedState=null,t}function Qx(t,e,n,r,i,s,a){if(n)return e.flags&256?(e.flags&=-257,r=Lo(Error(E(422))),Ss(t,e,a,r)):e.memoizedState!==null?(e.child=t.child,e.flags|=128,null):(s=r.fallback,i=e.mode,r=to({mode:"visible",children:r.children},i,0,null),s=Bn(s,i,a,null),s.flags|=2,r.return=e,s.return=e,r.sibling=s,e.child=r,e.mode&1&&$r(e,t.child,null,a),e.child.memoizedState=Fl(a),e.memoizedState=Al,s);if(!(e.mode&1))return Ss(t,e,a,null);if(i.data==="$!"){if(r=i.nextSibling&&i.nextSibling.dataset,r)var o=r.dgst;return r=o,s=Error(E(419)),r=Lo(s,r,void 0),Ss(t,e,a,r)}if(o=(a&t.childLanes)!==0,He||o){if(r=we,r!==null){switch(a&-a){case 4:i=2;break;case 16:i=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:i=32;break;case 536870912:i=268435456;break;default:i=0}i=i&(r.suspendedLanes|a)?0:i,i!==0&&i!==s.retryLane&&(s.retryLane=i,Yt(t,i),St(r,t,i,-1))}return Zc(),r=Lo(Error(E(421))),Ss(t,e,a,r)}return i.data==="$?"?(e.flags|=128,e.child=t.child,e=lv.bind(null,t),i._reactRetry=e,null):(t=s.treeContext,Ge=hn(i.nextSibling),qe=e,ne=!0,gt=null,t!==null&&(nt[rt++]=It,nt[rt++]=Ut,nt[rt++]=Vn,It=t.id,Ut=t.overflow,Vn=e),e=Yc(e,r.children),e.flags|=4096,e)}function bd(t,e,n){t.lanes|=e;var r=t.alternate;r!==null&&(r.lanes|=e),Ml(t.return,e,n)}function Ao(t,e,n,r,i){var s=t.memoizedState;s===null?t.memoizedState={isBackwards:e,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:i}:(s.isBackwards=e,s.rendering=null,s.renderingStartTime=0,s.last=r,s.tail=n,s.tailMode=i)}function lp(t,e,n){var r=e.pendingProps,i=r.revealOrder,s=r.tail;if(Re(t,e,r.children,n),r=ie.current,r&2)r=r&1|2,e.flags|=128;else{if(t!==null&&t.flags&128)e:for(t=e.child;t!==null;){if(t.tag===13)t.memoizedState!==null&&bd(t,n,e);else if(t.tag===19)bd(t,n,e);else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break e;for(;t.sibling===null;){if(t.return===null||t.return===e)break e;t=t.return}t.sibling.return=t.return,t=t.sibling}r&=1}if(Z(ie,r),!(e.mode&1))e.memoizedState=null;else switch(i){case"forwards":for(n=e.child,i=null;n!==null;)t=n.alternate,t!==null&&ya(t)===null&&(i=n),n=n.sibling;n=i,n===null?(i=e.child,e.child=null):(i=n.sibling,n.sibling=null),Ao(e,!1,i,n,s);break;case"backwards":for(n=null,i=e.child,e.child=null;i!==null;){if(t=i.alternate,t!==null&&ya(t)===null){e.child=i;break}t=i.sibling,i.sibling=n,n=i,i=t}Ao(e,!0,n,null,s);break;case"together":Ao(e,!1,null,null,void 0);break;default:e.memoizedState=null}return e.child}function Ks(t,e){!(e.mode&1)&&t!==null&&(t.alternate=null,e.alternate=null,e.flags|=2)}function Xt(t,e,n){if(t!==null&&(e.dependencies=t.dependencies),Xn|=e.lanes,!(n&e.childLanes))return null;if(t!==null&&e.child!==t.child)throw Error(E(153));if(e.child!==null){for(t=e.child,n=gn(t,t.pendingProps),e.child=n,n.return=e;t.sibling!==null;)t=t.sibling,n=n.sibling=gn(t,t.pendingProps),n.return=e;n.sibling=null}return e.child}function Gx(t,e,n){switch(e.tag){case 3:ap(e),Mr();break;case 5:Dm(e);break;case 1:Ye(e.type)&&fa(e);break;case 4:Fc(e,e.stateNode.containerInfo);break;case 10:var r=e.type._context,i=e.memoizedProps.value;Z(ga,r._currentValue),r._currentValue=i;break;case 13:if(r=e.memoizedState,r!==null)return r.dehydrated!==null?(Z(ie,ie.current&1),e.flags|=128,null):n&e.child.childLanes?op(t,e,n):(Z(ie,ie.current&1),t=Xt(t,e,n),t!==null?t.sibling:null);Z(ie,ie.current&1);break;case 19:if(r=(n&e.childLanes)!==0,t.flags&128){if(r)return lp(t,e,n);e.flags|=128}if(i=e.memoizedState,i!==null&&(i.rendering=null,i.tail=null,i.lastEffect=null),Z(ie,ie.current),r)break;return null;case 22:case 23:return e.lanes=0,ip(t,e,n)}return Xt(t,e,n)}var cp,zl,up,dp;cp=function(t,e){for(var n=e.child;n!==null;){if(n.tag===5||n.tag===6)t.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===e)break;for(;n.sibling===null;){if(n.return===null||n.return===e)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};zl=function(){};up=function(t,e,n,r){var i=t.memoizedProps;if(i!==r){t=e.stateNode,zn(Mt.current);var s=null;switch(n){case"input":i=ol(t,i),r=ol(t,r),s=[];break;case"select":i=ae({},i,{value:void 0}),r=ae({},r,{value:void 0}),s=[];break;case"textarea":i=ul(t,i),r=ul(t,r),s=[];break;default:typeof i.onClick!="function"&&typeof r.onClick=="function"&&(t.onclick=da)}hl(n,r);var a;n=null;for(u in i)if(!r.hasOwnProperty(u)&&i.hasOwnProperty(u)&&i[u]!=null)if(u==="style"){var o=i[u];for(a in o)o.hasOwnProperty(a)&&(n||(n={}),n[a]="")}else u!=="dangerouslySetInnerHTML"&&u!=="children"&&u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&u!=="autoFocus"&&(Ei.hasOwnProperty(u)?s||(s=[]):(s=s||[]).push(u,null));for(u in r){var l=r[u];if(o=i!=null?i[u]:void 0,r.hasOwnProperty(u)&&l!==o&&(l!=null||o!=null))if(u==="style")if(o){for(a in o)!o.hasOwnProperty(a)||l&&l.hasOwnProperty(a)||(n||(n={}),n[a]="");for(a in l)l.hasOwnProperty(a)&&o[a]!==l[a]&&(n||(n={}),n[a]=l[a])}else n||(s||(s=[]),s.push(u,n)),n=l;else u==="dangerouslySetInnerHTML"?(l=l?l.__html:void 0,o=o?o.__html:void 0,l!=null&&o!==l&&(s=s||[]).push(u,l)):u==="children"?typeof l!="string"&&typeof l!="number"||(s=s||[]).push(u,""+l):u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&(Ei.hasOwnProperty(u)?(l!=null&&u==="onScroll"&&J("scroll",t),s||o===l||(s=[])):(s=s||[]).push(u,l))}n&&(s=s||[]).push("style",n);var u=s;(e.updateQueue=u)&&(e.flags|=4)}};dp=function(t,e,n,r){n!==r&&(e.flags|=4)};function Jr(t,e){if(!ne)switch(t.tailMode){case"hidden":e=t.tail;for(var n=null;e!==null;)e.alternate!==null&&(n=e),e=e.sibling;n===null?t.tail=null:n.sibling=null;break;case"collapsed":n=t.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?e||t.tail===null?t.tail=null:t.tail.sibling=null:r.sibling=null}}function Ce(t){var e=t.alternate!==null&&t.alternate.child===t.child,n=0,r=0;if(e)for(var i=t.child;i!==null;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags&14680064,r|=i.flags&14680064,i.return=t,i=i.sibling;else for(i=t.child;i!==null;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags,r|=i.flags,i.return=t,i=i.sibling;return t.subtreeFlags|=r,t.childLanes=n,e}function qx(t,e,n){var r=e.pendingProps;switch(Mc(e),e.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Ce(e),null;case 1:return Ye(e.type)&&ha(),Ce(e),null;case 3:return r=e.stateNode,Rr(),te(Ve),te($e),Ic(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(t===null||t.child===null)&&(bs(e)?e.flags|=4:t===null||t.memoizedState.isDehydrated&&!(e.flags&256)||(e.flags|=1024,gt!==null&&(Xl(gt),gt=null))),zl(t,e),Ce(e),null;case 5:zc(e);var i=zn(Ui.current);if(n=e.type,t!==null&&e.stateNode!=null)up(t,e,n,r,i),t.ref!==e.ref&&(e.flags|=512,e.flags|=2097152);else{if(!r){if(e.stateNode===null)throw Error(E(166));return Ce(e),null}if(t=zn(Mt.current),bs(e)){r=e.stateNode,n=e.type;var s=e.memoizedProps;switch(r[Et]=e,r[zi]=s,t=(e.mode&1)!==0,n){case"dialog":J("cancel",r),J("close",r);break;case"iframe":case"object":case"embed":J("load",r);break;case"video":case"audio":for(i=0;i<di.length;i++)J(di[i],r);break;case"source":J("error",r);break;case"img":case"image":case"link":J("error",r),J("load",r);break;case"details":J("toggle",r);break;case"input":Eu(r,s),J("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!s.multiple},J("invalid",r);break;case"textarea":Mu(r,s),J("invalid",r)}hl(n,s),i=null;for(var a in s)if(s.hasOwnProperty(a)){var o=s[a];a==="children"?typeof o=="string"?r.textContent!==o&&(s.suppressHydrationWarning!==!0&&ys(r.textContent,o,t),i=["children",o]):typeof o=="number"&&r.textContent!==""+o&&(s.suppressHydrationWarning!==!0&&ys(r.textContent,o,t),i=["children",""+o]):Ei.hasOwnProperty(a)&&o!=null&&a==="onScroll"&&J("scroll",r)}switch(n){case"input":ds(r),Tu(r,s,!0);break;case"textarea":ds(r),$u(r);break;case"select":case"option":break;default:typeof s.onClick=="function"&&(r.onclick=da)}r=i,e.updateQueue=r,r!==null&&(e.flags|=4)}else{a=i.nodeType===9?i:i.ownerDocument,t==="http://www.w3.org/1999/xhtml"&&(t=zf(n)),t==="http://www.w3.org/1999/xhtml"?n==="script"?(t=a.createElement("div"),t.innerHTML="<script><\/script>",t=t.removeChild(t.firstChild)):typeof r.is=="string"?t=a.createElement(n,{is:r.is}):(t=a.createElement(n),n==="select"&&(a=t,r.multiple?a.multiple=!0:r.size&&(a.size=r.size))):t=a.createElementNS(t,n),t[Et]=e,t[zi]=r,cp(t,e,!1,!1),e.stateNode=t;e:{switch(a=fl(n,r),n){case"dialog":J("cancel",t),J("close",t),i=r;break;case"iframe":case"object":case"embed":J("load",t),i=r;break;case"video":case"audio":for(i=0;i<di.length;i++)J(di[i],t);i=r;break;case"source":J("error",t),i=r;break;case"img":case"image":case"link":J("error",t),J("load",t),i=r;break;case"details":J("toggle",t),i=r;break;case"input":Eu(t,r),i=ol(t,r),J("invalid",t);break;case"option":i=r;break;case"select":t._wrapperState={wasMultiple:!!r.multiple},i=ae({},r,{value:void 0}),J("invalid",t);break;case"textarea":Mu(t,r),i=ul(t,r),J("invalid",t);break;default:i=r}hl(n,i),o=i;for(s in o)if(o.hasOwnProperty(s)){var l=o[s];s==="style"?Bf(t,l):s==="dangerouslySetInnerHTML"?(l=l?l.__html:void 0,l!=null&&If(t,l)):s==="children"?typeof l=="string"?(n!=="textarea"||l!=="")&&Ti(t,l):typeof l=="number"&&Ti(t,""+l):s!=="suppressContentEditableWarning"&&s!=="suppressHydrationWarning"&&s!=="autoFocus"&&(Ei.hasOwnProperty(s)?l!=null&&s==="onScroll"&&J("scroll",t):l!=null&&gc(t,s,l,a))}switch(n){case"input":ds(t),Tu(t,r,!1);break;case"textarea":ds(t),$u(t);break;case"option":r.value!=null&&t.setAttribute("value",""+wn(r.value));break;case"select":t.multiple=!!r.multiple,s=r.value,s!=null?yr(t,!!r.multiple,s,!1):r.defaultValue!=null&&yr(t,!!r.multiple,r.defaultValue,!0);break;default:typeof i.onClick=="function"&&(t.onclick=da)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(e.flags|=4)}e.ref!==null&&(e.flags|=512,e.flags|=2097152)}return Ce(e),null;case 6:if(t&&e.stateNode!=null)dp(t,e,t.memoizedProps,r);else{if(typeof r!="string"&&e.stateNode===null)throw Error(E(166));if(n=zn(Ui.current),zn(Mt.current),bs(e)){if(r=e.stateNode,n=e.memoizedProps,r[Et]=e,(s=r.nodeValue!==n)&&(t=qe,t!==null))switch(t.tag){case 3:ys(r.nodeValue,n,(t.mode&1)!==0);break;case 5:t.memoizedProps.suppressHydrationWarning!==!0&&ys(r.nodeValue,n,(t.mode&1)!==0)}s&&(e.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[Et]=e,e.stateNode=r}return Ce(e),null;case 13:if(te(ie),r=e.memoizedState,t===null||t.memoizedState!==null&&t.memoizedState.dehydrated!==null){if(ne&&Ge!==null&&e.mode&1&&!(e.flags&128))Em(),Mr(),e.flags|=98560,s=!1;else if(s=bs(e),r!==null&&r.dehydrated!==null){if(t===null){if(!s)throw Error(E(318));if(s=e.memoizedState,s=s!==null?s.dehydrated:null,!s)throw Error(E(317));s[Et]=e}else Mr(),!(e.flags&128)&&(e.memoizedState=null),e.flags|=4;Ce(e),s=!1}else gt!==null&&(Xl(gt),gt=null),s=!0;if(!s)return e.flags&65536?e:null}return e.flags&128?(e.lanes=n,e):(r=r!==null,r!==(t!==null&&t.memoizedState!==null)&&r&&(e.child.flags|=8192,e.mode&1&&(t===null||ie.current&1?ve===0&&(ve=3):Zc())),e.updateQueue!==null&&(e.flags|=4),Ce(e),null);case 4:return Rr(),zl(t,e),t===null&&Ai(e.stateNode.containerInfo),Ce(e),null;case 10:return Oc(e.type._context),Ce(e),null;case 17:return Ye(e.type)&&ha(),Ce(e),null;case 19:if(te(ie),s=e.memoizedState,s===null)return Ce(e),null;if(r=(e.flags&128)!==0,a=s.rendering,a===null)if(r)Jr(s,!1);else{if(ve!==0||t!==null&&t.flags&128)for(t=e.child;t!==null;){if(a=ya(t),a!==null){for(e.flags|=128,Jr(s,!1),r=a.updateQueue,r!==null&&(e.updateQueue=r,e.flags|=4),e.subtreeFlags=0,r=n,n=e.child;n!==null;)s=n,t=r,s.flags&=14680066,a=s.alternate,a===null?(s.childLanes=0,s.lanes=t,s.child=null,s.subtreeFlags=0,s.memoizedProps=null,s.memoizedState=null,s.updateQueue=null,s.dependencies=null,s.stateNode=null):(s.childLanes=a.childLanes,s.lanes=a.lanes,s.child=a.child,s.subtreeFlags=0,s.deletions=null,s.memoizedProps=a.memoizedProps,s.memoizedState=a.memoizedState,s.updateQueue=a.updateQueue,s.type=a.type,t=a.dependencies,s.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext}),n=n.sibling;return Z(ie,ie.current&1|2),e.child}t=t.sibling}s.tail!==null&&ue()>Or&&(e.flags|=128,r=!0,Jr(s,!1),e.lanes=4194304)}else{if(!r)if(t=ya(a),t!==null){if(e.flags|=128,r=!0,n=t.updateQueue,n!==null&&(e.updateQueue=n,e.flags|=4),Jr(s,!0),s.tail===null&&s.tailMode==="hidden"&&!a.alternate&&!ne)return Ce(e),null}else 2*ue()-s.renderingStartTime>Or&&n!==1073741824&&(e.flags|=128,r=!0,Jr(s,!1),e.lanes=4194304);s.isBackwards?(a.sibling=e.child,e.child=a):(n=s.last,n!==null?n.sibling=a:e.child=a,s.last=a)}return s.tail!==null?(e=s.tail,s.rendering=e,s.tail=e.sibling,s.renderingStartTime=ue(),e.sibling=null,n=ie.current,Z(ie,r?n&1|2:n&1),e):(Ce(e),null);case 22:case 23:return qc(),r=e.memoizedState!==null,t!==null&&t.memoizedState!==null!==r&&(e.flags|=8192),r&&e.mode&1?Qe&1073741824&&(Ce(e),e.subtreeFlags&6&&(e.flags|=8192)):Ce(e),null;case 24:return null;case 25:return null}throw Error(E(156,e.tag))}function Zx(t,e){switch(Mc(e),e.tag){case 1:return Ye(e.type)&&ha(),t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 3:return Rr(),te(Ve),te($e),Ic(),t=e.flags,t&65536&&!(t&128)?(e.flags=t&-65537|128,e):null;case 5:return zc(e),null;case 13:if(te(ie),t=e.memoizedState,t!==null&&t.dehydrated!==null){if(e.alternate===null)throw Error(E(340));Mr()}return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 19:return te(ie),null;case 4:return Rr(),null;case 10:return Oc(e.type._context),null;case 22:case 23:return qc(),null;case 24:return null;default:return null}}var Ns=!1,Ee=!1,Jx=typeof WeakSet=="function"?WeakSet:Set,O=null;function xr(t,e){var n=t.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){oe(t,e,r)}else n.current=null}function Il(t,e,n){try{n()}catch(r){oe(t,e,r)}}var wd=!1;function ev(t,e){if(Nl=la,t=gm(),Ec(t)){if("selectionStart"in t)var n={start:t.selectionStart,end:t.selectionEnd};else e:{n=(n=t.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var i=r.anchorOffset,s=r.focusNode;r=r.focusOffset;try{n.nodeType,s.nodeType}catch{n=null;break e}var a=0,o=-1,l=-1,u=0,d=0,h=t,f=null;t:for(;;){for(var m;h!==n||i!==0&&h.nodeType!==3||(o=a+i),h!==s||r!==0&&h.nodeType!==3||(l=a+r),h.nodeType===3&&(a+=h.nodeValue.length),(m=h.firstChild)!==null;)f=h,h=m;for(;;){if(h===t)break t;if(f===n&&++u===i&&(o=a),f===s&&++d===r&&(l=a),(m=h.nextSibling)!==null)break;h=f,f=h.parentNode}h=m}n=o===-1||l===-1?null:{start:o,end:l}}else n=null}n=n||{start:0,end:0}}else n=null;for(_l={focusedElem:t,selectionRange:n},la=!1,O=e;O!==null;)if(e=O,t=e.child,(e.subtreeFlags&1028)!==0&&t!==null)t.return=e,O=t;else for(;O!==null;){e=O;try{var x=e.alternate;if(e.flags&1024)switch(e.tag){case 0:case 11:case 15:break;case 1:if(x!==null){var v=x.memoizedProps,w=x.memoizedState,g=e.stateNode,p=g.getSnapshotBeforeUpdate(e.elementType===e.type?v:mt(e.type,v),w);g.__reactInternalSnapshotBeforeUpdate=p}break;case 3:var y=e.stateNode.containerInfo;y.nodeType===1?y.textContent="":y.nodeType===9&&y.documentElement&&y.removeChild(y.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(E(163))}}catch(b){oe(e,e.return,b)}if(t=e.sibling,t!==null){t.return=e.return,O=t;break}O=e.return}return x=wd,wd=!1,x}function Ni(t,e,n){var r=e.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var i=r=r.next;do{if((i.tag&t)===t){var s=i.destroy;i.destroy=void 0,s!==void 0&&Il(e,n,s)}i=i.next}while(i!==r)}}function Ja(t,e){if(e=e.updateQueue,e=e!==null?e.lastEffect:null,e!==null){var n=e=e.next;do{if((n.tag&t)===t){var r=n.create;n.destroy=r()}n=n.next}while(n!==e)}}function Ul(t){var e=t.ref;if(e!==null){var n=t.stateNode;switch(t.tag){case 5:t=n;break;default:t=n}typeof e=="function"?e(t):e.current=t}}function hp(t){var e=t.alternate;e!==null&&(t.alternate=null,hp(e)),t.child=null,t.deletions=null,t.sibling=null,t.tag===5&&(e=t.stateNode,e!==null&&(delete e[Et],delete e[zi],delete e[Cl],delete e[Lx],delete e[Ax])),t.stateNode=null,t.return=null,t.dependencies=null,t.memoizedProps=null,t.memoizedState=null,t.pendingProps=null,t.stateNode=null,t.updateQueue=null}function fp(t){return t.tag===5||t.tag===3||t.tag===4}function Sd(t){e:for(;;){for(;t.sibling===null;){if(t.return===null||fp(t.return))return null;t=t.return}for(t.sibling.return=t.return,t=t.sibling;t.tag!==5&&t.tag!==6&&t.tag!==18;){if(t.flags&2||t.child===null||t.tag===4)continue e;t.child.return=t,t=t.child}if(!(t.flags&2))return t.stateNode}}function Bl(t,e,n){var r=t.tag;if(r===5||r===6)t=t.stateNode,e?n.nodeType===8?n.parentNode.insertBefore(t,e):n.insertBefore(t,e):(n.nodeType===8?(e=n.parentNode,e.insertBefore(t,n)):(e=n,e.appendChild(t)),n=n._reactRootContainer,n!=null||e.onclick!==null||(e.onclick=da));else if(r!==4&&(t=t.child,t!==null))for(Bl(t,e,n),t=t.sibling;t!==null;)Bl(t,e,n),t=t.sibling}function Hl(t,e,n){var r=t.tag;if(r===5||r===6)t=t.stateNode,e?n.insertBefore(t,e):n.appendChild(t);else if(r!==4&&(t=t.child,t!==null))for(Hl(t,e,n),t=t.sibling;t!==null;)Hl(t,e,n),t=t.sibling}var Ne=null,pt=!1;function Gt(t,e,n){for(n=n.child;n!==null;)mp(t,e,n),n=n.sibling}function mp(t,e,n){if(Tt&&typeof Tt.onCommitFiberUnmount=="function")try{Tt.onCommitFiberUnmount(Va,n)}catch{}switch(n.tag){case 5:Ee||xr(n,e);case 6:var r=Ne,i=pt;Ne=null,Gt(t,e,n),Ne=r,pt=i,Ne!==null&&(pt?(t=Ne,n=n.stateNode,t.nodeType===8?t.parentNode.removeChild(n):t.removeChild(n)):Ne.removeChild(n.stateNode));break;case 18:Ne!==null&&(pt?(t=Ne,n=n.stateNode,t.nodeType===8?To(t.parentNode,n):t.nodeType===1&&To(t,n),Di(t)):To(Ne,n.stateNode));break;case 4:r=Ne,i=pt,Ne=n.stateNode.containerInfo,pt=!0,Gt(t,e,n),Ne=r,pt=i;break;case 0:case 11:case 14:case 15:if(!Ee&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){i=r=r.next;do{var s=i,a=s.destroy;s=s.tag,a!==void 0&&(s&2||s&4)&&Il(n,e,a),i=i.next}while(i!==r)}Gt(t,e,n);break;case 1:if(!Ee&&(xr(n,e),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(o){oe(n,e,o)}Gt(t,e,n);break;case 21:Gt(t,e,n);break;case 22:n.mode&1?(Ee=(r=Ee)||n.memoizedState!==null,Gt(t,e,n),Ee=r):Gt(t,e,n);break;default:Gt(t,e,n)}}function Nd(t){var e=t.updateQueue;if(e!==null){t.updateQueue=null;var n=t.stateNode;n===null&&(n=t.stateNode=new Jx),e.forEach(function(r){var i=cv.bind(null,t,r);n.has(r)||(n.add(r),r.then(i,i))})}}function ht(t,e){var n=e.deletions;if(n!==null)for(var r=0;r<n.length;r++){var i=n[r];try{var s=t,a=e,o=a;e:for(;o!==null;){switch(o.tag){case 5:Ne=o.stateNode,pt=!1;break e;case 3:Ne=o.stateNode.containerInfo,pt=!0;break e;case 4:Ne=o.stateNode.containerInfo,pt=!0;break e}o=o.return}if(Ne===null)throw Error(E(160));mp(s,a,i),Ne=null,pt=!1;var l=i.alternate;l!==null&&(l.return=null),i.return=null}catch(u){oe(i,e,u)}}if(e.subtreeFlags&12854)for(e=e.child;e!==null;)pp(e,t),e=e.sibling}function pp(t,e){var n=t.alternate,r=t.flags;switch(t.tag){case 0:case 11:case 14:case 15:if(ht(e,t),_t(t),r&4){try{Ni(3,t,t.return),Ja(3,t)}catch(v){oe(t,t.return,v)}try{Ni(5,t,t.return)}catch(v){oe(t,t.return,v)}}break;case 1:ht(e,t),_t(t),r&512&&n!==null&&xr(n,n.return);break;case 5:if(ht(e,t),_t(t),r&512&&n!==null&&xr(n,n.return),t.flags&32){var i=t.stateNode;try{Ti(i,"")}catch(v){oe(t,t.return,v)}}if(r&4&&(i=t.stateNode,i!=null)){var s=t.memoizedProps,a=n!==null?n.memoizedProps:s,o=t.type,l=t.updateQueue;if(t.updateQueue=null,l!==null)try{o==="input"&&s.type==="radio"&&s.name!=null&&Af(i,s),fl(o,a);var u=fl(o,s);for(a=0;a<l.length;a+=2){var d=l[a],h=l[a+1];d==="style"?Bf(i,h):d==="dangerouslySetInnerHTML"?If(i,h):d==="children"?Ti(i,h):gc(i,d,h,u)}switch(o){case"input":ll(i,s);break;case"textarea":Ff(i,s);break;case"select":var f=i._wrapperState.wasMultiple;i._wrapperState.wasMultiple=!!s.multiple;var m=s.value;m!=null?yr(i,!!s.multiple,m,!1):f!==!!s.multiple&&(s.defaultValue!=null?yr(i,!!s.multiple,s.defaultValue,!0):yr(i,!!s.multiple,s.multiple?[]:"",!1))}i[zi]=s}catch(v){oe(t,t.return,v)}}break;case 6:if(ht(e,t),_t(t),r&4){if(t.stateNode===null)throw Error(E(162));i=t.stateNode,s=t.memoizedProps;try{i.nodeValue=s}catch(v){oe(t,t.return,v)}}break;case 3:if(ht(e,t),_t(t),r&4&&n!==null&&n.memoizedState.isDehydrated)try{Di(e.containerInfo)}catch(v){oe(t,t.return,v)}break;case 4:ht(e,t),_t(t);break;case 13:ht(e,t),_t(t),i=t.child,i.flags&8192&&(s=i.memoizedState!==null,i.stateNode.isHidden=s,!s||i.alternate!==null&&i.alternate.memoizedState!==null||(Qc=ue())),r&4&&Nd(t);break;case 22:if(d=n!==null&&n.memoizedState!==null,t.mode&1?(Ee=(u=Ee)||d,ht(e,t),Ee=u):ht(e,t),_t(t),r&8192){if(u=t.memoizedState!==null,(t.stateNode.isHidden=u)&&!d&&t.mode&1)for(O=t,d=t.child;d!==null;){for(h=O=d;O!==null;){switch(f=O,m=f.child,f.tag){case 0:case 11:case 14:case 15:Ni(4,f,f.return);break;case 1:xr(f,f.return);var x=f.stateNode;if(typeof x.componentWillUnmount=="function"){r=f,n=f.return;try{e=r,x.props=e.memoizedProps,x.state=e.memoizedState,x.componentWillUnmount()}catch(v){oe(r,n,v)}}break;case 5:xr(f,f.return);break;case 22:if(f.memoizedState!==null){kd(h);continue}}m!==null?(m.return=f,O=m):kd(h)}d=d.sibling}e:for(d=null,h=t;;){if(h.tag===5){if(d===null){d=h;try{i=h.stateNode,u?(s=i.style,typeof s.setProperty=="function"?s.setProperty("display","none","important"):s.display="none"):(o=h.stateNode,l=h.memoizedProps.style,a=l!=null&&l.hasOwnProperty("display")?l.display:null,o.style.display=Uf("display",a))}catch(v){oe(t,t.return,v)}}}else if(h.tag===6){if(d===null)try{h.stateNode.nodeValue=u?"":h.memoizedProps}catch(v){oe(t,t.return,v)}}else if((h.tag!==22&&h.tag!==23||h.memoizedState===null||h===t)&&h.child!==null){h.child.return=h,h=h.child;continue}if(h===t)break e;for(;h.sibling===null;){if(h.return===null||h.return===t)break e;d===h&&(d=null),h=h.return}d===h&&(d=null),h.sibling.return=h.return,h=h.sibling}}break;case 19:ht(e,t),_t(t),r&4&&Nd(t);break;case 21:break;default:ht(e,t),_t(t)}}function _t(t){var e=t.flags;if(e&2){try{e:{for(var n=t.return;n!==null;){if(fp(n)){var r=n;break e}n=n.return}throw Error(E(160))}switch(r.tag){case 5:var i=r.stateNode;r.flags&32&&(Ti(i,""),r.flags&=-33);var s=Sd(t);Hl(t,s,i);break;case 3:case 4:var a=r.stateNode.containerInfo,o=Sd(t);Bl(t,o,a);break;default:throw Error(E(161))}}catch(l){oe(t,t.return,l)}t.flags&=-3}e&4096&&(t.flags&=-4097)}function tv(t,e,n){O=t,gp(t)}function gp(t,e,n){for(var r=(t.mode&1)!==0;O!==null;){var i=O,s=i.child;if(i.tag===22&&r){var a=i.memoizedState!==null||Ns;if(!a){var o=i.alternate,l=o!==null&&o.memoizedState!==null||Ee;o=Ns;var u=Ee;if(Ns=a,(Ee=l)&&!u)for(O=i;O!==null;)a=O,l=a.child,a.tag===22&&a.memoizedState!==null?jd(i):l!==null?(l.return=a,O=l):jd(i);for(;s!==null;)O=s,gp(s),s=s.sibling;O=i,Ns=o,Ee=u}_d(t)}else i.subtreeFlags&8772&&s!==null?(s.return=i,O=s):_d(t)}}function _d(t){for(;O!==null;){var e=O;if(e.flags&8772){var n=e.alternate;try{if(e.flags&8772)switch(e.tag){case 0:case 11:case 15:Ee||Ja(5,e);break;case 1:var r=e.stateNode;if(e.flags&4&&!Ee)if(n===null)r.componentDidMount();else{var i=e.elementType===e.type?n.memoizedProps:mt(e.type,n.memoizedProps);r.componentDidUpdate(i,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var s=e.updateQueue;s!==null&&ld(e,s,r);break;case 3:var a=e.updateQueue;if(a!==null){if(n=null,e.child!==null)switch(e.child.tag){case 5:n=e.child.stateNode;break;case 1:n=e.child.stateNode}ld(e,a,n)}break;case 5:var o=e.stateNode;if(n===null&&e.flags&4){n=o;var l=e.memoizedProps;switch(e.type){case"button":case"input":case"select":case"textarea":l.autoFocus&&n.focus();break;case"img":l.src&&(n.src=l.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(e.memoizedState===null){var u=e.alternate;if(u!==null){var d=u.memoizedState;if(d!==null){var h=d.dehydrated;h!==null&&Di(h)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(E(163))}Ee||e.flags&512&&Ul(e)}catch(f){oe(e,e.return,f)}}if(e===t){O=null;break}if(n=e.sibling,n!==null){n.return=e.return,O=n;break}O=e.return}}function kd(t){for(;O!==null;){var e=O;if(e===t){O=null;break}var n=e.sibling;if(n!==null){n.return=e.return,O=n;break}O=e.return}}function jd(t){for(;O!==null;){var e=O;try{switch(e.tag){case 0:case 11:case 15:var n=e.return;try{Ja(4,e)}catch(l){oe(e,n,l)}break;case 1:var r=e.stateNode;if(typeof r.componentDidMount=="function"){var i=e.return;try{r.componentDidMount()}catch(l){oe(e,i,l)}}var s=e.return;try{Ul(e)}catch(l){oe(e,s,l)}break;case 5:var a=e.return;try{Ul(e)}catch(l){oe(e,a,l)}}}catch(l){oe(e,e.return,l)}if(e===t){O=null;break}var o=e.sibling;if(o!==null){o.return=e.return,O=o;break}O=e.return}}var nv=Math.ceil,Sa=Qt.ReactCurrentDispatcher,Xc=Qt.ReactCurrentOwner,at=Qt.ReactCurrentBatchConfig,W=0,we=null,he=null,_e=0,Qe=0,vr=kn(0),ve=0,Vi=null,Xn=0,eo=0,Kc=0,_i=null,Ue=null,Qc=0,Or=1/0,At=null,Na=!1,Wl=null,mn=null,_s=!1,sn=null,_a=0,ki=0,Vl=null,Qs=-1,Gs=0;function Oe(){return W&6?ue():Qs!==-1?Qs:Qs=ue()}function pn(t){return t.mode&1?W&2&&_e!==0?_e&-_e:zx.transition!==null?(Gs===0&&(Gs=em()),Gs):(t=G,t!==0||(t=window.event,t=t===void 0?16:om(t.type)),t):1}function St(t,e,n,r){if(50<ki)throw ki=0,Vl=null,Error(E(185));ts(t,n,r),(!(W&2)||t!==we)&&(t===we&&(!(W&2)&&(eo|=n),ve===4&&nn(t,_e)),Xe(t,r),n===1&&W===0&&!(e.mode&1)&&(Or=ue()+500,Ga&&jn()))}function Xe(t,e){var n=t.callbackNode;z0(t,e);var r=oa(t,t===we?_e:0);if(r===0)n!==null&&Ou(n),t.callbackNode=null,t.callbackPriority=0;else if(e=r&-r,t.callbackPriority!==e){if(n!=null&&Ou(n),e===1)t.tag===0?Fx(Cd.bind(null,t)):jm(Cd.bind(null,t)),Dx(function(){!(W&6)&&jn()}),n=null;else{switch(tm(r)){case 1:n=wc;break;case 4:n=Zf;break;case 16:n=aa;break;case 536870912:n=Jf;break;default:n=aa}n=_p(n,xp.bind(null,t))}t.callbackPriority=e,t.callbackNode=n}}function xp(t,e){if(Qs=-1,Gs=0,W&6)throw Error(E(327));var n=t.callbackNode;if(_r()&&t.callbackNode!==n)return null;var r=oa(t,t===we?_e:0);if(r===0)return null;if(r&30||r&t.expiredLanes||e)e=ka(t,r);else{e=r;var i=W;W|=2;var s=yp();(we!==t||_e!==e)&&(At=null,Or=ue()+500,Un(t,e));do try{sv();break}catch(o){vp(t,o)}while(!0);Dc(),Sa.current=s,W=i,he!==null?e=0:(we=null,_e=0,e=ve)}if(e!==0){if(e===2&&(i=vl(t),i!==0&&(r=i,e=Yl(t,i))),e===1)throw n=Vi,Un(t,0),nn(t,r),Xe(t,ue()),n;if(e===6)nn(t,r);else{if(i=t.current.alternate,!(r&30)&&!rv(i)&&(e=ka(t,r),e===2&&(s=vl(t),s!==0&&(r=s,e=Yl(t,s))),e===1))throw n=Vi,Un(t,0),nn(t,r),Xe(t,ue()),n;switch(t.finishedWork=i,t.finishedLanes=r,e){case 0:case 1:throw Error(E(345));case 2:Rn(t,Ue,At);break;case 3:if(nn(t,r),(r&130023424)===r&&(e=Qc+500-ue(),10<e)){if(oa(t,0)!==0)break;if(i=t.suspendedLanes,(i&r)!==r){Oe(),t.pingedLanes|=t.suspendedLanes&i;break}t.timeoutHandle=jl(Rn.bind(null,t,Ue,At),e);break}Rn(t,Ue,At);break;case 4:if(nn(t,r),(r&4194240)===r)break;for(e=t.eventTimes,i=-1;0<r;){var a=31-wt(r);s=1<<a,a=e[a],a>i&&(i=a),r&=~s}if(r=i,r=ue()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*nv(r/1960))-r,10<r){t.timeoutHandle=jl(Rn.bind(null,t,Ue,At),r);break}Rn(t,Ue,At);break;case 5:Rn(t,Ue,At);break;default:throw Error(E(329))}}}return Xe(t,ue()),t.callbackNode===n?xp.bind(null,t):null}function Yl(t,e){var n=_i;return t.current.memoizedState.isDehydrated&&(Un(t,e).flags|=256),t=ka(t,e),t!==2&&(e=Ue,Ue=n,e!==null&&Xl(e)),t}function Xl(t){Ue===null?Ue=t:Ue.push.apply(Ue,t)}function rv(t){for(var e=t;;){if(e.flags&16384){var n=e.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var i=n[r],s=i.getSnapshot;i=i.value;try{if(!Nt(s(),i))return!1}catch{return!1}}}if(n=e.child,e.subtreeFlags&16384&&n!==null)n.return=e,e=n;else{if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return!0;e=e.return}e.sibling.return=e.return,e=e.sibling}}return!0}function nn(t,e){for(e&=~Kc,e&=~eo,t.suspendedLanes|=e,t.pingedLanes&=~e,t=t.expirationTimes;0<e;){var n=31-wt(e),r=1<<n;t[n]=-1,e&=~r}}function Cd(t){if(W&6)throw Error(E(327));_r();var e=oa(t,0);if(!(e&1))return Xe(t,ue()),null;var n=ka(t,e);if(t.tag!==0&&n===2){var r=vl(t);r!==0&&(e=r,n=Yl(t,r))}if(n===1)throw n=Vi,Un(t,0),nn(t,e),Xe(t,ue()),n;if(n===6)throw Error(E(345));return t.finishedWork=t.current.alternate,t.finishedLanes=e,Rn(t,Ue,At),Xe(t,ue()),null}function Gc(t,e){var n=W;W|=1;try{return t(e)}finally{W=n,W===0&&(Or=ue()+500,Ga&&jn())}}function Kn(t){sn!==null&&sn.tag===0&&!(W&6)&&_r();var e=W;W|=1;var n=at.transition,r=G;try{if(at.transition=null,G=1,t)return t()}finally{G=r,at.transition=n,W=e,!(W&6)&&jn()}}function qc(){Qe=vr.current,te(vr)}function Un(t,e){t.finishedWork=null,t.finishedLanes=0;var n=t.timeoutHandle;if(n!==-1&&(t.timeoutHandle=-1,Rx(n)),he!==null)for(n=he.return;n!==null;){var r=n;switch(Mc(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&ha();break;case 3:Rr(),te(Ve),te($e),Ic();break;case 5:zc(r);break;case 4:Rr();break;case 13:te(ie);break;case 19:te(ie);break;case 10:Oc(r.type._context);break;case 22:case 23:qc()}n=n.return}if(we=t,he=t=gn(t.current,null),_e=Qe=e,ve=0,Vi=null,Kc=eo=Xn=0,Ue=_i=null,Fn!==null){for(e=0;e<Fn.length;e++)if(n=Fn[e],r=n.interleaved,r!==null){n.interleaved=null;var i=r.next,s=n.pending;if(s!==null){var a=s.next;s.next=i,r.next=a}n.pending=r}Fn=null}return t}function vp(t,e){do{var n=he;try{if(Dc(),Ys.current=wa,ba){for(var r=se.memoizedState;r!==null;){var i=r.queue;i!==null&&(i.pending=null),r=r.next}ba=!1}if(Yn=0,be=ge=se=null,Si=!1,Bi=0,Xc.current=null,n===null||n.return===null){ve=1,Vi=e,he=null;break}e:{var s=t,a=n.return,o=n,l=e;if(e=_e,o.flags|=32768,l!==null&&typeof l=="object"&&typeof l.then=="function"){var u=l,d=o,h=d.tag;if(!(d.mode&1)&&(h===0||h===11||h===15)){var f=d.alternate;f?(d.updateQueue=f.updateQueue,d.memoizedState=f.memoizedState,d.lanes=f.lanes):(d.updateQueue=null,d.memoizedState=null)}var m=md(a);if(m!==null){m.flags&=-257,pd(m,a,o,s,e),m.mode&1&&fd(s,u,e),e=m,l=u;var x=e.updateQueue;if(x===null){var v=new Set;v.add(l),e.updateQueue=v}else x.add(l);break e}else{if(!(e&1)){fd(s,u,e),Zc();break e}l=Error(E(426))}}else if(ne&&o.mode&1){var w=md(a);if(w!==null){!(w.flags&65536)&&(w.flags|=256),pd(w,a,o,s,e),$c(Dr(l,o));break e}}s=l=Dr(l,o),ve!==4&&(ve=2),_i===null?_i=[s]:_i.push(s),s=a;do{switch(s.tag){case 3:s.flags|=65536,e&=-e,s.lanes|=e;var g=tp(s,l,e);od(s,g);break e;case 1:o=l;var p=s.type,y=s.stateNode;if(!(s.flags&128)&&(typeof p.getDerivedStateFromError=="function"||y!==null&&typeof y.componentDidCatch=="function"&&(mn===null||!mn.has(y)))){s.flags|=65536,e&=-e,s.lanes|=e;var b=np(s,o,e);od(s,b);break e}}s=s.return}while(s!==null)}wp(n)}catch(S){e=S,he===n&&n!==null&&(he=n=n.return);continue}break}while(!0)}function yp(){var t=Sa.current;return Sa.current=wa,t===null?wa:t}function Zc(){(ve===0||ve===3||ve===2)&&(ve=4),we===null||!(Xn&268435455)&&!(eo&268435455)||nn(we,_e)}function ka(t,e){var n=W;W|=2;var r=yp();(we!==t||_e!==e)&&(At=null,Un(t,e));do try{iv();break}catch(i){vp(t,i)}while(!0);if(Dc(),W=n,Sa.current=r,he!==null)throw Error(E(261));return we=null,_e=0,ve}function iv(){for(;he!==null;)bp(he)}function sv(){for(;he!==null&&!T0();)bp(he)}function bp(t){var e=Np(t.alternate,t,Qe);t.memoizedProps=t.pendingProps,e===null?wp(t):he=e,Xc.current=null}function wp(t){var e=t;do{var n=e.alternate;if(t=e.return,e.flags&32768){if(n=Zx(n,e),n!==null){n.flags&=32767,he=n;return}if(t!==null)t.flags|=32768,t.subtreeFlags=0,t.deletions=null;else{ve=6,he=null;return}}else if(n=qx(n,e,Qe),n!==null){he=n;return}if(e=e.sibling,e!==null){he=e;return}he=e=t}while(e!==null);ve===0&&(ve=5)}function Rn(t,e,n){var r=G,i=at.transition;try{at.transition=null,G=1,av(t,e,n,r)}finally{at.transition=i,G=r}return null}function av(t,e,n,r){do _r();while(sn!==null);if(W&6)throw Error(E(327));n=t.finishedWork;var i=t.finishedLanes;if(n===null)return null;if(t.finishedWork=null,t.finishedLanes=0,n===t.current)throw Error(E(177));t.callbackNode=null,t.callbackPriority=0;var s=n.lanes|n.childLanes;if(I0(t,s),t===we&&(he=we=null,_e=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||_s||(_s=!0,_p(aa,function(){return _r(),null})),s=(n.flags&15990)!==0,n.subtreeFlags&15990||s){s=at.transition,at.transition=null;var a=G;G=1;var o=W;W|=4,Xc.current=null,ev(t,n),pp(n,t),jx(_l),la=!!Nl,_l=Nl=null,t.current=n,tv(n),M0(),W=o,G=a,at.transition=s}else t.current=n;if(_s&&(_s=!1,sn=t,_a=i),s=t.pendingLanes,s===0&&(mn=null),D0(n.stateNode),Xe(t,ue()),e!==null)for(r=t.onRecoverableError,n=0;n<e.length;n++)i=e[n],r(i.value,{componentStack:i.stack,digest:i.digest});if(Na)throw Na=!1,t=Wl,Wl=null,t;return _a&1&&t.tag!==0&&_r(),s=t.pendingLanes,s&1?t===Vl?ki++:(ki=0,Vl=t):ki=0,jn(),null}function _r(){if(sn!==null){var t=tm(_a),e=at.transition,n=G;try{if(at.transition=null,G=16>t?16:t,sn===null)var r=!1;else{if(t=sn,sn=null,_a=0,W&6)throw Error(E(331));var i=W;for(W|=4,O=t.current;O!==null;){var s=O,a=s.child;if(O.flags&16){var o=s.deletions;if(o!==null){for(var l=0;l<o.length;l++){var u=o[l];for(O=u;O!==null;){var d=O;switch(d.tag){case 0:case 11:case 15:Ni(8,d,s)}var h=d.child;if(h!==null)h.return=d,O=h;else for(;O!==null;){d=O;var f=d.sibling,m=d.return;if(hp(d),d===u){O=null;break}if(f!==null){f.return=m,O=f;break}O=m}}}var x=s.alternate;if(x!==null){var v=x.child;if(v!==null){x.child=null;do{var w=v.sibling;v.sibling=null,v=w}while(v!==null)}}O=s}}if(s.subtreeFlags&2064&&a!==null)a.return=s,O=a;else e:for(;O!==null;){if(s=O,s.flags&2048)switch(s.tag){case 0:case 11:case 15:Ni(9,s,s.return)}var g=s.sibling;if(g!==null){g.return=s.return,O=g;break e}O=s.return}}var p=t.current;for(O=p;O!==null;){a=O;var y=a.child;if(a.subtreeFlags&2064&&y!==null)y.return=a,O=y;else e:for(a=p;O!==null;){if(o=O,o.flags&2048)try{switch(o.tag){case 0:case 11:case 15:Ja(9,o)}}catch(S){oe(o,o.return,S)}if(o===a){O=null;break e}var b=o.sibling;if(b!==null){b.return=o.return,O=b;break e}O=o.return}}if(W=i,jn(),Tt&&typeof Tt.onPostCommitFiberRoot=="function")try{Tt.onPostCommitFiberRoot(Va,t)}catch{}r=!0}return r}finally{G=n,at.transition=e}}return!1}function Pd(t,e,n){e=Dr(n,e),e=tp(t,e,1),t=fn(t,e,1),e=Oe(),t!==null&&(ts(t,1,e),Xe(t,e))}function oe(t,e,n){if(t.tag===3)Pd(t,t,n);else for(;e!==null;){if(e.tag===3){Pd(e,t,n);break}else if(e.tag===1){var r=e.stateNode;if(typeof e.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(mn===null||!mn.has(r))){t=Dr(n,t),t=np(e,t,1),e=fn(e,t,1),t=Oe(),e!==null&&(ts(e,1,t),Xe(e,t));break}}e=e.return}}function ov(t,e,n){var r=t.pingCache;r!==null&&r.delete(e),e=Oe(),t.pingedLanes|=t.suspendedLanes&n,we===t&&(_e&n)===n&&(ve===4||ve===3&&(_e&130023424)===_e&&500>ue()-Qc?Un(t,0):Kc|=n),Xe(t,e)}function Sp(t,e){e===0&&(t.mode&1?(e=ms,ms<<=1,!(ms&130023424)&&(ms=4194304)):e=1);var n=Oe();t=Yt(t,e),t!==null&&(ts(t,e,n),Xe(t,n))}function lv(t){var e=t.memoizedState,n=0;e!==null&&(n=e.retryLane),Sp(t,n)}function cv(t,e){var n=0;switch(t.tag){case 13:var r=t.stateNode,i=t.memoizedState;i!==null&&(n=i.retryLane);break;case 19:r=t.stateNode;break;default:throw Error(E(314))}r!==null&&r.delete(e),Sp(t,n)}var Np;Np=function(t,e,n){if(t!==null)if(t.memoizedProps!==e.pendingProps||Ve.current)He=!0;else{if(!(t.lanes&n)&&!(e.flags&128))return He=!1,Gx(t,e,n);He=!!(t.flags&131072)}else He=!1,ne&&e.flags&1048576&&Cm(e,pa,e.index);switch(e.lanes=0,e.tag){case 2:var r=e.type;Ks(t,e),t=e.pendingProps;var i=Tr(e,$e.current);Nr(e,n),i=Bc(null,e,r,t,i,n);var s=Hc();return e.flags|=1,typeof i=="object"&&i!==null&&typeof i.render=="function"&&i.$$typeof===void 0?(e.tag=1,e.memoizedState=null,e.updateQueue=null,Ye(r)?(s=!0,fa(e)):s=!1,e.memoizedState=i.state!==null&&i.state!==void 0?i.state:null,Ac(e),i.updater=Za,e.stateNode=i,i._reactInternals=e,Rl(e,r,t,n),e=Ll(null,e,r,!0,s,n)):(e.tag=0,ne&&s&&Tc(e),Re(null,e,i,n),e=e.child),e;case 16:r=e.elementType;e:{switch(Ks(t,e),t=e.pendingProps,i=r._init,r=i(r._payload),e.type=r,i=e.tag=dv(r),t=mt(r,t),i){case 0:e=Ol(null,e,r,t,n);break e;case 1:e=vd(null,e,r,t,n);break e;case 11:e=gd(null,e,r,t,n);break e;case 14:e=xd(null,e,r,mt(r.type,t),n);break e}throw Error(E(306,r,""))}return e;case 0:return r=e.type,i=e.pendingProps,i=e.elementType===r?i:mt(r,i),Ol(t,e,r,i,n);case 1:return r=e.type,i=e.pendingProps,i=e.elementType===r?i:mt(r,i),vd(t,e,r,i,n);case 3:e:{if(ap(e),t===null)throw Error(E(387));r=e.pendingProps,s=e.memoizedState,i=s.element,Rm(t,e),va(e,r,null,n);var a=e.memoizedState;if(r=a.element,s.isDehydrated)if(s={element:r,isDehydrated:!1,cache:a.cache,pendingSuspenseBoundaries:a.pendingSuspenseBoundaries,transitions:a.transitions},e.updateQueue.baseState=s,e.memoizedState=s,e.flags&256){i=Dr(Error(E(423)),e),e=yd(t,e,r,n,i);break e}else if(r!==i){i=Dr(Error(E(424)),e),e=yd(t,e,r,n,i);break e}else for(Ge=hn(e.stateNode.containerInfo.firstChild),qe=e,ne=!0,gt=null,n=Mm(e,null,r,n),e.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(Mr(),r===i){e=Xt(t,e,n);break e}Re(t,e,r,n)}e=e.child}return e;case 5:return Dm(e),t===null&&Tl(e),r=e.type,i=e.pendingProps,s=t!==null?t.memoizedProps:null,a=i.children,kl(r,i)?a=null:s!==null&&kl(r,s)&&(e.flags|=32),sp(t,e),Re(t,e,a,n),e.child;case 6:return t===null&&Tl(e),null;case 13:return op(t,e,n);case 4:return Fc(e,e.stateNode.containerInfo),r=e.pendingProps,t===null?e.child=$r(e,null,r,n):Re(t,e,r,n),e.child;case 11:return r=e.type,i=e.pendingProps,i=e.elementType===r?i:mt(r,i),gd(t,e,r,i,n);case 7:return Re(t,e,e.pendingProps,n),e.child;case 8:return Re(t,e,e.pendingProps.children,n),e.child;case 12:return Re(t,e,e.pendingProps.children,n),e.child;case 10:e:{if(r=e.type._context,i=e.pendingProps,s=e.memoizedProps,a=i.value,Z(ga,r._currentValue),r._currentValue=a,s!==null)if(Nt(s.value,a)){if(s.children===i.children&&!Ve.current){e=Xt(t,e,n);break e}}else for(s=e.child,s!==null&&(s.return=e);s!==null;){var o=s.dependencies;if(o!==null){a=s.child;for(var l=o.firstContext;l!==null;){if(l.context===r){if(s.tag===1){l=Ht(-1,n&-n),l.tag=2;var u=s.updateQueue;if(u!==null){u=u.shared;var d=u.pending;d===null?l.next=l:(l.next=d.next,d.next=l),u.pending=l}}s.lanes|=n,l=s.alternate,l!==null&&(l.lanes|=n),Ml(s.return,n,e),o.lanes|=n;break}l=l.next}}else if(s.tag===10)a=s.type===e.type?null:s.child;else if(s.tag===18){if(a=s.return,a===null)throw Error(E(341));a.lanes|=n,o=a.alternate,o!==null&&(o.lanes|=n),Ml(a,n,e),a=s.sibling}else a=s.child;if(a!==null)a.return=s;else for(a=s;a!==null;){if(a===e){a=null;break}if(s=a.sibling,s!==null){s.return=a.return,a=s;break}a=a.return}s=a}Re(t,e,i.children,n),e=e.child}return e;case 9:return i=e.type,r=e.pendingProps.children,Nr(e,n),i=ot(i),r=r(i),e.flags|=1,Re(t,e,r,n),e.child;case 14:return r=e.type,i=mt(r,e.pendingProps),i=mt(r.type,i),xd(t,e,r,i,n);case 15:return rp(t,e,e.type,e.pendingProps,n);case 17:return r=e.type,i=e.pendingProps,i=e.elementType===r?i:mt(r,i),Ks(t,e),e.tag=1,Ye(r)?(t=!0,fa(e)):t=!1,Nr(e,n),ep(e,r,i),Rl(e,r,i,n),Ll(null,e,r,!0,t,n);case 19:return lp(t,e,n);case 22:return ip(t,e,n)}throw Error(E(156,e.tag))};function _p(t,e){return qf(t,e)}function uv(t,e,n,r){this.tag=t,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=e,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function it(t,e,n,r){return new uv(t,e,n,r)}function Jc(t){return t=t.prototype,!(!t||!t.isReactComponent)}function dv(t){if(typeof t=="function")return Jc(t)?1:0;if(t!=null){if(t=t.$$typeof,t===vc)return 11;if(t===yc)return 14}return 2}function gn(t,e){var n=t.alternate;return n===null?(n=it(t.tag,e,t.key,t.mode),n.elementType=t.elementType,n.type=t.type,n.stateNode=t.stateNode,n.alternate=t,t.alternate=n):(n.pendingProps=e,n.type=t.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=t.flags&14680064,n.childLanes=t.childLanes,n.lanes=t.lanes,n.child=t.child,n.memoizedProps=t.memoizedProps,n.memoizedState=t.memoizedState,n.updateQueue=t.updateQueue,e=t.dependencies,n.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext},n.sibling=t.sibling,n.index=t.index,n.ref=t.ref,n}function qs(t,e,n,r,i,s){var a=2;if(r=t,typeof t=="function")Jc(t)&&(a=1);else if(typeof t=="string")a=5;else e:switch(t){case lr:return Bn(n.children,i,s,e);case xc:a=8,i|=8;break;case rl:return t=it(12,n,e,i|2),t.elementType=rl,t.lanes=s,t;case il:return t=it(13,n,e,i),t.elementType=il,t.lanes=s,t;case sl:return t=it(19,n,e,i),t.elementType=sl,t.lanes=s,t;case Df:return to(n,i,s,e);default:if(typeof t=="object"&&t!==null)switch(t.$$typeof){case $f:a=10;break e;case Rf:a=9;break e;case vc:a=11;break e;case yc:a=14;break e;case Jt:a=16,r=null;break e}throw Error(E(130,t==null?t:typeof t,""))}return e=it(a,n,e,i),e.elementType=t,e.type=r,e.lanes=s,e}function Bn(t,e,n,r){return t=it(7,t,r,e),t.lanes=n,t}function to(t,e,n,r){return t=it(22,t,r,e),t.elementType=Df,t.lanes=n,t.stateNode={isHidden:!1},t}function Fo(t,e,n){return t=it(6,t,null,e),t.lanes=n,t}function zo(t,e,n){return e=it(4,t.children!==null?t.children:[],t.key,e),e.lanes=n,e.stateNode={containerInfo:t.containerInfo,pendingChildren:null,implementation:t.implementation},e}function hv(t,e,n,r,i){this.tag=e,this.containerInfo=t,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=yo(0),this.expirationTimes=yo(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=yo(0),this.identifierPrefix=r,this.onRecoverableError=i,this.mutableSourceEagerHydrationData=null}function eu(t,e,n,r,i,s,a,o,l){return t=new hv(t,e,n,o,l),e===1?(e=1,s===!0&&(e|=8)):e=0,s=it(3,null,null,e),t.current=s,s.stateNode=t,s.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Ac(s),t}function fv(t,e,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:or,key:r==null?null:""+r,children:t,containerInfo:e,implementation:n}}function kp(t){if(!t)return Sn;t=t._reactInternals;e:{if(Jn(t)!==t||t.tag!==1)throw Error(E(170));var e=t;do{switch(e.tag){case 3:e=e.stateNode.context;break e;case 1:if(Ye(e.type)){e=e.stateNode.__reactInternalMemoizedMergedChildContext;break e}}e=e.return}while(e!==null);throw Error(E(171))}if(t.tag===1){var n=t.type;if(Ye(n))return km(t,n,e)}return e}function jp(t,e,n,r,i,s,a,o,l){return t=eu(n,r,!0,t,i,s,a,o,l),t.context=kp(null),n=t.current,r=Oe(),i=pn(n),s=Ht(r,i),s.callback=e??null,fn(n,s,i),t.current.lanes=i,ts(t,i,r),Xe(t,r),t}function no(t,e,n,r){var i=e.current,s=Oe(),a=pn(i);return n=kp(n),e.context===null?e.context=n:e.pendingContext=n,e=Ht(s,a),e.payload={element:t},r=r===void 0?null:r,r!==null&&(e.callback=r),t=fn(i,e,a),t!==null&&(St(t,i,a,s),Vs(t,i,a)),a}function ja(t){if(t=t.current,!t.child)return null;switch(t.child.tag){case 5:return t.child.stateNode;default:return t.child.stateNode}}function Ed(t,e){if(t=t.memoizedState,t!==null&&t.dehydrated!==null){var n=t.retryLane;t.retryLane=n!==0&&n<e?n:e}}function tu(t,e){Ed(t,e),(t=t.alternate)&&Ed(t,e)}function mv(){return null}var Cp=typeof reportError=="function"?reportError:function(t){console.error(t)};function nu(t){this._internalRoot=t}ro.prototype.render=nu.prototype.render=function(t){var e=this._internalRoot;if(e===null)throw Error(E(409));no(t,e,null,null)};ro.prototype.unmount=nu.prototype.unmount=function(){var t=this._internalRoot;if(t!==null){this._internalRoot=null;var e=t.containerInfo;Kn(function(){no(null,t,null,null)}),e[Vt]=null}};function ro(t){this._internalRoot=t}ro.prototype.unstable_scheduleHydration=function(t){if(t){var e=im();t={blockedOn:null,target:t,priority:e};for(var n=0;n<tn.length&&e!==0&&e<tn[n].priority;n++);tn.splice(n,0,t),n===0&&am(t)}};function ru(t){return!(!t||t.nodeType!==1&&t.nodeType!==9&&t.nodeType!==11)}function io(t){return!(!t||t.nodeType!==1&&t.nodeType!==9&&t.nodeType!==11&&(t.nodeType!==8||t.nodeValue!==" react-mount-point-unstable "))}function Td(){}function pv(t,e,n,r,i){if(i){if(typeof r=="function"){var s=r;r=function(){var u=ja(a);s.call(u)}}var a=jp(e,r,t,0,null,!1,!1,"",Td);return t._reactRootContainer=a,t[Vt]=a.current,Ai(t.nodeType===8?t.parentNode:t),Kn(),a}for(;i=t.lastChild;)t.removeChild(i);if(typeof r=="function"){var o=r;r=function(){var u=ja(l);o.call(u)}}var l=eu(t,0,!1,null,null,!1,!1,"",Td);return t._reactRootContainer=l,t[Vt]=l.current,Ai(t.nodeType===8?t.parentNode:t),Kn(function(){no(e,l,n,r)}),l}function so(t,e,n,r,i){var s=n._reactRootContainer;if(s){var a=s;if(typeof i=="function"){var o=i;i=function(){var l=ja(a);o.call(l)}}no(e,a,t,i)}else a=pv(n,e,t,i,r);return ja(a)}nm=function(t){switch(t.tag){case 3:var e=t.stateNode;if(e.current.memoizedState.isDehydrated){var n=ui(e.pendingLanes);n!==0&&(Sc(e,n|1),Xe(e,ue()),!(W&6)&&(Or=ue()+500,jn()))}break;case 13:Kn(function(){var r=Yt(t,1);if(r!==null){var i=Oe();St(r,t,1,i)}}),tu(t,1)}};Nc=function(t){if(t.tag===13){var e=Yt(t,134217728);if(e!==null){var n=Oe();St(e,t,134217728,n)}tu(t,134217728)}};rm=function(t){if(t.tag===13){var e=pn(t),n=Yt(t,e);if(n!==null){var r=Oe();St(n,t,e,r)}tu(t,e)}};im=function(){return G};sm=function(t,e){var n=G;try{return G=t,e()}finally{G=n}};pl=function(t,e,n){switch(e){case"input":if(ll(t,n),e=n.name,n.type==="radio"&&e!=null){for(n=t;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+e)+'][type="radio"]'),e=0;e<n.length;e++){var r=n[e];if(r!==t&&r.form===t.form){var i=Qa(r);if(!i)throw Error(E(90));Lf(r),ll(r,i)}}}break;case"textarea":Ff(t,n);break;case"select":e=n.value,e!=null&&yr(t,!!n.multiple,e,!1)}};Vf=Gc;Yf=Kn;var gv={usingClientEntryPoint:!1,Events:[rs,hr,Qa,Hf,Wf,Gc]},ei={findFiberByHostInstance:An,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},xv={bundleType:ei.bundleType,version:ei.version,rendererPackageName:ei.rendererPackageName,rendererConfig:ei.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:Qt.ReactCurrentDispatcher,findHostInstanceByFiber:function(t){return t=Qf(t),t===null?null:t.stateNode},findFiberByHostInstance:ei.findFiberByHostInstance||mv,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var ks=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!ks.isDisabled&&ks.supportsFiber)try{Va=ks.inject(xv),Tt=ks}catch{}}Je.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=gv;Je.createPortal=function(t,e){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!ru(e))throw Error(E(200));return fv(t,e,null,n)};Je.createRoot=function(t,e){if(!ru(t))throw Error(E(299));var n=!1,r="",i=Cp;return e!=null&&(e.unstable_strictMode===!0&&(n=!0),e.identifierPrefix!==void 0&&(r=e.identifierPrefix),e.onRecoverableError!==void 0&&(i=e.onRecoverableError)),e=eu(t,1,!1,null,null,n,!1,r,i),t[Vt]=e.current,Ai(t.nodeType===8?t.parentNode:t),new nu(e)};Je.findDOMNode=function(t){if(t==null)return null;if(t.nodeType===1)return t;var e=t._reactInternals;if(e===void 0)throw typeof t.render=="function"?Error(E(188)):(t=Object.keys(t).join(","),Error(E(268,t)));return t=Qf(e),t=t===null?null:t.stateNode,t};Je.flushSync=function(t){return Kn(t)};Je.hydrate=function(t,e,n){if(!io(e))throw Error(E(200));return so(null,t,e,!0,n)};Je.hydrateRoot=function(t,e,n){if(!ru(t))throw Error(E(405));var r=n!=null&&n.hydratedSources||null,i=!1,s="",a=Cp;if(n!=null&&(n.unstable_strictMode===!0&&(i=!0),n.identifierPrefix!==void 0&&(s=n.identifierPrefix),n.onRecoverableError!==void 0&&(a=n.onRecoverableError)),e=jp(e,null,t,1,n??null,i,!1,s,a),t[Vt]=e.current,Ai(t),r)for(t=0;t<r.length;t++)n=r[t],i=n._getVersion,i=i(n._source),e.mutableSourceEagerHydrationData==null?e.mutableSourceEagerHydrationData=[n,i]:e.mutableSourceEagerHydrationData.push(n,i);return new ro(e)};Je.render=function(t,e,n){if(!io(e))throw Error(E(200));return so(null,t,e,!1,n)};Je.unmountComponentAtNode=function(t){if(!io(t))throw Error(E(40));return t._reactRootContainer?(Kn(function(){so(null,null,t,!1,function(){t._reactRootContainer=null,t[Vt]=null})}),!0):!1};Je.unstable_batchedUpdates=Gc;Je.unstable_renderSubtreeIntoContainer=function(t,e,n,r){if(!io(n))throw Error(E(200));if(t==null||t._reactInternals===void 0)throw Error(E(38));return so(t,e,n,!1,r)};Je.version="18.3.1-next-f1338f8080-20240426";function Pp(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(Pp)}catch(t){console.error(t)}}Pp(),Pf.exports=Je;var vv=Pf.exports,Md=vv;tl.createRoot=Md.createRoot,tl.hydrateRoot=Md.hydrateRoot;/**
 * @remix-run/router v1.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Yi(){return Yi=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},Yi.apply(this,arguments)}var an;(function(t){t.Pop="POP",t.Push="PUSH",t.Replace="REPLACE"})(an||(an={}));const $d="popstate";function yv(t){t===void 0&&(t={});function e(r,i){let{pathname:s,search:a,hash:o}=r.location;return Kl("",{pathname:s,search:a,hash:o},i.state&&i.state.usr||null,i.state&&i.state.key||"default")}function n(r,i){return typeof i=="string"?i:Ca(i)}return wv(e,n,null,t)}function le(t,e){if(t===!1||t===null||typeof t>"u")throw new Error(e)}function Ep(t,e){if(!t){typeof console<"u"&&console.warn(e);try{throw new Error(e)}catch{}}}function bv(){return Math.random().toString(36).substr(2,8)}function Rd(t,e){return{usr:t.state,key:t.key,idx:e}}function Kl(t,e,n,r){return n===void 0&&(n=null),Yi({pathname:typeof t=="string"?t:t.pathname,search:"",hash:""},typeof e=="string"?Hr(e):e,{state:n,key:e&&e.key||r||bv()})}function Ca(t){let{pathname:e="/",search:n="",hash:r=""}=t;return n&&n!=="?"&&(e+=n.charAt(0)==="?"?n:"?"+n),r&&r!=="#"&&(e+=r.charAt(0)==="#"?r:"#"+r),e}function Hr(t){let e={};if(t){let n=t.indexOf("#");n>=0&&(e.hash=t.substr(n),t=t.substr(0,n));let r=t.indexOf("?");r>=0&&(e.search=t.substr(r),t=t.substr(0,r)),t&&(e.pathname=t)}return e}function wv(t,e,n,r){r===void 0&&(r={});let{window:i=document.defaultView,v5Compat:s=!1}=r,a=i.history,o=an.Pop,l=null,u=d();u==null&&(u=0,a.replaceState(Yi({},a.state,{idx:u}),""));function d(){return(a.state||{idx:null}).idx}function h(){o=an.Pop;let w=d(),g=w==null?null:w-u;u=w,l&&l({action:o,location:v.location,delta:g})}function f(w,g){o=an.Push;let p=Kl(v.location,w,g);u=d()+1;let y=Rd(p,u),b=v.createHref(p);try{a.pushState(y,"",b)}catch(S){if(S instanceof DOMException&&S.name==="DataCloneError")throw S;i.location.assign(b)}s&&l&&l({action:o,location:v.location,delta:1})}function m(w,g){o=an.Replace;let p=Kl(v.location,w,g);u=d();let y=Rd(p,u),b=v.createHref(p);a.replaceState(y,"",b),s&&l&&l({action:o,location:v.location,delta:0})}function x(w){let g=i.location.origin!=="null"?i.location.origin:i.location.href,p=typeof w=="string"?w:Ca(w);return p=p.replace(/ $/,"%20"),le(g,"No window.location.(origin|href) available to create URL for href: "+p),new URL(p,g)}let v={get action(){return o},get location(){return t(i,a)},listen(w){if(l)throw new Error("A history only accepts one active listener");return i.addEventListener($d,h),l=w,()=>{i.removeEventListener($d,h),l=null}},createHref(w){return e(i,w)},createURL:x,encodeLocation(w){let g=x(w);return{pathname:g.pathname,search:g.search,hash:g.hash}},push:f,replace:m,go(w){return a.go(w)}};return v}var Dd;(function(t){t.data="data",t.deferred="deferred",t.redirect="redirect",t.error="error"})(Dd||(Dd={}));function Sv(t,e,n){return n===void 0&&(n="/"),Nv(t,e,n)}function Nv(t,e,n,r){let i=typeof e=="string"?Hr(e):e,s=Lr(i.pathname||"/",n);if(s==null)return null;let a=Tp(t);_v(a);let o=null;for(let l=0;o==null&&l<a.length;++l){let u=Ov(s);o=Rv(a[l],u)}return o}function Tp(t,e,n,r){e===void 0&&(e=[]),n===void 0&&(n=[]),r===void 0&&(r="");let i=(s,a,o)=>{let l={relativePath:o===void 0?s.path||"":o,caseSensitive:s.caseSensitive===!0,childrenIndex:a,route:s};l.relativePath.startsWith("/")&&(le(l.relativePath.startsWith(r),'Absolute route path "'+l.relativePath+'" nested under path '+('"'+r+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),l.relativePath=l.relativePath.slice(r.length));let u=xn([r,l.relativePath]),d=n.concat(l);s.children&&s.children.length>0&&(le(s.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+u+'".')),Tp(s.children,e,d,u)),!(s.path==null&&!s.index)&&e.push({path:u,score:Mv(u,s.index),routesMeta:d})};return t.forEach((s,a)=>{var o;if(s.path===""||!((o=s.path)!=null&&o.includes("?")))i(s,a);else for(let l of Mp(s.path))i(s,a,l)}),e}function Mp(t){let e=t.split("/");if(e.length===0)return[];let[n,...r]=e,i=n.endsWith("?"),s=n.replace(/\?$/,"");if(r.length===0)return i?[s,""]:[s];let a=Mp(r.join("/")),o=[];return o.push(...a.map(l=>l===""?s:[s,l].join("/"))),i&&o.push(...a),o.map(l=>t.startsWith("/")&&l===""?"/":l)}function _v(t){t.sort((e,n)=>e.score!==n.score?n.score-e.score:$v(e.routesMeta.map(r=>r.childrenIndex),n.routesMeta.map(r=>r.childrenIndex)))}const kv=/^:[\w-]+$/,jv=3,Cv=2,Pv=1,Ev=10,Tv=-2,Od=t=>t==="*";function Mv(t,e){let n=t.split("/"),r=n.length;return n.some(Od)&&(r+=Tv),e&&(r+=Cv),n.filter(i=>!Od(i)).reduce((i,s)=>i+(kv.test(s)?jv:s===""?Pv:Ev),r)}function $v(t,e){return t.length===e.length&&t.slice(0,-1).every((r,i)=>r===e[i])?t[t.length-1]-e[e.length-1]:0}function Rv(t,e,n){let{routesMeta:r}=t,i={},s="/",a=[];for(let o=0;o<r.length;++o){let l=r[o],u=o===r.length-1,d=s==="/"?e:e.slice(s.length)||"/",h=Ql({path:l.relativePath,caseSensitive:l.caseSensitive,end:u},d),f=l.route;if(!h)return null;Object.assign(i,h.params),a.push({params:i,pathname:xn([s,h.pathname]),pathnameBase:zv(xn([s,h.pathnameBase])),route:f}),h.pathnameBase!=="/"&&(s=xn([s,h.pathnameBase]))}return a}function Ql(t,e){typeof t=="string"&&(t={path:t,caseSensitive:!1,end:!0});let[n,r]=Dv(t.path,t.caseSensitive,t.end),i=e.match(n);if(!i)return null;let s=i[0],a=s.replace(/(.)\/+$/,"$1"),o=i.slice(1);return{params:r.reduce((u,d,h)=>{let{paramName:f,isOptional:m}=d;if(f==="*"){let v=o[h]||"";a=s.slice(0,s.length-v.length).replace(/(.)\/+$/,"$1")}const x=o[h];return m&&!x?u[f]=void 0:u[f]=(x||"").replace(/%2F/g,"/"),u},{}),pathname:s,pathnameBase:a,pattern:t}}function Dv(t,e,n){e===void 0&&(e=!1),n===void 0&&(n=!0),Ep(t==="*"||!t.endsWith("*")||t.endsWith("/*"),'Route path "'+t+'" will be treated as if it were '+('"'+t.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+t.replace(/\*$/,"/*")+'".'));let r=[],i="^"+t.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(a,o,l)=>(r.push({paramName:o,isOptional:l!=null}),l?"/?([^\\/]+)?":"/([^\\/]+)"));return t.endsWith("*")?(r.push({paramName:"*"}),i+=t==="*"||t==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?i+="\\/*$":t!==""&&t!=="/"&&(i+="(?:(?=\\/|$))"),[new RegExp(i,e?void 0:"i"),r]}function Ov(t){try{return t.split("/").map(e=>decodeURIComponent(e).replace(/\//g,"%2F")).join("/")}catch(e){return Ep(!1,'The URL path "'+t+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+e+").")),t}}function Lr(t,e){if(e==="/")return t;if(!t.toLowerCase().startsWith(e.toLowerCase()))return null;let n=e.endsWith("/")?e.length-1:e.length,r=t.charAt(n);return r&&r!=="/"?null:t.slice(n)||"/"}function Lv(t,e){e===void 0&&(e="/");let{pathname:n,search:r="",hash:i=""}=typeof t=="string"?Hr(t):t;return{pathname:n?n.startsWith("/")?n:Av(n,e):e,search:Iv(r),hash:Uv(i)}}function Av(t,e){let n=e.replace(/\/+$/,"").split("/");return t.split("/").forEach(i=>{i===".."?n.length>1&&n.pop():i!=="."&&n.push(i)}),n.length>1?n.join("/"):"/"}function Io(t,e,n,r){return"Cannot include a '"+t+"' character in a manually specified "+("`to."+e+"` field ["+JSON.stringify(r)+"].  Please separate it out to the ")+("`to."+n+"` field. Alternatively you may provide the full path as ")+'a string in <Link to="..."> and the router will parse it for you.'}function Fv(t){return t.filter((e,n)=>n===0||e.route.path&&e.route.path.length>0)}function $p(t,e){let n=Fv(t);return e?n.map((r,i)=>i===n.length-1?r.pathname:r.pathnameBase):n.map(r=>r.pathnameBase)}function Rp(t,e,n,r){r===void 0&&(r=!1);let i;typeof t=="string"?i=Hr(t):(i=Yi({},t),le(!i.pathname||!i.pathname.includes("?"),Io("?","pathname","search",i)),le(!i.pathname||!i.pathname.includes("#"),Io("#","pathname","hash",i)),le(!i.search||!i.search.includes("#"),Io("#","search","hash",i)));let s=t===""||i.pathname==="",a=s?"/":i.pathname,o;if(a==null)o=n;else{let h=e.length-1;if(!r&&a.startsWith("..")){let f=a.split("/");for(;f[0]==="..";)f.shift(),h-=1;i.pathname=f.join("/")}o=h>=0?e[h]:"/"}let l=Lv(i,o),u=a&&a!=="/"&&a.endsWith("/"),d=(s||a===".")&&n.endsWith("/");return!l.pathname.endsWith("/")&&(u||d)&&(l.pathname+="/"),l}const xn=t=>t.join("/").replace(/\/\/+/g,"/"),zv=t=>t.replace(/\/+$/,"").replace(/^\/*/,"/"),Iv=t=>!t||t==="?"?"":t.startsWith("?")?t:"?"+t,Uv=t=>!t||t==="#"?"":t.startsWith("#")?t:"#"+t;function Bv(t){return t!=null&&typeof t.status=="number"&&typeof t.statusText=="string"&&typeof t.internal=="boolean"&&"data"in t}const Dp=["post","put","patch","delete"];new Set(Dp);const Hv=["get",...Dp];new Set(Hv);/**
 * React Router v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Xi(){return Xi=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},Xi.apply(this,arguments)}const ao=_.createContext(null),Op=_.createContext(null),Cn=_.createContext(null),oo=_.createContext(null),er=_.createContext({outlet:null,matches:[],isDataRoute:!1}),Lp=_.createContext(null);function Wv(t,e){let{relative:n}=e===void 0?{}:e;ss()||le(!1);let{basename:r,navigator:i}=_.useContext(Cn),{hash:s,pathname:a,search:o}=lo(t,{relative:n}),l=a;return r!=="/"&&(l=a==="/"?r:xn([r,a])),i.createHref({pathname:l,search:o,hash:s})}function ss(){return _.useContext(oo)!=null}function $t(){return ss()||le(!1),_.useContext(oo).location}function Ap(t){_.useContext(Cn).static||_.useLayoutEffect(t)}function tr(){let{isDataRoute:t}=_.useContext(er);return t?ry():Vv()}function Vv(){ss()||le(!1);let t=_.useContext(ao),{basename:e,future:n,navigator:r}=_.useContext(Cn),{matches:i}=_.useContext(er),{pathname:s}=$t(),a=JSON.stringify($p(i,n.v7_relativeSplatPath)),o=_.useRef(!1);return Ap(()=>{o.current=!0}),_.useCallback(function(u,d){if(d===void 0&&(d={}),!o.current)return;if(typeof u=="number"){r.go(u);return}let h=Rp(u,JSON.parse(a),s,d.relative==="path");t==null&&e!=="/"&&(h.pathname=h.pathname==="/"?e:xn([e,h.pathname])),(d.replace?r.replace:r.push)(h,d.state,d)},[e,r,a,s,t])}function lo(t,e){let{relative:n}=e===void 0?{}:e,{future:r}=_.useContext(Cn),{matches:i}=_.useContext(er),{pathname:s}=$t(),a=JSON.stringify($p(i,r.v7_relativeSplatPath));return _.useMemo(()=>Rp(t,JSON.parse(a),s,n==="path"),[t,a,s,n])}function Yv(t,e){return Xv(t,e)}function Xv(t,e,n,r){ss()||le(!1);let{navigator:i}=_.useContext(Cn),{matches:s}=_.useContext(er),a=s[s.length-1],o=a?a.params:{};a&&a.pathname;let l=a?a.pathnameBase:"/";a&&a.route;let u=$t(),d;if(e){var h;let w=typeof e=="string"?Hr(e):e;l==="/"||(h=w.pathname)!=null&&h.startsWith(l)||le(!1),d=w}else d=u;let f=d.pathname||"/",m=f;if(l!=="/"){let w=l.replace(/^\//,"").split("/");m="/"+f.replace(/^\//,"").split("/").slice(w.length).join("/")}let x=Sv(t,{pathname:m}),v=Zv(x&&x.map(w=>Object.assign({},w,{params:Object.assign({},o,w.params),pathname:xn([l,i.encodeLocation?i.encodeLocation(w.pathname).pathname:w.pathname]),pathnameBase:w.pathnameBase==="/"?l:xn([l,i.encodeLocation?i.encodeLocation(w.pathnameBase).pathname:w.pathnameBase])})),s,n,r);return e&&v?_.createElement(oo.Provider,{value:{location:Xi({pathname:"/",search:"",hash:"",state:null,key:"default"},d),navigationType:an.Pop}},v):v}function Kv(){let t=ny(),e=Bv(t)?t.status+" "+t.statusText:t instanceof Error?t.message:JSON.stringify(t),n=t instanceof Error?t.stack:null,i={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"};return _.createElement(_.Fragment,null,_.createElement("h2",null,"Unexpected Application Error!"),_.createElement("h3",{style:{fontStyle:"italic"}},e),n?_.createElement("pre",{style:i},n):null,null)}const Qv=_.createElement(Kv,null);class Gv extends _.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,n){return n.location!==e.location||n.revalidation!=="idle"&&e.revalidation==="idle"?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:e.error!==void 0?e.error:n.error,location:n.location,revalidation:e.revalidation||n.revalidation}}componentDidCatch(e,n){console.error("React Router caught the following error during render",e,n)}render(){return this.state.error!==void 0?_.createElement(er.Provider,{value:this.props.routeContext},_.createElement(Lp.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function qv(t){let{routeContext:e,match:n,children:r}=t,i=_.useContext(ao);return i&&i.static&&i.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(i.staticContext._deepestRenderedBoundaryId=n.route.id),_.createElement(er.Provider,{value:e},r)}function Zv(t,e,n,r){var i;if(e===void 0&&(e=[]),n===void 0&&(n=null),r===void 0&&(r=null),t==null){var s;if(!n)return null;if(n.errors)t=n.matches;else if((s=r)!=null&&s.v7_partialHydration&&e.length===0&&!n.initialized&&n.matches.length>0)t=n.matches;else return null}let a=t,o=(i=n)==null?void 0:i.errors;if(o!=null){let d=a.findIndex(h=>h.route.id&&(o==null?void 0:o[h.route.id])!==void 0);d>=0||le(!1),a=a.slice(0,Math.min(a.length,d+1))}let l=!1,u=-1;if(n&&r&&r.v7_partialHydration)for(let d=0;d<a.length;d++){let h=a[d];if((h.route.HydrateFallback||h.route.hydrateFallbackElement)&&(u=d),h.route.id){let{loaderData:f,errors:m}=n,x=h.route.loader&&f[h.route.id]===void 0&&(!m||m[h.route.id]===void 0);if(h.route.lazy||x){l=!0,u>=0?a=a.slice(0,u+1):a=[a[0]];break}}}return a.reduceRight((d,h,f)=>{let m,x=!1,v=null,w=null;n&&(m=o&&h.route.id?o[h.route.id]:void 0,v=h.route.errorElement||Qv,l&&(u<0&&f===0?(iy("route-fallback"),x=!0,w=null):u===f&&(x=!0,w=h.route.hydrateFallbackElement||null)));let g=e.concat(a.slice(0,f+1)),p=()=>{let y;return m?y=v:x?y=w:h.route.Component?y=_.createElement(h.route.Component,null):h.route.element?y=h.route.element:y=d,_.createElement(qv,{match:h,routeContext:{outlet:d,matches:g,isDataRoute:n!=null},children:y})};return n&&(h.route.ErrorBoundary||h.route.errorElement||f===0)?_.createElement(Gv,{location:n.location,revalidation:n.revalidation,component:v,error:m,children:p(),routeContext:{outlet:null,matches:g,isDataRoute:!0}}):p()},null)}var Fp=function(t){return t.UseBlocker="useBlocker",t.UseRevalidator="useRevalidator",t.UseNavigateStable="useNavigate",t}(Fp||{}),zp=function(t){return t.UseBlocker="useBlocker",t.UseLoaderData="useLoaderData",t.UseActionData="useActionData",t.UseRouteError="useRouteError",t.UseNavigation="useNavigation",t.UseRouteLoaderData="useRouteLoaderData",t.UseMatches="useMatches",t.UseRevalidator="useRevalidator",t.UseNavigateStable="useNavigate",t.UseRouteId="useRouteId",t}(zp||{});function Jv(t){let e=_.useContext(ao);return e||le(!1),e}function ey(t){let e=_.useContext(Op);return e||le(!1),e}function ty(t){let e=_.useContext(er);return e||le(!1),e}function Ip(t){let e=ty(),n=e.matches[e.matches.length-1];return n.route.id||le(!1),n.route.id}function ny(){var t;let e=_.useContext(Lp),n=ey(),r=Ip();return e!==void 0?e:(t=n.errors)==null?void 0:t[r]}function ry(){let{router:t}=Jv(Fp.UseNavigateStable),e=Ip(zp.UseNavigateStable),n=_.useRef(!1);return Ap(()=>{n.current=!0}),_.useCallback(function(i,s){s===void 0&&(s={}),n.current&&(typeof i=="number"?t.navigate(i):t.navigate(i,Xi({fromRouteId:e},s)))},[t,e])}const Ld={};function iy(t,e,n){Ld[t]||(Ld[t]=!0)}function sy(t,e){t==null||t.v7_startTransition,t==null||t.v7_relativeSplatPath}function Dn(t){le(!1)}function ay(t){let{basename:e="/",children:n=null,location:r,navigationType:i=an.Pop,navigator:s,static:a=!1,future:o}=t;ss()&&le(!1);let l=e.replace(/^\/*/,"/"),u=_.useMemo(()=>({basename:l,navigator:s,static:a,future:Xi({v7_relativeSplatPath:!1},o)}),[l,o,s,a]);typeof r=="string"&&(r=Hr(r));let{pathname:d="/",search:h="",hash:f="",state:m=null,key:x="default"}=r,v=_.useMemo(()=>{let w=Lr(d,l);return w==null?null:{location:{pathname:w,search:h,hash:f,state:m,key:x},navigationType:i}},[l,d,h,f,m,x,i]);return v==null?null:_.createElement(Cn.Provider,{value:u},_.createElement(oo.Provider,{children:n,value:v}))}function oy(t){let{children:e,location:n}=t;return Yv(Gl(e),n)}new Promise(()=>{});function Gl(t,e){e===void 0&&(e=[]);let n=[];return _.Children.forEach(t,(r,i)=>{if(!_.isValidElement(r))return;let s=[...e,i];if(r.type===_.Fragment){n.push.apply(n,Gl(r.props.children,s));return}r.type!==Dn&&le(!1),!r.props.index||!r.props.children||le(!1);let a={id:r.props.id||s.join("-"),caseSensitive:r.props.caseSensitive,element:r.props.element,Component:r.props.Component,index:r.props.index,path:r.props.path,loader:r.props.loader,action:r.props.action,errorElement:r.props.errorElement,ErrorBoundary:r.props.ErrorBoundary,hasErrorBoundary:r.props.ErrorBoundary!=null||r.props.errorElement!=null,shouldRevalidate:r.props.shouldRevalidate,handle:r.props.handle,lazy:r.props.lazy};r.props.children&&(a.children=Gl(r.props.children,s)),n.push(a)}),n}/**
 * React Router DOM v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Pa(){return Pa=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},Pa.apply(this,arguments)}function Up(t,e){if(t==null)return{};var n={},r=Object.keys(t),i,s;for(s=0;s<r.length;s++)i=r[s],!(e.indexOf(i)>=0)&&(n[i]=t[i]);return n}function ly(t){return!!(t.metaKey||t.altKey||t.ctrlKey||t.shiftKey)}function cy(t,e){return t.button===0&&(!e||e==="_self")&&!ly(t)}function ql(t){return t===void 0&&(t=""),new URLSearchParams(typeof t=="string"||Array.isArray(t)||t instanceof URLSearchParams?t:Object.keys(t).reduce((e,n)=>{let r=t[n];return e.concat(Array.isArray(r)?r.map(i=>[n,i]):[[n,r]])},[]))}function uy(t,e){let n=ql(t);return e&&e.forEach((r,i)=>{n.has(i)||e.getAll(i).forEach(s=>{n.append(i,s)})}),n}const dy=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"],hy=["aria-current","caseSensitive","className","end","style","to","viewTransition","children"],fy="6";try{window.__reactRouterVersion=fy}catch{}const my=_.createContext({isTransitioning:!1}),py="startTransition",Ad=o0[py];function gy(t){let{basename:e,children:n,future:r,window:i}=t,s=_.useRef();s.current==null&&(s.current=yv({window:i,v5Compat:!0}));let a=s.current,[o,l]=_.useState({action:a.action,location:a.location}),{v7_startTransition:u}=r||{},d=_.useCallback(h=>{u&&Ad?Ad(()=>l(h)):l(h)},[l,u]);return _.useLayoutEffect(()=>a.listen(d),[a,d]),_.useEffect(()=>sy(r),[r]),_.createElement(ay,{basename:e,children:n,location:o.location,navigationType:o.action,navigator:a,future:r})}const xy=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",vy=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,yy=_.forwardRef(function(e,n){let{onClick:r,relative:i,reloadDocument:s,replace:a,state:o,target:l,to:u,preventScrollReset:d,viewTransition:h}=e,f=Up(e,dy),{basename:m}=_.useContext(Cn),x,v=!1;if(typeof u=="string"&&vy.test(u)&&(x=u,xy))try{let y=new URL(window.location.href),b=u.startsWith("//")?new URL(y.protocol+u):new URL(u),S=Lr(b.pathname,m);b.origin===y.origin&&S!=null?u=S+b.search+b.hash:v=!0}catch{}let w=Wv(u,{relative:i}),g=wy(u,{replace:a,state:o,target:l,preventScrollReset:d,relative:i,viewTransition:h});function p(y){r&&r(y),y.defaultPrevented||g(y)}return _.createElement("a",Pa({},f,{href:x||w,onClick:v||s?r:p,ref:n,target:l}))}),js=_.forwardRef(function(e,n){let{"aria-current":r="page",caseSensitive:i=!1,className:s="",end:a=!1,style:o,to:l,viewTransition:u,children:d}=e,h=Up(e,hy),f=lo(l,{relative:h.relative}),m=$t(),x=_.useContext(Op),{navigator:v,basename:w}=_.useContext(Cn),g=x!=null&&Ny(f)&&u===!0,p=v.encodeLocation?v.encodeLocation(f).pathname:f.pathname,y=m.pathname,b=x&&x.navigation&&x.navigation.location?x.navigation.location.pathname:null;i||(y=y.toLowerCase(),b=b?b.toLowerCase():null,p=p.toLowerCase()),b&&w&&(b=Lr(b,w)||b);const S=p!=="/"&&p.endsWith("/")?p.length-1:p.length;let j=y===p||!a&&y.startsWith(p)&&y.charAt(S)==="/",N=b!=null&&(b===p||!a&&b.startsWith(p)&&b.charAt(p.length)==="/"),k={isActive:j,isPending:N,isTransitioning:g},C=j?r:void 0,P;typeof s=="function"?P=s(k):P=[s,j?"active":null,N?"pending":null,g?"transitioning":null].filter(Boolean).join(" ");let M=typeof o=="function"?o(k):o;return _.createElement(yy,Pa({},h,{"aria-current":C,className:P,ref:n,style:M,to:l,viewTransition:u}),typeof d=="function"?d(k):d)});var Zl;(function(t){t.UseScrollRestoration="useScrollRestoration",t.UseSubmit="useSubmit",t.UseSubmitFetcher="useSubmitFetcher",t.UseFetcher="useFetcher",t.useViewTransitionState="useViewTransitionState"})(Zl||(Zl={}));var Fd;(function(t){t.UseFetcher="useFetcher",t.UseFetchers="useFetchers",t.UseScrollRestoration="useScrollRestoration"})(Fd||(Fd={}));function by(t){let e=_.useContext(ao);return e||le(!1),e}function wy(t,e){let{target:n,replace:r,state:i,preventScrollReset:s,relative:a,viewTransition:o}=e===void 0?{}:e,l=tr(),u=$t(),d=lo(t,{relative:a});return _.useCallback(h=>{if(cy(h,n)){h.preventDefault();let f=r!==void 0?r:Ca(u)===Ca(d);l(t,{replace:f,state:i,preventScrollReset:s,relative:a,viewTransition:o})}},[u,l,d,r,i,n,t,s,a,o])}function Sy(t){let e=_.useRef(ql(t)),n=_.useRef(!1),r=$t(),i=_.useMemo(()=>uy(r.search,n.current?null:e.current),[r.search]),s=tr(),a=_.useCallback((o,l)=>{const u=ql(typeof o=="function"?o(i):o);n.current=!0,s("?"+u,l)},[s,i]);return[i,a]}function Ny(t,e){e===void 0&&(e={});let n=_.useContext(my);n==null&&le(!1);let{basename:r}=by(Zl.useViewTransitionState),i=lo(t,{relative:e.relative});if(!n.isTransitioning)return!1;let s=Lr(n.currentLocation.pathname,r)||n.currentLocation.pathname,a=Lr(n.nextLocation.pathname,r)||n.nextLocation.pathname;return Ql(i.pathname,a)!=null||Ql(i.pathname,s)!=null}const _y="modulepreload",ky=function(t){return"/"+t},zd={},iu=function(e,n,r){let i=Promise.resolve();if(n&&n.length>0){document.getElementsByTagName("link");const a=document.querySelector("meta[property=csp-nonce]"),o=(a==null?void 0:a.nonce)||(a==null?void 0:a.getAttribute("nonce"));i=Promise.allSettled(n.map(l=>{if(l=ky(l),l in zd)return;zd[l]=!0;const u=l.endsWith(".css"),d=u?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${l}"]${d}`))return;const h=document.createElement("link");if(h.rel=u?"stylesheet":_y,u||(h.as="script"),h.crossOrigin="",h.href=l,o&&h.setAttribute("nonce",o),document.head.appendChild(h),u)return new Promise((f,m)=>{h.addEventListener("load",f),h.addEventListener("error",()=>m(new Error(`Unable to preload CSS for ${l}`)))})}))}function s(a){const o=new Event("vite:preloadError",{cancelable:!0});if(o.payload=a,window.dispatchEvent(o),!o.defaultPrevented)throw a}return i.then(a=>{for(const o of a||[])o.status==="rejected"&&s(o.reason);return e().catch(s)})};/*! Capacitor: https://capacitorjs.com/ - MIT License */var Ar;(function(t){t.Unimplemented="UNIMPLEMENTED",t.Unavailable="UNAVAILABLE"})(Ar||(Ar={}));class Uo extends Error{constructor(e,n,r){super(e),this.message=e,this.code=n,this.data=r}}const jy=t=>{var e,n;return t!=null&&t.androidBridge?"android":!((n=(e=t==null?void 0:t.webkit)===null||e===void 0?void 0:e.messageHandlers)===null||n===void 0)&&n.bridge?"ios":"web"},Cy=t=>{const e=t.CapacitorCustomPlatform||null,n=t.Capacitor||{},r=n.Plugins=n.Plugins||{},i=()=>e!==null?e.name:jy(t),s=()=>i()!=="web",a=h=>{const f=u.get(h);return!!(f!=null&&f.platforms.has(i())||o(h))},o=h=>{var f;return(f=n.PluginHeaders)===null||f===void 0?void 0:f.find(m=>m.name===h)},l=h=>t.console.error(h),u=new Map,d=(h,f={})=>{const m=u.get(h);if(m)return console.warn(`Capacitor plugin "${h}" already registered. Cannot register plugins twice.`),m.proxy;const x=i(),v=o(h);let w;const g=async()=>(!w&&x in f?w=typeof f[x]=="function"?w=await f[x]():w=f[x]:e!==null&&!w&&"web"in f&&(w=typeof f.web=="function"?w=await f.web():w=f.web),w),p=(k,C)=>{var P,M;if(v){const D=v==null?void 0:v.methods.find($=>C===$.name);if(D)return D.rtype==="promise"?$=>n.nativePromise(h,C.toString(),$):($,U)=>n.nativeCallback(h,C.toString(),$,U);if(k)return(P=k[C])===null||P===void 0?void 0:P.bind(k)}else{if(k)return(M=k[C])===null||M===void 0?void 0:M.bind(k);throw new Uo(`"${h}" plugin is not implemented on ${x}`,Ar.Unimplemented)}},y=k=>{let C;const P=(...M)=>{const D=g().then($=>{const U=p($,k);if(U){const z=U(...M);return C=z==null?void 0:z.remove,z}else throw new Uo(`"${h}.${k}()" is not implemented on ${x}`,Ar.Unimplemented)});return k==="addListener"&&(D.remove=async()=>C()),D};return P.toString=()=>`${k.toString()}() { [capacitor code] }`,Object.defineProperty(P,"name",{value:k,writable:!1,configurable:!1}),P},b=y("addListener"),S=y("removeListener"),j=(k,C)=>{const P=b({eventName:k},C),M=async()=>{const $=await P;S({eventName:k,callbackId:$},C)},D=new Promise($=>P.then(()=>$({remove:M})));return D.remove=async()=>{console.warn("Using addListener() without 'await' is deprecated."),await M()},D},N=new Proxy({},{get(k,C){switch(C){case"$$typeof":return;case"toJSON":return()=>({});case"addListener":return v?j:b;case"removeListener":return S;default:return y(C)}}});return r[h]=N,u.set(h,{name:h,proxy:N,platforms:new Set([...Object.keys(f),...v?[x]:[]])}),N};return n.convertFileSrc||(n.convertFileSrc=h=>h),n.getPlatform=i,n.handleError=l,n.isNativePlatform=s,n.isPluginAvailable=a,n.registerPlugin=d,n.Exception=Uo,n.DEBUG=!!n.DEBUG,n.isLoggingEnabled=!!n.isLoggingEnabled,n},Py=t=>t.Capacitor=Cy(t),vt=Py(typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{}),Wr=vt.registerPlugin;class Bp{constructor(){this.listeners={},this.retainedEventArguments={},this.windowListeners={}}addListener(e,n){let r=!1;this.listeners[e]||(this.listeners[e]=[],r=!0),this.listeners[e].push(n);const s=this.windowListeners[e];s&&!s.registered&&this.addWindowListener(s),r&&this.sendRetainedArgumentsForEvent(e);const a=async()=>this.removeListener(e,n);return Promise.resolve({remove:a})}async removeAllListeners(){this.listeners={};for(const e in this.windowListeners)this.removeWindowListener(this.windowListeners[e]);this.windowListeners={}}notifyListeners(e,n,r){const i=this.listeners[e];if(!i){if(r){let s=this.retainedEventArguments[e];s||(s=[]),s.push(n),this.retainedEventArguments[e]=s}return}i.forEach(s=>s(n))}hasListeners(e){return!!this.listeners[e].length}registerWindowListener(e,n){this.windowListeners[n]={registered:!1,windowEventName:e,pluginEventName:n,handler:r=>{this.notifyListeners(n,r)}}}unimplemented(e="not implemented"){return new vt.Exception(e,Ar.Unimplemented)}unavailable(e="not available"){return new vt.Exception(e,Ar.Unavailable)}async removeListener(e,n){const r=this.listeners[e];if(!r)return;const i=r.indexOf(n);this.listeners[e].splice(i,1),this.listeners[e].length||this.removeWindowListener(this.windowListeners[e])}addWindowListener(e){window.addEventListener(e.windowEventName,e.handler),e.registered=!0}removeWindowListener(e){e&&(window.removeEventListener(e.windowEventName,e.handler),e.registered=!1)}sendRetainedArgumentsForEvent(e){const n=this.retainedEventArguments[e];n&&(delete this.retainedEventArguments[e],n.forEach(r=>{this.notifyListeners(e,r)}))}}const Id=t=>encodeURIComponent(t).replace(/%(2[346B]|5E|60|7C)/g,decodeURIComponent).replace(/[()]/g,escape),Ud=t=>t.replace(/(%[\dA-F]{2})+/gi,decodeURIComponent);class Ey extends Bp{async getCookies(){const e=document.cookie,n={};return e.split(";").forEach(r=>{if(r.length<=0)return;let[i,s]=r.replace(/=/,"CAP_COOKIE").split("CAP_COOKIE");i=Ud(i).trim(),s=Ud(s).trim(),n[i]=s}),n}async setCookie(e){try{const n=Id(e.key),r=Id(e.value),i=`; expires=${(e.expires||"").replace("expires=","")}`,s=(e.path||"/").replace("path=",""),a=e.url!=null&&e.url.length>0?`domain=${e.url}`:"";document.cookie=`${n}=${r||""}${i}; path=${s}; ${a};`}catch(n){return Promise.reject(n)}}async deleteCookie(e){try{document.cookie=`${e.key}=; Max-Age=0`}catch(n){return Promise.reject(n)}}async clearCookies(){try{const e=document.cookie.split(";")||[];for(const n of e)document.cookie=n.replace(/^ +/,"").replace(/=.*/,`=;expires=${new Date().toUTCString()};path=/`)}catch(e){return Promise.reject(e)}}async clearAllCookies(){try{await this.clearCookies()}catch(e){return Promise.reject(e)}}}Wr("CapacitorCookies",{web:()=>new Ey});const Ty=async t=>new Promise((e,n)=>{const r=new FileReader;r.onload=()=>{const i=r.result;e(i.indexOf(",")>=0?i.split(",")[1]:i)},r.onerror=i=>n(i),r.readAsDataURL(t)}),My=(t={})=>{const e=Object.keys(t);return Object.keys(t).map(i=>i.toLocaleLowerCase()).reduce((i,s,a)=>(i[s]=t[e[a]],i),{})},$y=(t,e=!0)=>t?Object.entries(t).reduce((r,i)=>{const[s,a]=i;let o,l;return Array.isArray(a)?(l="",a.forEach(u=>{o=e?encodeURIComponent(u):u,l+=`${s}=${o}&`}),l.slice(0,-1)):(o=e?encodeURIComponent(a):a,l=`${s}=${o}`),`${r}&${l}`},"").substr(1):null,Ry=(t,e={})=>{const n=Object.assign({method:t.method||"GET",headers:t.headers},e),i=My(t.headers)["content-type"]||"";if(typeof t.data=="string")n.body=t.data;else if(i.includes("application/x-www-form-urlencoded")){const s=new URLSearchParams;for(const[a,o]of Object.entries(t.data||{}))s.set(a,o);n.body=s.toString()}else if(i.includes("multipart/form-data")||t.data instanceof FormData){const s=new FormData;if(t.data instanceof FormData)t.data.forEach((o,l)=>{s.append(l,o)});else for(const o of Object.keys(t.data))s.append(o,t.data[o]);n.body=s;const a=new Headers(n.headers);a.delete("content-type"),n.headers=a}else(i.includes("application/json")||typeof t.data=="object")&&(n.body=JSON.stringify(t.data));return n};class Dy extends Bp{async request(e){const n=Ry(e,e.webFetchExtra),r=$y(e.params,e.shouldEncodeUrlParams),i=r?`${e.url}?${r}`:e.url,s=await fetch(i,n),a=s.headers.get("content-type")||"";let{responseType:o="text"}=s.ok?e:{};a.includes("application/json")&&(o="json");let l,u;switch(o){case"arraybuffer":case"blob":u=await s.blob(),l=await Ty(u);break;case"json":l=await s.json();break;case"document":case"text":default:l=await s.text()}const d={};return s.headers.forEach((h,f)=>{d[f]=h}),{data:l,headers:d,status:s.status,url:s.url}}async get(e){return this.request(Object.assign(Object.assign({},e),{method:"GET"}))}async post(e){return this.request(Object.assign(Object.assign({},e),{method:"POST"}))}async put(e){return this.request(Object.assign(Object.assign({},e),{method:"PUT"}))}async patch(e){return this.request(Object.assign(Object.assign({},e),{method:"PATCH"}))}async delete(e){return this.request(Object.assign(Object.assign({},e),{method:"DELETE"}))}}Wr("CapacitorHttp",{web:()=>new Dy});var Bd;(function(t){t[t.Sunday=1]="Sunday",t[t.Monday=2]="Monday",t[t.Tuesday=3]="Tuesday",t[t.Wednesday=4]="Wednesday",t[t.Thursday=5]="Thursday",t[t.Friday=6]="Friday",t[t.Saturday=7]="Saturday"})(Bd||(Bd={}));const ft=Wr("LocalNotifications",{web:()=>iu(()=>import("./web-lwDDBSgq.js"),[]).then(t=>new t.LocalNotificationsWeb)}),Oy=(t,e,n,r)=>{const i=_.useRef(null),s=_.useRef(!1);return _.useEffect(()=>{if(i.current&&(clearInterval(i.current),i.current=null),!t){console.log("Notifications: Disabled, cleaning up");return}const o=async()=>{try{console.log("Notifications: Setting up for platform:",vt.getPlatform()),vt.isNativePlatform()?(console.log("Notifications: Native platform detected"),await l()):(console.log("Notifications: Web platform detected, using fallback"),d())}catch(f){console.error("Notifications: Setup error:",f),d()}},l=async()=>{try{console.log("Notifications: Requesting permissions...");const f=await ft.requestPermissions();if(console.log("Notifications: Permission result:",f),f.display!=="granted"){console.log("Notifications: Permission denied");return}console.log("Notifications: Permission granted, setting up daily reminder"),await u(),s.current||(await h(),s.current=!0)}catch(f){throw console.error("Notifications: Native setup error:",f),f}},u=async()=>{try{console.log("Notifications: Scheduling daily notification...");try{await ft.cancel({notifications:[{id:1}]}),console.log("Notifications: Cancelled existing notifications")}catch{console.log("Notifications: No existing notifications to cancel")}const[f,m]=e.split(":").map(Number);console.log("Notifications: Parsed time:",f,":",m);const x=new Date,v=new Date;v.setHours(f,m,0,0),v<=x&&(v.setDate(v.getDate()+1),console.log("Notifications: Time has passed today, scheduling for tomorrow")),console.log("Notifications: Scheduling for:",v.toLocaleString());const w={notifications:[{title:"⚡ Prepaid Meter Reminder",body:"Don't forget to record your electricity usage today!",id:1,schedule:{at:v,repeats:!0,every:"day"},sound:"default",attachments:null,actionTypeId:"",extra:{type:"daily_reminder"}}]};await ft.schedule(w),console.log("Notifications: Daily reminder scheduled successfully for",v.toLocaleString());const g=await ft.getPending();console.log("Notifications: Pending notifications:",g.notifications.length)}catch(f){throw console.error("Notifications: Scheduling error:",f),f}},d=()=>{console.log("Notifications: Setting up web notifications"),(async()=>{if("Notification"in window&&Notification.permission==="default"){console.log("Notifications: Requesting web permission...");const x=await Notification.requestPermission();console.log("Notifications: Web permission result:",x)}})();const m=()=>{const x=new Date,[v,w]=e.split(":").map(Number),g=new Date;g.setHours(v,w,0,0);const p=new Date(g);p.setMinutes(p.getMinutes()-2);const y=new Date(g);y.setMinutes(y.getMinutes()+3);const b=x.toDateString(),S=n?new Date(n).toDateString():null;if(console.log("Notifications: Checking web notification conditions..."),console.log("Current time:",x.toLocaleTimeString()),console.log("Notification window:",p.toLocaleTimeString(),"-",y.toLocaleTimeString()),console.log("Last notification date:",S),console.log("Today:",b),x>=p&&x<=y&&S!==b)if(console.log("Notifications: Conditions met, attempting to send notification"),"Notification"in window&&Notification.permission==="granted"){console.log("Notifications: Sending web notification");const j=new Notification("⚡ Prepaid Meter Reminder",{body:"Don't forget to record your electricity usage today!",icon:"/favicon.ico",badge:"/favicon.ico",tag:"daily-reminder",requireInteraction:!1,silent:!1});setTimeout(()=>{j.close()},1e4),r({type:"UPDATE_SETTINGS",payload:{lastNotificationDate:x.toISOString()}}),console.log("Notifications: Web notification sent successfully")}else console.log("Notifications: Web notification permission not granted")};m(),i.current=setInterval(m,3e4),console.log("Notifications: Web notification checker started")},h=async()=>{if(vt.isNativePlatform())try{console.log("Notifications: Setting up listeners..."),await ft.removeAllListeners(),await ft.addListener("localNotificationActionPerformed",f=>{console.log("Notification action performed:",f),r({type:"UPDATE_SETTINGS",payload:{lastNotificationDate:new Date().toISOString()}})}),await ft.addListener("localNotificationReceived",f=>{console.log("Notification received in foreground:",f),r({type:"UPDATE_SETTINGS",payload:{lastNotificationDate:new Date().toISOString()}})}),console.log("Notifications: Listeners setup complete")}catch(f){console.error("Notifications: Listener setup error:",f)}};return o(),()=>{console.log("Notifications: Cleaning up..."),i.current&&(clearInterval(i.current),i.current=null),vt.isNativePlatform()&&s.current&&(ft.removeAllListeners(),s.current=!1)}},[t,e,r]),{testNotification:async()=>{try{if(console.log("Notifications: Testing notification..."),!vt.isNativePlatform()){if(console.log("Notifications: Testing web notification"),"Notification"in window){if(Notification.permission==="default"){console.log("Notifications: Requesting permission for test...");const u=await Notification.requestPermission();console.log("Notifications: Permission result:",u)}if(Notification.permission==="granted"){console.log("Notifications: Sending test web notification");const u=new Notification("⚡ Test Notification",{body:"This is a test notification from your Prepaid Meter app!",icon:"/favicon.ico",tag:"test-notification",requireInteraction:!1});return setTimeout(()=>{u.close()},5e3),!0}else console.log("Notifications: Web permission not granted")}else console.log("Notifications: Web notifications not supported");return!1}console.log("Notifications: Testing native notification");const o=await ft.requestPermissions();if(console.log("Notifications: Native permission result:",o),o.display!=="granted")return console.log("Test notification: Native permission denied"),!1;try{await ft.cancel({notifications:[{id:999}]})}catch{}const l={notifications:[{title:"⚡ Test Notification",body:"This is a test notification from your Prepaid Meter app!",id:999,schedule:{at:new Date(Date.now()+2e3)},sound:"default",attachments:null,actionTypeId:"",extra:{type:"test_notification"}}]};return await ft.schedule(l),console.log("Notifications: Test notification scheduled for 2 seconds from now"),!0}catch(o){return console.error("Test notification error:",o),!1}}}};function Ea(t){"@babel/helpers - typeof";return Ea=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Ea(t)}function Qn(t){if(t===null||t===!0||t===!1)return NaN;var e=Number(t);return isNaN(e)?e:e<0?Math.ceil(e):Math.floor(e)}function Fe(t,e){if(e.length<t)throw new TypeError(t+" argument"+(t>1?"s":"")+" required, but only "+e.length+" present")}function Rt(t){Fe(1,arguments);var e=Object.prototype.toString.call(t);return t instanceof Date||Ea(t)==="object"&&e==="[object Date]"?new Date(t.getTime()):typeof t=="number"||e==="[object Number]"?new Date(t):((typeof t=="string"||e==="[object String]")&&typeof console<"u"&&(console.warn("Starting with v2.0.0-beta.1 date-fns doesn't accept strings as date arguments. Please use `parseISO` to parse strings. See: https://github.com/date-fns/date-fns/blob/master/docs/upgradeGuide.md#string-arguments"),console.warn(new Error().stack)),new Date(NaN))}function Ly(t,e){Fe(2,arguments);var n=Rt(t).getTime(),r=Qn(e);return new Date(n+r)}var Ay={};function co(){return Ay}function Fy(t){var e=new Date(Date.UTC(t.getFullYear(),t.getMonth(),t.getDate(),t.getHours(),t.getMinutes(),t.getSeconds(),t.getMilliseconds()));return e.setUTCFullYear(t.getFullYear()),t.getTime()-e.getTime()}function zy(t){return Fe(1,arguments),t instanceof Date||Ea(t)==="object"&&Object.prototype.toString.call(t)==="[object Date]"}function Iy(t){if(Fe(1,arguments),!zy(t)&&typeof t!="number")return!1;var e=Rt(t);return!isNaN(Number(e))}function Uy(t,e){Fe(2,arguments);var n=Qn(e);return Ly(t,-n)}var By=864e5;function Hy(t){Fe(1,arguments);var e=Rt(t),n=e.getTime();e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0);var r=e.getTime(),i=n-r;return Math.floor(i/By)+1}function Ta(t){Fe(1,arguments);var e=1,n=Rt(t),r=n.getUTCDay(),i=(r<e?7:0)+r-e;return n.setUTCDate(n.getUTCDate()-i),n.setUTCHours(0,0,0,0),n}function Hp(t){Fe(1,arguments);var e=Rt(t),n=e.getUTCFullYear(),r=new Date(0);r.setUTCFullYear(n+1,0,4),r.setUTCHours(0,0,0,0);var i=Ta(r),s=new Date(0);s.setUTCFullYear(n,0,4),s.setUTCHours(0,0,0,0);var a=Ta(s);return e.getTime()>=i.getTime()?n+1:e.getTime()>=a.getTime()?n:n-1}function Wy(t){Fe(1,arguments);var e=Hp(t),n=new Date(0);n.setUTCFullYear(e,0,4),n.setUTCHours(0,0,0,0);var r=Ta(n);return r}var Vy=6048e5;function Yy(t){Fe(1,arguments);var e=Rt(t),n=Ta(e).getTime()-Wy(e).getTime();return Math.round(n/Vy)+1}function Ma(t,e){var n,r,i,s,a,o,l,u;Fe(1,arguments);var d=co(),h=Qn((n=(r=(i=(s=e==null?void 0:e.weekStartsOn)!==null&&s!==void 0?s:e==null||(a=e.locale)===null||a===void 0||(o=a.options)===null||o===void 0?void 0:o.weekStartsOn)!==null&&i!==void 0?i:d.weekStartsOn)!==null&&r!==void 0?r:(l=d.locale)===null||l===void 0||(u=l.options)===null||u===void 0?void 0:u.weekStartsOn)!==null&&n!==void 0?n:0);if(!(h>=0&&h<=6))throw new RangeError("weekStartsOn must be between 0 and 6 inclusively");var f=Rt(t),m=f.getUTCDay(),x=(m<h?7:0)+m-h;return f.setUTCDate(f.getUTCDate()-x),f.setUTCHours(0,0,0,0),f}function Wp(t,e){var n,r,i,s,a,o,l,u;Fe(1,arguments);var d=Rt(t),h=d.getUTCFullYear(),f=co(),m=Qn((n=(r=(i=(s=e==null?void 0:e.firstWeekContainsDate)!==null&&s!==void 0?s:e==null||(a=e.locale)===null||a===void 0||(o=a.options)===null||o===void 0?void 0:o.firstWeekContainsDate)!==null&&i!==void 0?i:f.firstWeekContainsDate)!==null&&r!==void 0?r:(l=f.locale)===null||l===void 0||(u=l.options)===null||u===void 0?void 0:u.firstWeekContainsDate)!==null&&n!==void 0?n:1);if(!(m>=1&&m<=7))throw new RangeError("firstWeekContainsDate must be between 1 and 7 inclusively");var x=new Date(0);x.setUTCFullYear(h+1,0,m),x.setUTCHours(0,0,0,0);var v=Ma(x,e),w=new Date(0);w.setUTCFullYear(h,0,m),w.setUTCHours(0,0,0,0);var g=Ma(w,e);return d.getTime()>=v.getTime()?h+1:d.getTime()>=g.getTime()?h:h-1}function Xy(t,e){var n,r,i,s,a,o,l,u;Fe(1,arguments);var d=co(),h=Qn((n=(r=(i=(s=e==null?void 0:e.firstWeekContainsDate)!==null&&s!==void 0?s:e==null||(a=e.locale)===null||a===void 0||(o=a.options)===null||o===void 0?void 0:o.firstWeekContainsDate)!==null&&i!==void 0?i:d.firstWeekContainsDate)!==null&&r!==void 0?r:(l=d.locale)===null||l===void 0||(u=l.options)===null||u===void 0?void 0:u.firstWeekContainsDate)!==null&&n!==void 0?n:1),f=Wp(t,e),m=new Date(0);m.setUTCFullYear(f,0,h),m.setUTCHours(0,0,0,0);var x=Ma(m,e);return x}var Ky=6048e5;function Qy(t,e){Fe(1,arguments);var n=Rt(t),r=Ma(n,e).getTime()-Xy(n,e).getTime();return Math.round(r/Ky)+1}function K(t,e){for(var n=t<0?"-":"",r=Math.abs(t).toString();r.length<e;)r="0"+r;return n+r}var qt={y:function(e,n){var r=e.getUTCFullYear(),i=r>0?r:1-r;return K(n==="yy"?i%100:i,n.length)},M:function(e,n){var r=e.getUTCMonth();return n==="M"?String(r+1):K(r+1,2)},d:function(e,n){return K(e.getUTCDate(),n.length)},a:function(e,n){var r=e.getUTCHours()/12>=1?"pm":"am";switch(n){case"a":case"aa":return r.toUpperCase();case"aaa":return r;case"aaaaa":return r[0];case"aaaa":default:return r==="am"?"a.m.":"p.m."}},h:function(e,n){return K(e.getUTCHours()%12||12,n.length)},H:function(e,n){return K(e.getUTCHours(),n.length)},m:function(e,n){return K(e.getUTCMinutes(),n.length)},s:function(e,n){return K(e.getUTCSeconds(),n.length)},S:function(e,n){var r=n.length,i=e.getUTCMilliseconds(),s=Math.floor(i*Math.pow(10,r-3));return K(s,n.length)}},rr={midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},Gy={G:function(e,n,r){var i=e.getUTCFullYear()>0?1:0;switch(n){case"G":case"GG":case"GGG":return r.era(i,{width:"abbreviated"});case"GGGGG":return r.era(i,{width:"narrow"});case"GGGG":default:return r.era(i,{width:"wide"})}},y:function(e,n,r){if(n==="yo"){var i=e.getUTCFullYear(),s=i>0?i:1-i;return r.ordinalNumber(s,{unit:"year"})}return qt.y(e,n)},Y:function(e,n,r,i){var s=Wp(e,i),a=s>0?s:1-s;if(n==="YY"){var o=a%100;return K(o,2)}return n==="Yo"?r.ordinalNumber(a,{unit:"year"}):K(a,n.length)},R:function(e,n){var r=Hp(e);return K(r,n.length)},u:function(e,n){var r=e.getUTCFullYear();return K(r,n.length)},Q:function(e,n,r){var i=Math.ceil((e.getUTCMonth()+1)/3);switch(n){case"Q":return String(i);case"QQ":return K(i,2);case"Qo":return r.ordinalNumber(i,{unit:"quarter"});case"QQQ":return r.quarter(i,{width:"abbreviated",context:"formatting"});case"QQQQQ":return r.quarter(i,{width:"narrow",context:"formatting"});case"QQQQ":default:return r.quarter(i,{width:"wide",context:"formatting"})}},q:function(e,n,r){var i=Math.ceil((e.getUTCMonth()+1)/3);switch(n){case"q":return String(i);case"qq":return K(i,2);case"qo":return r.ordinalNumber(i,{unit:"quarter"});case"qqq":return r.quarter(i,{width:"abbreviated",context:"standalone"});case"qqqqq":return r.quarter(i,{width:"narrow",context:"standalone"});case"qqqq":default:return r.quarter(i,{width:"wide",context:"standalone"})}},M:function(e,n,r){var i=e.getUTCMonth();switch(n){case"M":case"MM":return qt.M(e,n);case"Mo":return r.ordinalNumber(i+1,{unit:"month"});case"MMM":return r.month(i,{width:"abbreviated",context:"formatting"});case"MMMMM":return r.month(i,{width:"narrow",context:"formatting"});case"MMMM":default:return r.month(i,{width:"wide",context:"formatting"})}},L:function(e,n,r){var i=e.getUTCMonth();switch(n){case"L":return String(i+1);case"LL":return K(i+1,2);case"Lo":return r.ordinalNumber(i+1,{unit:"month"});case"LLL":return r.month(i,{width:"abbreviated",context:"standalone"});case"LLLLL":return r.month(i,{width:"narrow",context:"standalone"});case"LLLL":default:return r.month(i,{width:"wide",context:"standalone"})}},w:function(e,n,r,i){var s=Qy(e,i);return n==="wo"?r.ordinalNumber(s,{unit:"week"}):K(s,n.length)},I:function(e,n,r){var i=Yy(e);return n==="Io"?r.ordinalNumber(i,{unit:"week"}):K(i,n.length)},d:function(e,n,r){return n==="do"?r.ordinalNumber(e.getUTCDate(),{unit:"date"}):qt.d(e,n)},D:function(e,n,r){var i=Hy(e);return n==="Do"?r.ordinalNumber(i,{unit:"dayOfYear"}):K(i,n.length)},E:function(e,n,r){var i=e.getUTCDay();switch(n){case"E":case"EE":case"EEE":return r.day(i,{width:"abbreviated",context:"formatting"});case"EEEEE":return r.day(i,{width:"narrow",context:"formatting"});case"EEEEEE":return r.day(i,{width:"short",context:"formatting"});case"EEEE":default:return r.day(i,{width:"wide",context:"formatting"})}},e:function(e,n,r,i){var s=e.getUTCDay(),a=(s-i.weekStartsOn+8)%7||7;switch(n){case"e":return String(a);case"ee":return K(a,2);case"eo":return r.ordinalNumber(a,{unit:"day"});case"eee":return r.day(s,{width:"abbreviated",context:"formatting"});case"eeeee":return r.day(s,{width:"narrow",context:"formatting"});case"eeeeee":return r.day(s,{width:"short",context:"formatting"});case"eeee":default:return r.day(s,{width:"wide",context:"formatting"})}},c:function(e,n,r,i){var s=e.getUTCDay(),a=(s-i.weekStartsOn+8)%7||7;switch(n){case"c":return String(a);case"cc":return K(a,n.length);case"co":return r.ordinalNumber(a,{unit:"day"});case"ccc":return r.day(s,{width:"abbreviated",context:"standalone"});case"ccccc":return r.day(s,{width:"narrow",context:"standalone"});case"cccccc":return r.day(s,{width:"short",context:"standalone"});case"cccc":default:return r.day(s,{width:"wide",context:"standalone"})}},i:function(e,n,r){var i=e.getUTCDay(),s=i===0?7:i;switch(n){case"i":return String(s);case"ii":return K(s,n.length);case"io":return r.ordinalNumber(s,{unit:"day"});case"iii":return r.day(i,{width:"abbreviated",context:"formatting"});case"iiiii":return r.day(i,{width:"narrow",context:"formatting"});case"iiiiii":return r.day(i,{width:"short",context:"formatting"});case"iiii":default:return r.day(i,{width:"wide",context:"formatting"})}},a:function(e,n,r){var i=e.getUTCHours(),s=i/12>=1?"pm":"am";switch(n){case"a":case"aa":return r.dayPeriod(s,{width:"abbreviated",context:"formatting"});case"aaa":return r.dayPeriod(s,{width:"abbreviated",context:"formatting"}).toLowerCase();case"aaaaa":return r.dayPeriod(s,{width:"narrow",context:"formatting"});case"aaaa":default:return r.dayPeriod(s,{width:"wide",context:"formatting"})}},b:function(e,n,r){var i=e.getUTCHours(),s;switch(i===12?s=rr.noon:i===0?s=rr.midnight:s=i/12>=1?"pm":"am",n){case"b":case"bb":return r.dayPeriod(s,{width:"abbreviated",context:"formatting"});case"bbb":return r.dayPeriod(s,{width:"abbreviated",context:"formatting"}).toLowerCase();case"bbbbb":return r.dayPeriod(s,{width:"narrow",context:"formatting"});case"bbbb":default:return r.dayPeriod(s,{width:"wide",context:"formatting"})}},B:function(e,n,r){var i=e.getUTCHours(),s;switch(i>=17?s=rr.evening:i>=12?s=rr.afternoon:i>=4?s=rr.morning:s=rr.night,n){case"B":case"BB":case"BBB":return r.dayPeriod(s,{width:"abbreviated",context:"formatting"});case"BBBBB":return r.dayPeriod(s,{width:"narrow",context:"formatting"});case"BBBB":default:return r.dayPeriod(s,{width:"wide",context:"formatting"})}},h:function(e,n,r){if(n==="ho"){var i=e.getUTCHours()%12;return i===0&&(i=12),r.ordinalNumber(i,{unit:"hour"})}return qt.h(e,n)},H:function(e,n,r){return n==="Ho"?r.ordinalNumber(e.getUTCHours(),{unit:"hour"}):qt.H(e,n)},K:function(e,n,r){var i=e.getUTCHours()%12;return n==="Ko"?r.ordinalNumber(i,{unit:"hour"}):K(i,n.length)},k:function(e,n,r){var i=e.getUTCHours();return i===0&&(i=24),n==="ko"?r.ordinalNumber(i,{unit:"hour"}):K(i,n.length)},m:function(e,n,r){return n==="mo"?r.ordinalNumber(e.getUTCMinutes(),{unit:"minute"}):qt.m(e,n)},s:function(e,n,r){return n==="so"?r.ordinalNumber(e.getUTCSeconds(),{unit:"second"}):qt.s(e,n)},S:function(e,n){return qt.S(e,n)},X:function(e,n,r,i){var s=i._originalDate||e,a=s.getTimezoneOffset();if(a===0)return"Z";switch(n){case"X":return Wd(a);case"XXXX":case"XX":return On(a);case"XXXXX":case"XXX":default:return On(a,":")}},x:function(e,n,r,i){var s=i._originalDate||e,a=s.getTimezoneOffset();switch(n){case"x":return Wd(a);case"xxxx":case"xx":return On(a);case"xxxxx":case"xxx":default:return On(a,":")}},O:function(e,n,r,i){var s=i._originalDate||e,a=s.getTimezoneOffset();switch(n){case"O":case"OO":case"OOO":return"GMT"+Hd(a,":");case"OOOO":default:return"GMT"+On(a,":")}},z:function(e,n,r,i){var s=i._originalDate||e,a=s.getTimezoneOffset();switch(n){case"z":case"zz":case"zzz":return"GMT"+Hd(a,":");case"zzzz":default:return"GMT"+On(a,":")}},t:function(e,n,r,i){var s=i._originalDate||e,a=Math.floor(s.getTime()/1e3);return K(a,n.length)},T:function(e,n,r,i){var s=i._originalDate||e,a=s.getTime();return K(a,n.length)}};function Hd(t,e){var n=t>0?"-":"+",r=Math.abs(t),i=Math.floor(r/60),s=r%60;if(s===0)return n+String(i);var a=e;return n+String(i)+a+K(s,2)}function Wd(t,e){if(t%60===0){var n=t>0?"-":"+";return n+K(Math.abs(t)/60,2)}return On(t,e)}function On(t,e){var n=e||"",r=t>0?"-":"+",i=Math.abs(t),s=K(Math.floor(i/60),2),a=K(i%60,2);return r+s+n+a}var Vd=function(e,n){switch(e){case"P":return n.date({width:"short"});case"PP":return n.date({width:"medium"});case"PPP":return n.date({width:"long"});case"PPPP":default:return n.date({width:"full"})}},Vp=function(e,n){switch(e){case"p":return n.time({width:"short"});case"pp":return n.time({width:"medium"});case"ppp":return n.time({width:"long"});case"pppp":default:return n.time({width:"full"})}},qy=function(e,n){var r=e.match(/(P+)(p+)?/)||[],i=r[1],s=r[2];if(!s)return Vd(e,n);var a;switch(i){case"P":a=n.dateTime({width:"short"});break;case"PP":a=n.dateTime({width:"medium"});break;case"PPP":a=n.dateTime({width:"long"});break;case"PPPP":default:a=n.dateTime({width:"full"});break}return a.replace("{{date}}",Vd(i,n)).replace("{{time}}",Vp(s,n))},Zy={p:Vp,P:qy},Jy=["D","DD"],eb=["YY","YYYY"];function tb(t){return Jy.indexOf(t)!==-1}function nb(t){return eb.indexOf(t)!==-1}function Yd(t,e,n){if(t==="YYYY")throw new RangeError("Use `yyyy` instead of `YYYY` (in `".concat(e,"`) for formatting years to the input `").concat(n,"`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md"));if(t==="YY")throw new RangeError("Use `yy` instead of `YY` (in `".concat(e,"`) for formatting years to the input `").concat(n,"`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md"));if(t==="D")throw new RangeError("Use `d` instead of `D` (in `".concat(e,"`) for formatting days of the month to the input `").concat(n,"`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md"));if(t==="DD")throw new RangeError("Use `dd` instead of `DD` (in `".concat(e,"`) for formatting days of the month to the input `").concat(n,"`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md"))}var rb={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}},ib=function(e,n,r){var i,s=rb[e];return typeof s=="string"?i=s:n===1?i=s.one:i=s.other.replace("{{count}}",n.toString()),r!=null&&r.addSuffix?r.comparison&&r.comparison>0?"in "+i:i+" ago":i};function Bo(t){return function(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},n=e.width?String(e.width):t.defaultWidth,r=t.formats[n]||t.formats[t.defaultWidth];return r}}var sb={full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},ab={full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},ob={full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},lb={date:Bo({formats:sb,defaultWidth:"full"}),time:Bo({formats:ab,defaultWidth:"full"}),dateTime:Bo({formats:ob,defaultWidth:"full"})},cb={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"},ub=function(e,n,r,i){return cb[e]};function ti(t){return function(e,n){var r=n!=null&&n.context?String(n.context):"standalone",i;if(r==="formatting"&&t.formattingValues){var s=t.defaultFormattingWidth||t.defaultWidth,a=n!=null&&n.width?String(n.width):s;i=t.formattingValues[a]||t.formattingValues[s]}else{var o=t.defaultWidth,l=n!=null&&n.width?String(n.width):t.defaultWidth;i=t.values[l]||t.values[o]}var u=t.argumentCallback?t.argumentCallback(e):e;return i[u]}}var db={narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},hb={narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},fb={narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},mb={narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},pb={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},gb={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},xb=function(e,n){var r=Number(e),i=r%100;if(i>20||i<10)switch(i%10){case 1:return r+"st";case 2:return r+"nd";case 3:return r+"rd"}return r+"th"},vb={ordinalNumber:xb,era:ti({values:db,defaultWidth:"wide"}),quarter:ti({values:hb,defaultWidth:"wide",argumentCallback:function(e){return e-1}}),month:ti({values:fb,defaultWidth:"wide"}),day:ti({values:mb,defaultWidth:"wide"}),dayPeriod:ti({values:pb,defaultWidth:"wide",formattingValues:gb,defaultFormattingWidth:"wide"})};function ni(t){return function(e){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},r=n.width,i=r&&t.matchPatterns[r]||t.matchPatterns[t.defaultMatchWidth],s=e.match(i);if(!s)return null;var a=s[0],o=r&&t.parsePatterns[r]||t.parsePatterns[t.defaultParseWidth],l=Array.isArray(o)?bb(o,function(h){return h.test(a)}):yb(o,function(h){return h.test(a)}),u;u=t.valueCallback?t.valueCallback(l):l,u=n.valueCallback?n.valueCallback(u):u;var d=e.slice(a.length);return{value:u,rest:d}}}function yb(t,e){for(var n in t)if(t.hasOwnProperty(n)&&e(t[n]))return n}function bb(t,e){for(var n=0;n<t.length;n++)if(e(t[n]))return n}function wb(t){return function(e){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},r=e.match(t.matchPattern);if(!r)return null;var i=r[0],s=e.match(t.parsePattern);if(!s)return null;var a=t.valueCallback?t.valueCallback(s[0]):s[0];a=n.valueCallback?n.valueCallback(a):a;var o=e.slice(i.length);return{value:a,rest:o}}}var Sb=/^(\d+)(th|st|nd|rd)?/i,Nb=/\d+/i,_b={narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},kb={any:[/^b/i,/^(a|c)/i]},jb={narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},Cb={any:[/1/i,/2/i,/3/i,/4/i]},Pb={narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},Eb={narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},Tb={narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},Mb={narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},$b={narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},Rb={any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},Db={ordinalNumber:wb({matchPattern:Sb,parsePattern:Nb,valueCallback:function(e){return parseInt(e,10)}}),era:ni({matchPatterns:_b,defaultMatchWidth:"wide",parsePatterns:kb,defaultParseWidth:"any"}),quarter:ni({matchPatterns:jb,defaultMatchWidth:"wide",parsePatterns:Cb,defaultParseWidth:"any",valueCallback:function(e){return e+1}}),month:ni({matchPatterns:Pb,defaultMatchWidth:"wide",parsePatterns:Eb,defaultParseWidth:"any"}),day:ni({matchPatterns:Tb,defaultMatchWidth:"wide",parsePatterns:Mb,defaultParseWidth:"any"}),dayPeriod:ni({matchPatterns:$b,defaultMatchWidth:"any",parsePatterns:Rb,defaultParseWidth:"any"})},Ob={code:"en-US",formatDistance:ib,formatLong:lb,formatRelative:ub,localize:vb,match:Db,options:{weekStartsOn:0,firstWeekContainsDate:1}},Lb=/[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g,Ab=/P+p+|P+|p+|''|'(''|[^'])+('|$)|./g,Fb=/^'([^]*?)'?$/,zb=/''/g,Ib=/[a-zA-Z]/;function Xd(t,e,n){var r,i,s,a,o,l,u,d,h,f,m,x,v,w;Fe(2,arguments);var g=String(e),p=co(),y=(r=(i=void 0)!==null&&i!==void 0?i:p.locale)!==null&&r!==void 0?r:Ob,b=Qn((s=(a=(o=(l=void 0)!==null&&l!==void 0?l:void 0)!==null&&o!==void 0?o:p.firstWeekContainsDate)!==null&&a!==void 0?a:(u=p.locale)===null||u===void 0||(d=u.options)===null||d===void 0?void 0:d.firstWeekContainsDate)!==null&&s!==void 0?s:1);if(!(b>=1&&b<=7))throw new RangeError("firstWeekContainsDate must be between 1 and 7 inclusively");var S=Qn((h=(f=(m=(x=void 0)!==null&&x!==void 0?x:void 0)!==null&&m!==void 0?m:p.weekStartsOn)!==null&&f!==void 0?f:(v=p.locale)===null||v===void 0||(w=v.options)===null||w===void 0?void 0:w.weekStartsOn)!==null&&h!==void 0?h:0);if(!(S>=0&&S<=6))throw new RangeError("weekStartsOn must be between 0 and 6 inclusively");if(!y.localize)throw new RangeError("locale must contain localize property");if(!y.formatLong)throw new RangeError("locale must contain formatLong property");var j=Rt(t);if(!Iy(j))throw new RangeError("Invalid time value");var N=Fy(j),k=Uy(j,N),C={firstWeekContainsDate:b,weekStartsOn:S,locale:y,_originalDate:j},P=g.match(Ab).map(function(M){var D=M[0];if(D==="p"||D==="P"){var $=Zy[D];return $(M,y.formatLong)}return M}).join("").match(Lb).map(function(M){if(M==="''")return"'";var D=M[0];if(D==="'")return Ub(M);var $=Gy[D];if($)return nb(M)&&Yd(M,e,String(t)),tb(M)&&Yd(M,e,String(t)),$(k,M,y.localize,C);if(D.match(Ib))throw new RangeError("Format string contains an unescaped latin alphabet character `"+D+"`");return M}).join("");return P}function Ub(t){var e=t.match(Fb);return e?e[1].replace(zb,"'"):t}const Yp=_.createContext(),Jl={version:"1.1.0",currentUnits:0,previousUnits:0,unitCost:0,thresholdLimit:0,currency:"ZAR",currencySymbol:"R",customCurrencyName:"",customCurrencySymbol:"",unitName:"kWh",customUnitName:"",purchases:[],usageHistory:[],isInitialized:!1,lastResetDate:null,lastMonthlyReset:null,notificationsEnabled:!1,notificationTime:"18:00",lastNotificationDate:null};function Bb(t,e){switch(e.type){case"INITIALIZE_APP":return{...t,currentUnits:e.payload.initialUnits,previousUnits:e.payload.initialUnits,unitCost:e.payload.unitCost,isInitialized:!0,lastResetDate:new Date().toISOString()};case"ADD_PURCHASE":{const n={id:Date.now(),date:new Date().toISOString(),currency:e.payload.currency,units:e.payload.units,unitCost:t.unitCost,timestamp:Xd(new Date,"yyyy-MM-dd HH:mm:ss")};return{...t,purchases:[n,...t.purchases],currentUnits:t.currentUnits+e.payload.units}}case"UPDATE_USAGE":{const n={id:Date.now(),date:new Date().toISOString(),previousUnits:t.currentUnits,currentUnits:e.payload.currentUnits,usage:t.currentUnits-e.payload.currentUnits,timestamp:Xd(new Date,"yyyy-MM-dd HH:mm:ss")};return{...t,previousUnits:t.currentUnits,currentUnits:e.payload.currentUnits,usageHistory:[n,...t.usageHistory]}}case"UPDATE_SETTINGS":return{...t,...e.payload};case"FACTORY_RESET":return{...Jl};case"DASHBOARD_RESET":return{...t,currentUnits:0,previousUnits:0,lastResetDate:new Date().toISOString()};case"MONTHLY_RESET":return{...t,usageHistory:[],lastMonthlyReset:new Date().toISOString()};case"LOAD_STATE":return{...t,...e.payload};default:return t}}function Hb({children:t}){const[e,n]=_.useReducer(Bb,Jl);_.useEffect(()=>{const N="prepaid-meter-app-v1.1",k=localStorage.getItem(N);if(localStorage.removeItem("prepaid-meter-app"),k)try{const C=JSON.parse(k);if(!C.version||C.version!==Jl.version){console.log("Version mismatch detected, clearing old data"),localStorage.removeItem(N);return}n({type:"LOAD_STATE",payload:C})}catch(C){console.error("Error loading saved state:",C),localStorage.removeItem(N)}},[]),_.useEffect(()=>{localStorage.setItem("prepaid-meter-app-v1.1",JSON.stringify(e))},[e]),_.useEffect(()=>{const N=new Date,k=N.getMonth(),C=N.getFullYear();if(e.lastMonthlyReset){const P=new Date(e.lastMonthlyReset),M=P.getMonth(),D=P.getFullYear();(C>D||C===D&&k>M)&&n({type:"MONTHLY_RESET"})}else e.isInitialized&&n({type:"UPDATE_SETTINGS",payload:{lastMonthlyReset:N.toISOString()}})},[e.lastMonthlyReset,e.isInitialized]);const{testNotification:r}=Oy(e.notificationsEnabled&&e.isInitialized,e.notificationTime,e.lastNotificationDate,n),i=e.usageHistory.length>0?e.previousUnits-e.currentUnits:0,s=e.currentUnits<=e.thresholdLimit&&e.thresholdLimit>0,a=e.purchases.reduce((N,k)=>N+k.currency,0),o=e.usageHistory.reduce((N,k)=>N+k.usage,0),l=new Date,u=new Date(l.getFullYear(),l.getMonth(),l.getDate()-l.getDay()),d=new Date(l.getFullYear(),l.getMonth(),1),h=e.purchases.filter(N=>new Date(N.date)>=u),f=e.purchases.filter(N=>new Date(N.date)>=d),m=e.usageHistory.filter(N=>new Date(N.date)>=u),x=e.usageHistory.filter(N=>new Date(N.date)>=d),v=h.reduce((N,k)=>N+k.currency,0),w=f.reduce((N,k)=>N+k.currency,0),g=m.reduce((N,k)=>N+k.usage,0),p=x.reduce((N,k)=>N+k.usage,0),j={state:e,dispatch:n,initializeApp:(N,k)=>{n({type:"INITIALIZE_APP",payload:{initialUnits:N,unitCost:k}})},addPurchase:(N,k)=>{n({type:"ADD_PURCHASE",payload:{currency:N,units:k}})},updateUsage:N=>{n({type:"UPDATE_USAGE",payload:{currentUnits:N}})},updateSettings:N=>{n({type:"UPDATE_SETTINGS",payload:N})},factoryReset:()=>{n({type:"FACTORY_RESET"})},dashboardReset:()=>{n({type:"DASHBOARD_RESET"})},usageSinceLastRecording:i,isThresholdExceeded:s,totalPurchases:a,totalUnitsUsed:o,getDisplayUnitName:()=>e.unitName==="custom"?e.customUnitName||"Units":e.unitName,getDisplayCurrencySymbol:()=>e.currency==="CUSTOM"?e.customCurrencySymbol||"C":e.currencySymbol||"R",getDisplayCurrencyName:()=>{var k;return e.currency==="CUSTOM"?e.customCurrencyName||"Custom Currency":((k=[{code:"ZAR",name:"South African Rand"},{code:"USD",name:"US Dollar"},{code:"EUR",name:"Euro"},{code:"GBP",name:"British Pound"},{code:"JPY",name:"Japanese Yen"}].find(C=>C.code===e.currency))==null?void 0:k.name)||"Unknown Currency"},testNotification:r,weeklyPurchaseTotal:v,monthlyPurchaseTotal:w,weeklyUsageTotal:g,monthlyUsageTotal:p,weeklyPurchases:h,monthlyPurchases:f,weeklyUsage:m,monthlyUsage:x};return c.jsx(Yp.Provider,{value:j,children:t})}function Ke(){const t=_.useContext(Yp);if(!t)throw new Error("useApp must be used within an AppProvider");return t}const Xp=_.createContext(),xt={electric:{name:"Electric Blue",primary:"bg-blue-700",secondary:"bg-blue-50",accent:"bg-blue-500",background:"bg-blue-25",text:"text-blue-900",textSecondary:"text-blue-700",border:"border-blue-200",card:"bg-blue-50",gradient:"from-blue-500 to-blue-700",light:"bg-blue-100",lighter:"bg-blue-50",dark:"bg-blue-700",darker:"bg-blue-800"},dark:{name:"Dark Mode",primary:"bg-gray-700",secondary:"bg-gray-700",accent:"bg-gray-500",background:"bg-gray-900",text:"text-white",textSecondary:"text-gray-300",border:"border-gray-600",card:"bg-gray-800",gradient:"from-gray-600 to-gray-800",light:"bg-gray-700",lighter:"bg-gray-600",dark:"bg-gray-800",darker:"bg-gray-900"},green:{name:"Eco Green",primary:"bg-green-700",secondary:"bg-green-50",accent:"bg-green-500",background:"bg-green-25",text:"text-green-900",textSecondary:"text-green-700",border:"border-green-200",card:"bg-green-50",gradient:"from-green-500 to-green-700",light:"bg-green-100",lighter:"bg-green-50",dark:"bg-green-700",darker:"bg-green-800"},teal:{name:"Ocean Teal",primary:"bg-teal-700",secondary:"bg-teal-50",accent:"bg-teal-500",background:"bg-teal-25",text:"text-teal-900",textSecondary:"text-teal-700",border:"border-teal-200",card:"bg-teal-50",gradient:"from-teal-500 to-teal-700",light:"bg-teal-100",lighter:"bg-teal-50",dark:"bg-teal-700",darker:"bg-teal-800"},pink:{name:"Rose Pink",primary:"bg-pink-700",secondary:"bg-pink-50",accent:"bg-pink-500",background:"bg-pink-25",text:"text-pink-900",textSecondary:"text-pink-700",border:"border-pink-200",card:"bg-pink-50",gradient:"from-pink-500 to-pink-700",light:"bg-pink-100",lighter:"bg-pink-50",dark:"bg-pink-700",darker:"bg-pink-800"}};function Wb({children:t}){const[e,n]=_.useState("electric");_.useEffect(()=>{const i=localStorage.getItem("prepaid-meter-theme");i&&xt[i]&&n(i)},[]),_.useEffect(()=>{localStorage.setItem("prepaid-meter-theme",e)},[e]);const r={currentTheme:e,setCurrentTheme:n,theme:xt[e],themes:xt};return c.jsx(Xp.Provider,{value:r,children:c.jsx("div",{className:`${xt[e].background} ${xt[e].text} text-base min-h-screen transition-all duration-300`,style:{fontFamily:"Inter, system-ui, -apple-system, sans-serif",fontSize:"16px"},children:t})})}function ce(){const t=_.useContext(Xp);if(!t)throw new Error("useTheme must be used within a ThemeProvider");return t}var Kp={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},Kd=bt.createContext&&bt.createContext(Kp),vn=function(){return vn=Object.assign||function(t){for(var e,n=1,r=arguments.length;n<r;n++){e=arguments[n];for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])}return t},vn.apply(this,arguments)},Vb=function(t,e){var n={};for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&e.indexOf(r)<0&&(n[r]=t[r]);if(t!=null&&typeof Object.getOwnPropertySymbols=="function")for(var i=0,r=Object.getOwnPropertySymbols(t);i<r.length;i++)e.indexOf(r[i])<0&&Object.prototype.propertyIsEnumerable.call(t,r[i])&&(n[r[i]]=t[r[i]]);return n};function Qp(t){return t&&t.map(function(e,n){return bt.createElement(e.tag,vn({key:n},e.attr),Qp(e.child))})}function re(t){return function(e){return bt.createElement(Yb,vn({attr:vn({},t.attr)},e),Qp(t.child))}}function Yb(t){var e=function(n){var r=t.attr,i=t.size,s=t.title,a=Vb(t,["attr","size","title"]),o=i||n.size||"1em",l;return n.className&&(l=n.className),t.className&&(l=(l?l+" ":"")+t.className),bt.createElement("svg",vn({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},n.attr,r,a,{className:l,style:vn(vn({color:t.color||n.color},n.style),t.style),height:o,width:o,xmlns:"http://www.w3.org/2000/svg"}),s&&bt.createElement("title",null,s),t.children)};return Kd!==void 0?bt.createElement(Kd.Consumer,null,function(n){return e(n)}):e(Kp)}function Xb(t){return re({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M16.707 10.293a1 1 0 010 1.414l-6 6a1 1 0 01-1.414 0l-6-6a1 1 0 111.414-1.414L9 14.586V3a1 1 0 012 0v11.586l4.293-4.293a1 1 0 011.414 0z",clipRule:"evenodd"}}]})(t)}function Kb(t){return re({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M3.293 9.707a1 1 0 010-1.414l6-6a1 1 0 011.414 0l6 6a1 1 0 01-1.414 1.414L11 5.414V17a1 1 0 11-2 0V5.414L4.707 9.707a1 1 0 01-1.414 0z",clipRule:"evenodd"}}]})(t)}function Qb(t){return re({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M6 2a2 2 0 00-2 2v12a2 2 0 002 2h8a2 2 0 002-2V4a2 2 0 00-2-2H6zm1 2a1 1 0 000 2h6a1 1 0 100-2H7zm6 7a1 1 0 011 1v3a1 1 0 11-2 0v-3a1 1 0 011-1zm-3 3a1 1 0 100 2h.01a1 1 0 100-2H10zm-4 1a1 1 0 011-1h.01a1 1 0 110 2H7a1 1 0 01-1-1zm1-4a1 1 0 100 2h.01a1 1 0 100-2H7zm2 1a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1zm4-4a1 1 0 100 2h.01a1 1 0 100-2H13zM9 9a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1zM7 8a1 1 0 000 2h.01a1 1 0 000-2H7z",clipRule:"evenodd"}}]})(t)}function $a(t){return re({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z",clipRule:"evenodd"}}]})(t)}function su(t){return re({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{d:"M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z"}}]})(t)}function Gb(t){return re({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"}}]})(t)}function ec(t){return re({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z",clipRule:"evenodd"}}]})(t)}function Qd(t){return re({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z",clipRule:"evenodd"}}]})(t)}function Ra(t){return re({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{d:"M9 2a1 1 0 000 2h2a1 1 0 100-2H9z"}},{tag:"path",attr:{fillRule:"evenodd",d:"M4 5a2 2 0 012-2 3 3 0 003 3h2a3 3 0 003-3 2 2 0 012 2v11a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 4a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h3a1 1 0 100-2h-3zm-3 4a1 1 0 100 2h.01a1 1 0 100-2H7zm3 0a1 1 0 100 2h3a1 1 0 100-2h-3z",clipRule:"evenodd"}}]})(t)}function Gd(t){return re({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z",clipRule:"evenodd"}}]})(t)}function Ki(t){return re({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z",clipRule:"evenodd"}}]})(t)}function Gp(t){return re({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M4 2a2 2 0 00-2 2v11a3 3 0 106 0V4a2 2 0 00-2-2H4zm1 14a1 1 0 100-2 1 1 0 000 2zm5-1.757l4.9-4.9a2 2 0 000-2.828L13.485 5.1a2 2 0 00-2.828 0L10 5.757v8.486zM16 18H9.071l6-6H16a2 2 0 012 2v2a2 2 0 01-2 2z",clipRule:"evenodd"}}]})(t)}function Te(t){return re({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{d:"M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z"}},{tag:"path",attr:{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z",clipRule:"evenodd"}}]})(t)}function kr(t){return re({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z",clipRule:"evenodd"}}]})(t)}function qb(t){return re({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M3 3a1 1 0 011-1h12a1 1 0 011 1v3a1 1 0 01-.293.707L12 11.414V15a1 1 0 01-.293.707l-2 2A1 1 0 018 17v-5.586L3.293 6.707A1 1 0 013 6V3z",clipRule:"evenodd"}}]})(t)}function Zb(t){return re({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM4.332 8.027a6.012 6.012 0 011.912-2.706C6.512 5.73 6.974 6 7.5 6A1.5 1.5 0 019 7.5V8a2 2 0 004 0 2 2 0 011.523-1.943A5.977 5.977 0 0116 10c0 .34-.028.675-.083 1H15a2 2 0 00-2 2v2.197A5.973 5.973 0 0110 16v-2a2 2 0 00-2-2 2 2 0 01-2-2 2 2 0 00-1.668-1.973z",clipRule:"evenodd"}}]})(t)}function qp(t){return re({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{d:"M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"}}]})(t)}function De(t){return re({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z",clipRule:"evenodd"}}]})(t)}function Jb(t){return re({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M3 5a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 15a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z",clipRule:"evenodd"}}]})(t)}function e1(t){return re({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z",clipRule:"evenodd"}}]})(t)}function t1(t){return re({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{d:"M3 1a1 1 0 000 2h1.22l.305 1.222a.997.997 0 00.01.042l1.358 5.43-.893.892C3.74 11.846 4.632 14 6.414 14H15a1 1 0 000-2H6.414l1-1H14a1 1 0 00.894-.553l3-6A1 1 0 0017 3H6.28l-.31-1.243A1 1 0 005 1H3zM16 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0zM6.5 18a1.5 1.5 0 100-3 1.5 1.5 0 000 3z"}}]})(t)}function n1(t){return re({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M12 13a1 1 0 100 2h5a1 1 0 001-1V9a1 1 0 10-2 0v2.586l-4.293-4.293a1 1 0 00-1.414 0L8 9.586 3.707 5.293a1 1 0 00-1.414 1.414l5 5a1 1 0 001.414 0L11 9.414 14.586 13H12z",clipRule:"evenodd"}}]})(t)}function yt(t){return re({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M12 7a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0V8.414l-4.293 4.293a1 1 0 01-1.414 0L8 10.414l-4.293 4.293a1 1 0 01-1.414-1.414l5-5a1 1 0 011.414 0L11 10.586 14.586 7H12z",clipRule:"evenodd"}}]})(t)}function r1(t){return re({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z",clipRule:"evenodd"}}]})(t)}function i1({size:t="md",animated:e=!0,showText:n=!0}){const{theme:r}=ce(),[i,s]=_.useState(!1),a={sm:{logo:"h-12 w-12",text:"text-sm",icon:"h-6 w-6"},md:{logo:"h-16 w-16",text:"text-lg",icon:"h-8 w-8"},lg:{logo:"h-20 w-20",text:"text-xl",icon:"h-10 w-10"},xl:{logo:"h-24 w-24",text:"text-2xl",icon:"h-12 w-12"}},o=a[t]||a.md;return c.jsxs("div",{className:"flex items-center gap-3",children:[c.jsx("div",{className:`${o.logo} flex items-center justify-center`,children:i?c.jsx("div",{className:`${o.logo} rounded-2xl bg-gradient-to-br ${r.gradient} shadow-lg flex items-center justify-center ${e?"animate-pulse":""}`,children:c.jsx(De,{className:`${o.icon} text-white`})}):c.jsx("img",{src:"/Logo Prepaid User.png",alt:"Prepaid User Electricity Logo",className:`${o.logo} object-contain`,onError:()=>s(!0),onLoad:()=>s(!1)})}),n&&c.jsxs("div",{className:"flex flex-col",children:[c.jsx("h1",{className:`${o.text} font-black ${r.text} leading-tight`,children:"Prepaid User"}),c.jsx("p",{className:`text-base font-bold ${r.textSecondary} tracking-wider leading-tight`,children:"Electricity"})]})]})}function s1({onMenuClick:t}){const{theme:e,currentTheme:n}=ce(),{state:r}=Ke(),i=()=>n==="dark"?"bg-gray-900/95 backdrop-blur-xl border-gray-700/50":{electric:"bg-blue-550/95 backdrop-blur-xl border-white/30",green:"bg-green-550/95 backdrop-blur-xl border-white/30",teal:"bg-teal-550/95 backdrop-blur-xl border-white/30",pink:"bg-pink-550/95 backdrop-blur-xl border-white/30"}[n]||"bg-blue-550/95 backdrop-blur-xl border-white/30",s=()=>n==="dark"?e.text:"text-white drop-shadow-sm";return c.jsxs("header",{className:`${i()} border-b px-4 py-0.5 flex items-center justify-between shadow-xl`,children:[c.jsxs("div",{className:"flex items-center space-x-2",children:[c.jsx("button",{onClick:t,className:"hamburger-menu lg:hidden p-1.5 rounded-lg bg-white/15 backdrop-blur-sm text-white hover:bg-white/25 transition-all duration-300 shadow-lg",children:c.jsx(Jb,{className:"h-6 w-6"})}),c.jsx("div",{className:"flex items-center",children:c.jsx(i1,{size:"sm",animated:!0,showText:!1})})]}),c.jsxs("div",{className:"flex items-center space-x-2 lg:space-x-3 bg-black/30 backdrop-blur-md rounded-lg px-3 lg:px-4 py-1 lg:py-2 border border-white/30 shadow-lg",children:[c.jsxs("div",{className:"text-right",children:[c.jsx("p",{className:`text-sm lg:text-base ${s()}`,children:"Current Units"}),c.jsx("p",{className:`text-base lg:text-lg font-bold ${s()}`,children:r.currentUnits.toFixed(2)})]}),c.jsx("div",{className:"w-2 lg:w-3 h-2 lg:h-3 rounded-full bg-white/80 pulse-glow shadow-sm"})]})]})}const qd=[{name:"Dashboard",href:"/dashboard",icon:qp},{name:"Purchases",href:"/purchases",icon:t1},{name:"Usage",href:"/usage",icon:su},{name:"History",href:"/history",icon:Ra}],Zd=[{name:"General Settings",href:"/settings?section=general",icon:Ki},{name:"Appearance",href:"/settings?section=appearance",icon:Gp},{name:"Reset Options",href:"/settings?section=reset",icon:e1}];function a1({isOpen:t,onClose:e}){const{theme:n,currentTheme:r}=ce(),i=$t(),[s,a]=_.useState(!1),o=()=>r==="dark"?"bg-gray-900/95 backdrop-blur-xl border-gray-700/50":{electric:"bg-blue-550/95 backdrop-blur-xl border-white/30",green:"bg-green-550/95 backdrop-blur-xl border-white/30",teal:"bg-teal-550/95 backdrop-blur-xl border-white/30",pink:"bg-pink-550/95 backdrop-blur-xl border-white/30"}[r]||"bg-blue-550/95 backdrop-blur-xl border-white/30",l=()=>r==="dark"?n.text:"text-white drop-shadow-sm",u=()=>r==="dark"?"bg-white/15 backdrop-blur-sm":"bg-white/25 backdrop-blur-sm",d=()=>r==="dark"?"bg-white/20 backdrop-blur-md":"bg-white/35 backdrop-blur-md";return c.jsxs(c.Fragment,{children:[c.jsx("div",{className:`hidden lg:flex lg:flex-shrink-0 lg:w-64 ${o()} border-r shadow-2xl`,children:c.jsxs("div",{className:"flex flex-col w-full",children:[c.jsx("div",{className:"flex items-center justify-center h-16 px-4 border-b border-white/15",children:c.jsx("h2",{className:`text-xl font-bold ${l()} tracking-wider drop-shadow-sm`,children:"MENU"})}),c.jsxs("nav",{className:"flex-1 px-4 py-6 space-y-2",children:[qd.map(h=>c.jsxs(js,{to:h.href,className:({isActive:f})=>`flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-300 ${f?`${d()} ${l()} shadow-lg border border-white/30`:`${l()} hover:${u()} hover:shadow-md hover:border hover:border-white/20`}`,children:[c.jsx(h.icon,{className:"mr-3 h-5 w-5"}),h.name]},h.name)),c.jsxs("div",{className:"space-y-2",children:[c.jsxs("button",{onClick:()=>a(!s),className:`w-full flex items-center justify-between px-4 py-3 text-sm font-medium rounded-xl transition-all duration-300 ${i.pathname.includes("/settings")?`${d()} ${l()} shadow-lg border border-white/30`:`${l()} hover:${u()} hover:shadow-md hover:border hover:border-white/20`}`,children:[c.jsxs("div",{className:"flex items-center",children:[c.jsx(Ki,{className:"mr-3 h-5 w-5"}),"Settings"]}),s?c.jsx(ec,{className:"h-4 w-4"}):c.jsx(Qd,{className:"h-4 w-4"})]}),s&&c.jsx("div",{className:"ml-4 space-y-2 mt-2",children:Zd.map(h=>c.jsxs(js,{to:h.href,className:({isActive:f})=>`flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-all duration-300 ${f?`bg-white/30 backdrop-blur-sm ${l()} shadow-md border border-white/25`:`${l()} hover:bg-white/20 hover:backdrop-blur-sm hover:shadow-sm`}`,children:[c.jsx(h.icon,{className:"mr-3 h-4 w-4"}),h.name]},h.name))})]})]})]})}),t&&c.jsx("div",{className:`sidebar-container lg:hidden fixed inset-y-0 left-0 z-50 w-64 ${o()} transform transition-transform duration-300 ease-in-out shadow-2xl translate-x-0`,style:{paddingTop:"env(safe-area-inset-top, 0px)"},children:c.jsxs("div",{className:"flex flex-col h-full",children:[c.jsxs("div",{className:"flex items-center justify-between px-4 border-b border-white/15",style:{minHeight:"calc(4rem + env(safe-area-inset-top, 0px))",paddingTop:"calc(env(safe-area-inset-top, 0px) + 1rem)",paddingBottom:"1rem"},children:[c.jsx("div",{className:"flex items-center",children:c.jsx("h2",{className:`text-xl font-bold ${l()} tracking-wider drop-shadow-sm`,children:"MENU"})}),c.jsx("button",{onClick:e,className:`p-2 rounded-xl ${l()} hover:bg-white/15 hover:backdrop-blur-sm transition-all duration-300 shadow-sm`,children:c.jsx(r1,{className:"h-6 w-6"})})]}),c.jsxs("nav",{className:"flex-1 px-4 py-6 space-y-2",children:[qd.map(h=>c.jsxs(js,{to:h.href,onClick:e,className:({isActive:f})=>`flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-300 ${f?`${d()} ${l()} shadow-lg border border-white/30`:`${l()} hover:${u()} hover:shadow-md hover:border hover:border-white/20`}`,children:[c.jsx(h.icon,{className:"mr-3 h-5 w-5"}),h.name]},h.name)),c.jsxs("div",{className:"space-y-2",children:[c.jsxs("button",{onClick:()=>a(!s),className:`w-full flex items-center justify-between px-4 py-3 text-sm font-medium rounded-xl transition-all duration-300 ${i.pathname.includes("/settings")?`${d()} ${l()} shadow-lg border border-white/30`:`${l()} hover:${u()} hover:shadow-md hover:border hover:border-white/20`}`,children:[c.jsxs("div",{className:"flex items-center",children:[c.jsx(Ki,{className:"mr-3 h-5 w-5"}),"Settings"]}),s?c.jsx(ec,{className:"h-4 w-4"}):c.jsx(Qd,{className:"h-4 w-4"})]}),s&&c.jsx("div",{className:"ml-4 space-y-2 mt-2",children:Zd.map(h=>c.jsxs(js,{to:h.href,onClick:e,className:({isActive:f})=>`flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-all duration-300 ${f?`bg-white/30 backdrop-blur-sm ${l()} shadow-md border border-white/25`:`${l()} hover:bg-white/20 hover:backdrop-blur-sm hover:shadow-sm`}`,children:[c.jsx(h.icon,{className:"mr-3 h-4 w-4"}),h.name]},h.name))})]})]})]})})]})}function o1(){const t=$t(),e=tr(),{theme:n,currentTheme:r}=ce(),i=()=>r==="dark"?"bg-gray-900/95 backdrop-blur-xl border-gray-700/50":{electric:"bg-blue-550/95 backdrop-blur-xl border-white/30",green:"bg-green-550/95 backdrop-blur-xl border-white/30",teal:"bg-teal-550/95 backdrop-blur-xl border-white/30",pink:"bg-pink-550/95 backdrop-blur-xl border-white/30"}[r]||"bg-blue-550/95 backdrop-blur-xl border-white/30",s=[{id:"dashboard",path:"/",icon:qp,label:"Dashboard"},{id:"purchases",path:"/purchases",icon:Te,label:"Purchases"},{id:"usage",path:"/usage",icon:yt,label:"Usage"},{id:"history",path:"/history",icon:Ra,label:"History"}],a=o=>o==="/"?t.pathname==="/"||t.pathname==="/dashboard":t.pathname===o;return c.jsx("div",{className:`md:hidden ${i()} border-t shadow-xl`,children:c.jsx("div",{className:"flex items-center justify-around px-1 py-0.5",children:s.map(o=>{const l=o.icon,u=a(o.path);return c.jsxs("button",{onClick:()=>e(o.path),className:`flex flex-col items-center justify-center p-1 rounded-lg transition-all duration-200 min-w-0 flex-1 ${u?"bg-white/20 text-white shadow-lg transform scale-105 border border-white/30":"text-white/80 hover:bg-white/10 hover:text-white"}`,children:[c.jsx(l,{className:`h-3.5 w-3.5 ${u?"text-white":"text-white/80"}`}),c.jsx("span",{className:`text-xs font-medium mt-0.5 truncate ${u?"text-white":"text-white/80"}`,children:o.label})]},o.id)})})})}/*!
 * @kurkle/color v0.3.4
 * https://github.com/kurkle/color#readme
 * (c) 2024 Jukka Kurkela
 * Released under the MIT License
 */function as(t){return t+.5|0}const on=(t,e,n)=>Math.max(Math.min(t,n),e);function hi(t){return on(as(t*2.55),0,255)}function yn(t){return on(as(t*255),0,255)}function zt(t){return on(as(t/2.55)/100,0,1)}function Jd(t){return on(as(t*100),0,100)}const tt={0:0,1:1,2:2,3:3,4:4,5:5,6:6,7:7,8:8,9:9,A:10,B:11,C:12,D:13,E:14,F:15,a:10,b:11,c:12,d:13,e:14,f:15},tc=[..."0123456789ABCDEF"],l1=t=>tc[t&15],c1=t=>tc[(t&240)>>4]+tc[t&15],Cs=t=>(t&240)>>4===(t&15),u1=t=>Cs(t.r)&&Cs(t.g)&&Cs(t.b)&&Cs(t.a);function d1(t){var e=t.length,n;return t[0]==="#"&&(e===4||e===5?n={r:255&tt[t[1]]*17,g:255&tt[t[2]]*17,b:255&tt[t[3]]*17,a:e===5?tt[t[4]]*17:255}:(e===7||e===9)&&(n={r:tt[t[1]]<<4|tt[t[2]],g:tt[t[3]]<<4|tt[t[4]],b:tt[t[5]]<<4|tt[t[6]],a:e===9?tt[t[7]]<<4|tt[t[8]]:255})),n}const h1=(t,e)=>t<255?e(t):"";function f1(t){var e=u1(t)?l1:c1;return t?"#"+e(t.r)+e(t.g)+e(t.b)+h1(t.a,e):void 0}const m1=/^(hsla?|hwb|hsv)\(\s*([-+.e\d]+)(?:deg)?[\s,]+([-+.e\d]+)%[\s,]+([-+.e\d]+)%(?:[\s,]+([-+.e\d]+)(%)?)?\s*\)$/;function Zp(t,e,n){const r=e*Math.min(n,1-n),i=(s,a=(s+t/30)%12)=>n-r*Math.max(Math.min(a-3,9-a,1),-1);return[i(0),i(8),i(4)]}function p1(t,e,n){const r=(i,s=(i+t/60)%6)=>n-n*e*Math.max(Math.min(s,4-s,1),0);return[r(5),r(3),r(1)]}function g1(t,e,n){const r=Zp(t,1,.5);let i;for(e+n>1&&(i=1/(e+n),e*=i,n*=i),i=0;i<3;i++)r[i]*=1-e-n,r[i]+=e;return r}function x1(t,e,n,r,i){return t===i?(e-n)/r+(e<n?6:0):e===i?(n-t)/r+2:(t-e)/r+4}function au(t){const n=t.r/255,r=t.g/255,i=t.b/255,s=Math.max(n,r,i),a=Math.min(n,r,i),o=(s+a)/2;let l,u,d;return s!==a&&(d=s-a,u=o>.5?d/(2-s-a):d/(s+a),l=x1(n,r,i,d,s),l=l*60+.5),[l|0,u||0,o]}function ou(t,e,n,r){return(Array.isArray(e)?t(e[0],e[1],e[2]):t(e,n,r)).map(yn)}function lu(t,e,n){return ou(Zp,t,e,n)}function v1(t,e,n){return ou(g1,t,e,n)}function y1(t,e,n){return ou(p1,t,e,n)}function Jp(t){return(t%360+360)%360}function b1(t){const e=m1.exec(t);let n=255,r;if(!e)return;e[5]!==r&&(n=e[6]?hi(+e[5]):yn(+e[5]));const i=Jp(+e[2]),s=+e[3]/100,a=+e[4]/100;return e[1]==="hwb"?r=v1(i,s,a):e[1]==="hsv"?r=y1(i,s,a):r=lu(i,s,a),{r:r[0],g:r[1],b:r[2],a:n}}function w1(t,e){var n=au(t);n[0]=Jp(n[0]+e),n=lu(n),t.r=n[0],t.g=n[1],t.b=n[2]}function S1(t){if(!t)return;const e=au(t),n=e[0],r=Jd(e[1]),i=Jd(e[2]);return t.a<255?`hsla(${n}, ${r}%, ${i}%, ${zt(t.a)})`:`hsl(${n}, ${r}%, ${i}%)`}const eh={x:"dark",Z:"light",Y:"re",X:"blu",W:"gr",V:"medium",U:"slate",A:"ee",T:"ol",S:"or",B:"ra",C:"lateg",D:"ights",R:"in",Q:"turquois",E:"hi",P:"ro",O:"al",N:"le",M:"de",L:"yello",F:"en",K:"ch",G:"arks",H:"ea",I:"ightg",J:"wh"},th={OiceXe:"f0f8ff",antiquewEte:"faebd7",aqua:"ffff",aquamarRe:"7fffd4",azuY:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"0",blanKedOmond:"ffebcd",Xe:"ff",XeviTet:"8a2be2",bPwn:"a52a2a",burlywood:"deb887",caMtXe:"5f9ea0",KartYuse:"7fff00",KocTate:"d2691e",cSO:"ff7f50",cSnflowerXe:"6495ed",cSnsilk:"fff8dc",crimson:"dc143c",cyan:"ffff",xXe:"8b",xcyan:"8b8b",xgTMnPd:"b8860b",xWay:"a9a9a9",xgYF:"6400",xgYy:"a9a9a9",xkhaki:"bdb76b",xmagFta:"8b008b",xTivegYF:"556b2f",xSange:"ff8c00",xScEd:"9932cc",xYd:"8b0000",xsOmon:"e9967a",xsHgYF:"8fbc8f",xUXe:"483d8b",xUWay:"2f4f4f",xUgYy:"2f4f4f",xQe:"ced1",xviTet:"9400d3",dAppRk:"ff1493",dApskyXe:"bfff",dimWay:"696969",dimgYy:"696969",dodgerXe:"1e90ff",fiYbrick:"b22222",flSOwEte:"fffaf0",foYstWAn:"228b22",fuKsia:"ff00ff",gaRsbSo:"dcdcdc",ghostwEte:"f8f8ff",gTd:"ffd700",gTMnPd:"daa520",Way:"808080",gYF:"8000",gYFLw:"adff2f",gYy:"808080",honeyMw:"f0fff0",hotpRk:"ff69b4",RdianYd:"cd5c5c",Rdigo:"4b0082",ivSy:"fffff0",khaki:"f0e68c",lavFMr:"e6e6fa",lavFMrXsh:"fff0f5",lawngYF:"7cfc00",NmoncEffon:"fffacd",ZXe:"add8e6",ZcSO:"f08080",Zcyan:"e0ffff",ZgTMnPdLw:"fafad2",ZWay:"d3d3d3",ZgYF:"90ee90",ZgYy:"d3d3d3",ZpRk:"ffb6c1",ZsOmon:"ffa07a",ZsHgYF:"20b2aa",ZskyXe:"87cefa",ZUWay:"778899",ZUgYy:"778899",ZstAlXe:"b0c4de",ZLw:"ffffe0",lime:"ff00",limegYF:"32cd32",lRF:"faf0e6",magFta:"ff00ff",maPon:"800000",VaquamarRe:"66cdaa",VXe:"cd",VScEd:"ba55d3",VpurpN:"9370db",VsHgYF:"3cb371",VUXe:"7b68ee",VsprRggYF:"fa9a",VQe:"48d1cc",VviTetYd:"c71585",midnightXe:"191970",mRtcYam:"f5fffa",mistyPse:"ffe4e1",moccasR:"ffe4b5",navajowEte:"ffdead",navy:"80",Tdlace:"fdf5e6",Tive:"808000",TivedBb:"6b8e23",Sange:"ffa500",SangeYd:"ff4500",ScEd:"da70d6",pOegTMnPd:"eee8aa",pOegYF:"98fb98",pOeQe:"afeeee",pOeviTetYd:"db7093",papayawEp:"ffefd5",pHKpuff:"ffdab9",peru:"cd853f",pRk:"ffc0cb",plum:"dda0dd",powMrXe:"b0e0e6",purpN:"800080",YbeccapurpN:"663399",Yd:"ff0000",Psybrown:"bc8f8f",PyOXe:"4169e1",saddNbPwn:"8b4513",sOmon:"fa8072",sandybPwn:"f4a460",sHgYF:"2e8b57",sHshell:"fff5ee",siFna:"a0522d",silver:"c0c0c0",skyXe:"87ceeb",UXe:"6a5acd",UWay:"708090",UgYy:"708090",snow:"fffafa",sprRggYF:"ff7f",stAlXe:"4682b4",tan:"d2b48c",teO:"8080",tEstN:"d8bfd8",tomato:"ff6347",Qe:"40e0d0",viTet:"ee82ee",JHt:"f5deb3",wEte:"ffffff",wEtesmoke:"f5f5f5",Lw:"ffff00",LwgYF:"9acd32"};function N1(){const t={},e=Object.keys(th),n=Object.keys(eh);let r,i,s,a,o;for(r=0;r<e.length;r++){for(a=o=e[r],i=0;i<n.length;i++)s=n[i],o=o.replace(s,eh[s]);s=parseInt(th[a],16),t[o]=[s>>16&255,s>>8&255,s&255]}return t}let Ps;function _1(t){Ps||(Ps=N1(),Ps.transparent=[0,0,0,0]);const e=Ps[t.toLowerCase()];return e&&{r:e[0],g:e[1],b:e[2],a:e.length===4?e[3]:255}}const k1=/^rgba?\(\s*([-+.\d]+)(%)?[\s,]+([-+.e\d]+)(%)?[\s,]+([-+.e\d]+)(%)?(?:[\s,/]+([-+.e\d]+)(%)?)?\s*\)$/;function j1(t){const e=k1.exec(t);let n=255,r,i,s;if(e){if(e[7]!==r){const a=+e[7];n=e[8]?hi(a):on(a*255,0,255)}return r=+e[1],i=+e[3],s=+e[5],r=255&(e[2]?hi(r):on(r,0,255)),i=255&(e[4]?hi(i):on(i,0,255)),s=255&(e[6]?hi(s):on(s,0,255)),{r,g:i,b:s,a:n}}}function C1(t){return t&&(t.a<255?`rgba(${t.r}, ${t.g}, ${t.b}, ${zt(t.a)})`:`rgb(${t.r}, ${t.g}, ${t.b})`)}const Ho=t=>t<=.0031308?t*12.92:Math.pow(t,1/2.4)*1.055-.055,ir=t=>t<=.04045?t/12.92:Math.pow((t+.055)/1.055,2.4);function P1(t,e,n){const r=ir(zt(t.r)),i=ir(zt(t.g)),s=ir(zt(t.b));return{r:yn(Ho(r+n*(ir(zt(e.r))-r))),g:yn(Ho(i+n*(ir(zt(e.g))-i))),b:yn(Ho(s+n*(ir(zt(e.b))-s))),a:t.a+n*(e.a-t.a)}}function Es(t,e,n){if(t){let r=au(t);r[e]=Math.max(0,Math.min(r[e]+r[e]*n,e===0?360:1)),r=lu(r),t.r=r[0],t.g=r[1],t.b=r[2]}}function eg(t,e){return t&&Object.assign(e||{},t)}function nh(t){var e={r:0,g:0,b:0,a:255};return Array.isArray(t)?t.length>=3&&(e={r:t[0],g:t[1],b:t[2],a:255},t.length>3&&(e.a=yn(t[3]))):(e=eg(t,{r:0,g:0,b:0,a:1}),e.a=yn(e.a)),e}function E1(t){return t.charAt(0)==="r"?j1(t):b1(t)}class Qi{constructor(e){if(e instanceof Qi)return e;const n=typeof e;let r;n==="object"?r=nh(e):n==="string"&&(r=d1(e)||_1(e)||E1(e)),this._rgb=r,this._valid=!!r}get valid(){return this._valid}get rgb(){var e=eg(this._rgb);return e&&(e.a=zt(e.a)),e}set rgb(e){this._rgb=nh(e)}rgbString(){return this._valid?C1(this._rgb):void 0}hexString(){return this._valid?f1(this._rgb):void 0}hslString(){return this._valid?S1(this._rgb):void 0}mix(e,n){if(e){const r=this.rgb,i=e.rgb;let s;const a=n===s?.5:n,o=2*a-1,l=r.a-i.a,u=((o*l===-1?o:(o+l)/(1+o*l))+1)/2;s=1-u,r.r=255&u*r.r+s*i.r+.5,r.g=255&u*r.g+s*i.g+.5,r.b=255&u*r.b+s*i.b+.5,r.a=a*r.a+(1-a)*i.a,this.rgb=r}return this}interpolate(e,n){return e&&(this._rgb=P1(this._rgb,e._rgb,n)),this}clone(){return new Qi(this.rgb)}alpha(e){return this._rgb.a=yn(e),this}clearer(e){const n=this._rgb;return n.a*=1-e,this}greyscale(){const e=this._rgb,n=as(e.r*.3+e.g*.59+e.b*.11);return e.r=e.g=e.b=n,this}opaquer(e){const n=this._rgb;return n.a*=1+e,this}negate(){const e=this._rgb;return e.r=255-e.r,e.g=255-e.g,e.b=255-e.b,this}lighten(e){return Es(this._rgb,2,e),this}darken(e){return Es(this._rgb,2,-e),this}saturate(e){return Es(this._rgb,1,e),this}desaturate(e){return Es(this._rgb,1,-e),this}rotate(e){return w1(this._rgb,e),this}}/*!
 * Chart.js v4.4.9
 * https://www.chartjs.org
 * (c) 2025 Chart.js Contributors
 * Released under the MIT License
 */function Dt(){}const T1=(()=>{let t=0;return()=>t++})();function q(t){return t==null}function xe(t){if(Array.isArray&&Array.isArray(t))return!0;const e=Object.prototype.toString.call(t);return e.slice(0,7)==="[object"&&e.slice(-6)==="Array]"}function V(t){return t!==null&&Object.prototype.toString.call(t)==="[object Object]"}function ct(t){return(typeof t=="number"||t instanceof Number)&&isFinite(+t)}function kt(t,e){return ct(t)?t:e}function X(t,e){return typeof t>"u"?e:t}const M1=(t,e)=>typeof t=="string"&&t.endsWith("%")?parseFloat(t)/100:+t/e,tg=(t,e)=>typeof t=="string"&&t.endsWith("%")?parseFloat(t)/100*e:+t;function ee(t,e,n){if(t&&typeof t.call=="function")return t.apply(n,e)}function Q(t,e,n,r){let i,s,a;if(xe(t))for(s=t.length,i=0;i<s;i++)e.call(n,t[i],i);else if(V(t))for(a=Object.keys(t),s=a.length,i=0;i<s;i++)e.call(n,t[a[i]],a[i])}function Da(t,e){let n,r,i,s;if(!t||!e||t.length!==e.length)return!1;for(n=0,r=t.length;n<r;++n)if(i=t[n],s=e[n],i.datasetIndex!==s.datasetIndex||i.index!==s.index)return!1;return!0}function Oa(t){if(xe(t))return t.map(Oa);if(V(t)){const e=Object.create(null),n=Object.keys(t),r=n.length;let i=0;for(;i<r;++i)e[n[i]]=Oa(t[n[i]]);return e}return t}function ng(t){return["__proto__","prototype","constructor"].indexOf(t)===-1}function $1(t,e,n,r){if(!ng(t))return;const i=e[t],s=n[t];V(i)&&V(s)?Gi(i,s,r):e[t]=Oa(s)}function Gi(t,e,n){const r=xe(e)?e:[e],i=r.length;if(!V(t))return t;n=n||{};const s=n.merger||$1;let a;for(let o=0;o<i;++o){if(a=r[o],!V(a))continue;const l=Object.keys(a);for(let u=0,d=l.length;u<d;++u)s(l[u],t,a,n)}return t}function ji(t,e){return Gi(t,e,{merger:R1})}function R1(t,e,n){if(!ng(t))return;const r=e[t],i=n[t];V(r)&&V(i)?ji(r,i):Object.prototype.hasOwnProperty.call(e,t)||(e[t]=Oa(i))}const rh={"":t=>t,x:t=>t.x,y:t=>t.y};function D1(t){const e=t.split("."),n=[];let r="";for(const i of e)r+=i,r.endsWith("\\")?r=r.slice(0,-1)+".":(n.push(r),r="");return n}function O1(t){const e=D1(t);return n=>{for(const r of e){if(r==="")break;n=n&&n[r]}return n}}function Gn(t,e){return(rh[e]||(rh[e]=O1(e)))(t)}function cu(t){return t.charAt(0).toUpperCase()+t.slice(1)}const qi=t=>typeof t<"u",Nn=t=>typeof t=="function",ih=(t,e)=>{if(t.size!==e.size)return!1;for(const n of t)if(!e.has(n))return!1;return!0};function L1(t){return t.type==="mouseup"||t.type==="click"||t.type==="contextmenu"}const me=Math.PI,fe=2*me,La=Number.POSITIVE_INFINITY,A1=me/180,ye=me/2,Pn=me/4,sh=me*2/3,rg=Math.log10,bn=Math.sign;function Zs(t,e,n){return Math.abs(t-e)<n}function ah(t){const e=Math.round(t);t=Zs(t,e,t/1e3)?e:t;const n=Math.pow(10,Math.floor(rg(t))),r=t/n;return(r<=1?1:r<=2?2:r<=5?5:10)*n}function F1(t){const e=[],n=Math.sqrt(t);let r;for(r=1;r<n;r++)t%r===0&&(e.push(r),e.push(t/r));return n===(n|0)&&e.push(n),e.sort((i,s)=>i-s).pop(),e}function z1(t){return typeof t=="symbol"||typeof t=="object"&&t!==null&&!(Symbol.toPrimitive in t||"toString"in t||"valueOf"in t)}function Aa(t){return!z1(t)&&!isNaN(parseFloat(t))&&isFinite(t)}function I1(t,e){const n=Math.round(t);return n-e<=t&&n+e>=t}function U1(t,e,n){let r,i,s;for(r=0,i=t.length;r<i;r++)s=t[r][n],isNaN(s)||(e.min=Math.min(e.min,s),e.max=Math.max(e.max,s))}function Bt(t){return t*(me/180)}function B1(t){return t*(180/me)}function oh(t){if(!ct(t))return;let e=1,n=0;for(;Math.round(t*e)/e!==t;)e*=10,n++;return n}function ig(t,e){const n=e.x-t.x,r=e.y-t.y,i=Math.sqrt(n*n+r*r);let s=Math.atan2(r,n);return s<-.5*me&&(s+=fe),{angle:s,distance:i}}function H1(t,e){return Math.sqrt(Math.pow(e.x-t.x,2)+Math.pow(e.y-t.y,2))}function En(t){return(t%fe+fe)%fe}function Fa(t,e,n,r){const i=En(t),s=En(e),a=En(n),o=En(s-i),l=En(a-i),u=En(i-s),d=En(i-a);return i===s||i===a||r&&s===a||o>l&&u<d}function We(t,e,n){return Math.max(e,Math.min(n,t))}function W1(t){return We(t,-32768,32767)}function In(t,e,n,r=1e-6){return t>=Math.min(e,n)-r&&t<=Math.max(e,n)+r}function uu(t,e,n){n=n||(a=>t[a]<e);let r=t.length-1,i=0,s;for(;r-i>1;)s=i+r>>1,n(s)?i=s:r=s;return{lo:i,hi:r}}const nc=(t,e,n,r)=>uu(t,n,r?i=>{const s=t[i][e];return s<n||s===n&&t[i+1][e]===n}:i=>t[i][e]<n),V1=(t,e,n)=>uu(t,n,r=>t[r][e]>=n);function Y1(t,e,n){let r=0,i=t.length;for(;r<i&&t[r]<e;)r++;for(;i>r&&t[i-1]>n;)i--;return r>0||i<t.length?t.slice(r,i):t}const sg=["push","pop","shift","splice","unshift"];function X1(t,e){if(t._chartjs){t._chartjs.listeners.push(e);return}Object.defineProperty(t,"_chartjs",{configurable:!0,enumerable:!1,value:{listeners:[e]}}),sg.forEach(n=>{const r="_onData"+cu(n),i=t[n];Object.defineProperty(t,n,{configurable:!0,enumerable:!1,value(...s){const a=i.apply(this,s);return t._chartjs.listeners.forEach(o=>{typeof o[r]=="function"&&o[r](...s)}),a}})})}function lh(t,e){const n=t._chartjs;if(!n)return;const r=n.listeners,i=r.indexOf(e);i!==-1&&r.splice(i,1),!(r.length>0)&&(sg.forEach(s=>{delete t[s]}),delete t._chartjs)}function ag(t){const e=new Set(t);return e.size===t.length?t:Array.from(e)}const og=function(){return typeof window>"u"?function(t){return t()}:window.requestAnimationFrame}();function lg(t,e){let n=[],r=!1;return function(...i){n=i,r||(r=!0,og.call(window,()=>{r=!1,t.apply(e,n)}))}}function K1(t,e){let n;return function(...r){return e?(clearTimeout(n),n=setTimeout(t,e,r)):t.apply(this,r),e}}const du=t=>t==="start"?"left":t==="end"?"right":"center",Pe=(t,e,n)=>t==="start"?e:t==="end"?n:(e+n)/2,Q1=(t,e,n,r)=>t===(r?"left":"right")?n:t==="center"?(e+n)/2:e,Ts=t=>t===0||t===1,ch=(t,e,n)=>-(Math.pow(2,10*(t-=1))*Math.sin((t-e)*fe/n)),uh=(t,e,n)=>Math.pow(2,-10*t)*Math.sin((t-e)*fe/n)+1,Ci={linear:t=>t,easeInQuad:t=>t*t,easeOutQuad:t=>-t*(t-2),easeInOutQuad:t=>(t/=.5)<1?.5*t*t:-.5*(--t*(t-2)-1),easeInCubic:t=>t*t*t,easeOutCubic:t=>(t-=1)*t*t+1,easeInOutCubic:t=>(t/=.5)<1?.5*t*t*t:.5*((t-=2)*t*t+2),easeInQuart:t=>t*t*t*t,easeOutQuart:t=>-((t-=1)*t*t*t-1),easeInOutQuart:t=>(t/=.5)<1?.5*t*t*t*t:-.5*((t-=2)*t*t*t-2),easeInQuint:t=>t*t*t*t*t,easeOutQuint:t=>(t-=1)*t*t*t*t+1,easeInOutQuint:t=>(t/=.5)<1?.5*t*t*t*t*t:.5*((t-=2)*t*t*t*t+2),easeInSine:t=>-Math.cos(t*ye)+1,easeOutSine:t=>Math.sin(t*ye),easeInOutSine:t=>-.5*(Math.cos(me*t)-1),easeInExpo:t=>t===0?0:Math.pow(2,10*(t-1)),easeOutExpo:t=>t===1?1:-Math.pow(2,-10*t)+1,easeInOutExpo:t=>Ts(t)?t:t<.5?.5*Math.pow(2,10*(t*2-1)):.5*(-Math.pow(2,-10*(t*2-1))+2),easeInCirc:t=>t>=1?t:-(Math.sqrt(1-t*t)-1),easeOutCirc:t=>Math.sqrt(1-(t-=1)*t),easeInOutCirc:t=>(t/=.5)<1?-.5*(Math.sqrt(1-t*t)-1):.5*(Math.sqrt(1-(t-=2)*t)+1),easeInElastic:t=>Ts(t)?t:ch(t,.075,.3),easeOutElastic:t=>Ts(t)?t:uh(t,.075,.3),easeInOutElastic(t){return Ts(t)?t:t<.5?.5*ch(t*2,.1125,.45):.5+.5*uh(t*2-1,.1125,.45)},easeInBack(t){return t*t*((1.70158+1)*t-1.70158)},easeOutBack(t){return(t-=1)*t*((1.70158+1)*t********)+1},easeInOutBack(t){let e=1.70158;return(t/=.5)<1?.5*(t*t*(((e*=1.525)+1)*t-e)):.5*((t-=2)*t*(((e*=1.525)+1)*t+e)+2)},easeInBounce:t=>1-Ci.easeOutBounce(1-t),easeOutBounce(t){return t<1/2.75?7.5625*t*t:t<2/2.75?7.5625*(t-=1.5/2.75)*t+.75:t<2.5/2.75?7.5625*(t-=2.25/2.75)*t+.9375:7.5625*(t-=2.625/2.75)*t+.984375},easeInOutBounce:t=>t<.5?Ci.easeInBounce(t*2)*.5:Ci.easeOutBounce(t*2-1)*.5+.5};function cg(t){if(t&&typeof t=="object"){const e=t.toString();return e==="[object CanvasPattern]"||e==="[object CanvasGradient]"}return!1}function dh(t){return cg(t)?t:new Qi(t)}function Wo(t){return cg(t)?t:new Qi(t).saturate(.5).darken(.1).hexString()}const G1=["x","y","borderWidth","radius","tension"],q1=["color","borderColor","backgroundColor"];function Z1(t){t.set("animation",{delay:void 0,duration:1e3,easing:"easeOutQuart",fn:void 0,from:void 0,loop:void 0,to:void 0,type:void 0}),t.describe("animation",{_fallback:!1,_indexable:!1,_scriptable:e=>e!=="onProgress"&&e!=="onComplete"&&e!=="fn"}),t.set("animations",{colors:{type:"color",properties:q1},numbers:{type:"number",properties:G1}}),t.describe("animations",{_fallback:"animation"}),t.set("transitions",{active:{animation:{duration:400}},resize:{animation:{duration:0}},show:{animations:{colors:{from:"transparent"},visible:{type:"boolean",duration:0}}},hide:{animations:{colors:{to:"transparent"},visible:{type:"boolean",easing:"linear",fn:e=>e|0}}}})}function J1(t){t.set("layout",{autoPadding:!0,padding:{top:0,right:0,bottom:0,left:0}})}const hh=new Map;function ew(t,e){e=e||{};const n=t+JSON.stringify(e);let r=hh.get(n);return r||(r=new Intl.NumberFormat(t,e),hh.set(n,r)),r}function hu(t,e,n){return ew(e,n).format(t)}const tw={values(t){return xe(t)?t:""+t},numeric(t,e,n){if(t===0)return"0";const r=this.chart.options.locale;let i,s=t;if(n.length>1){const u=Math.max(Math.abs(n[0].value),Math.abs(n[n.length-1].value));(u<1e-4||u>1e15)&&(i="scientific"),s=nw(t,n)}const a=rg(Math.abs(s)),o=isNaN(a)?1:Math.max(Math.min(-1*Math.floor(a),20),0),l={notation:i,minimumFractionDigits:o,maximumFractionDigits:o};return Object.assign(l,this.options.ticks.format),hu(t,r,l)}};function nw(t,e){let n=e.length>3?e[2].value-e[1].value:e[1].value-e[0].value;return Math.abs(n)>=1&&t!==Math.floor(t)&&(n=t-Math.floor(t)),n}var ug={formatters:tw};function rw(t){t.set("scale",{display:!0,offset:!1,reverse:!1,beginAtZero:!1,bounds:"ticks",clip:!0,grace:0,grid:{display:!0,lineWidth:1,drawOnChartArea:!0,drawTicks:!0,tickLength:8,tickWidth:(e,n)=>n.lineWidth,tickColor:(e,n)=>n.color,offset:!1},border:{display:!0,dash:[],dashOffset:0,width:1},title:{display:!1,text:"",padding:{top:4,bottom:4}},ticks:{minRotation:0,maxRotation:50,mirror:!1,textStrokeWidth:0,textStrokeColor:"",padding:3,display:!0,autoSkip:!0,autoSkipPadding:3,labelOffset:0,callback:ug.formatters.values,minor:{},major:{},align:"center",crossAlign:"near",showLabelBackdrop:!1,backdropColor:"rgba(255, 255, 255, 0.75)",backdropPadding:2}}),t.route("scale.ticks","color","","color"),t.route("scale.grid","color","","borderColor"),t.route("scale.border","color","","borderColor"),t.route("scale.title","color","","color"),t.describe("scale",{_fallback:!1,_scriptable:e=>!e.startsWith("before")&&!e.startsWith("after")&&e!=="callback"&&e!=="parser",_indexable:e=>e!=="borderDash"&&e!=="tickBorderDash"&&e!=="dash"}),t.describe("scales",{_fallback:"scale"}),t.describe("scale.ticks",{_scriptable:e=>e!=="backdropPadding"&&e!=="callback",_indexable:e=>e!=="backdropPadding"})}const qn=Object.create(null),rc=Object.create(null);function Pi(t,e){if(!e)return t;const n=e.split(".");for(let r=0,i=n.length;r<i;++r){const s=n[r];t=t[s]||(t[s]=Object.create(null))}return t}function Vo(t,e,n){return typeof e=="string"?Gi(Pi(t,e),n):Gi(Pi(t,""),e)}class iw{constructor(e,n){this.animation=void 0,this.backgroundColor="rgba(0,0,0,0.1)",this.borderColor="rgba(0,0,0,0.1)",this.color="#666",this.datasets={},this.devicePixelRatio=r=>r.chart.platform.getDevicePixelRatio(),this.elements={},this.events=["mousemove","mouseout","click","touchstart","touchmove"],this.font={family:"'Helvetica Neue', 'Helvetica', 'Arial', sans-serif",size:12,style:"normal",lineHeight:1.2,weight:null},this.hover={},this.hoverBackgroundColor=(r,i)=>Wo(i.backgroundColor),this.hoverBorderColor=(r,i)=>Wo(i.borderColor),this.hoverColor=(r,i)=>Wo(i.color),this.indexAxis="x",this.interaction={mode:"nearest",intersect:!0,includeInvisible:!1},this.maintainAspectRatio=!0,this.onHover=null,this.onClick=null,this.parsing=!0,this.plugins={},this.responsive=!0,this.scale=void 0,this.scales={},this.showLine=!0,this.drawActiveElementsOnTop=!0,this.describe(e),this.apply(n)}set(e,n){return Vo(this,e,n)}get(e){return Pi(this,e)}describe(e,n){return Vo(rc,e,n)}override(e,n){return Vo(qn,e,n)}route(e,n,r,i){const s=Pi(this,e),a=Pi(this,r),o="_"+n;Object.defineProperties(s,{[o]:{value:s[n],writable:!0},[n]:{enumerable:!0,get(){const l=this[o],u=a[i];return V(l)?Object.assign({},u,l):X(l,u)},set(l){this[o]=l}}})}apply(e){e.forEach(n=>n(this))}}var de=new iw({_scriptable:t=>!t.startsWith("on"),_indexable:t=>t!=="events",hover:{_fallback:"interaction"},interaction:{_scriptable:!1,_indexable:!1}},[Z1,J1,rw]);function sw(t){return!t||q(t.size)||q(t.family)?null:(t.style?t.style+" ":"")+(t.weight?t.weight+" ":"")+t.size+"px "+t.family}function fh(t,e,n,r,i){let s=e[i];return s||(s=e[i]=t.measureText(i).width,n.push(i)),s>r&&(r=s),r}function Tn(t,e,n){const r=t.currentDevicePixelRatio,i=n!==0?Math.max(n/2,.5):0;return Math.round((e-i)*r)/r+i}function mh(t,e){!e&&!t||(e=e||t.getContext("2d"),e.save(),e.resetTransform(),e.clearRect(0,0,t.width,t.height),e.restore())}function ph(t,e,n,r){dg(t,e,n,r,null)}function dg(t,e,n,r,i){let s,a,o,l,u,d,h,f;const m=e.pointStyle,x=e.rotation,v=e.radius;let w=(x||0)*A1;if(m&&typeof m=="object"&&(s=m.toString(),s==="[object HTMLImageElement]"||s==="[object HTMLCanvasElement]")){t.save(),t.translate(n,r),t.rotate(w),t.drawImage(m,-m.width/2,-m.height/2,m.width,m.height),t.restore();return}if(!(isNaN(v)||v<=0)){switch(t.beginPath(),m){default:i?t.ellipse(n,r,i/2,v,0,0,fe):t.arc(n,r,v,0,fe),t.closePath();break;case"triangle":d=i?i/2:v,t.moveTo(n+Math.sin(w)*d,r-Math.cos(w)*v),w+=sh,t.lineTo(n+Math.sin(w)*d,r-Math.cos(w)*v),w+=sh,t.lineTo(n+Math.sin(w)*d,r-Math.cos(w)*v),t.closePath();break;case"rectRounded":u=v*.516,l=v-u,a=Math.cos(w+Pn)*l,h=Math.cos(w+Pn)*(i?i/2-u:l),o=Math.sin(w+Pn)*l,f=Math.sin(w+Pn)*(i?i/2-u:l),t.arc(n-h,r-o,u,w-me,w-ye),t.arc(n+f,r-a,u,w-ye,w),t.arc(n+h,r+o,u,w,w+ye),t.arc(n-f,r+a,u,w+ye,w+me),t.closePath();break;case"rect":if(!x){l=Math.SQRT1_2*v,d=i?i/2:l,t.rect(n-d,r-l,2*d,2*l);break}w+=Pn;case"rectRot":h=Math.cos(w)*(i?i/2:v),a=Math.cos(w)*v,o=Math.sin(w)*v,f=Math.sin(w)*(i?i/2:v),t.moveTo(n-h,r-o),t.lineTo(n+f,r-a),t.lineTo(n+h,r+o),t.lineTo(n-f,r+a),t.closePath();break;case"crossRot":w+=Pn;case"cross":h=Math.cos(w)*(i?i/2:v),a=Math.cos(w)*v,o=Math.sin(w)*v,f=Math.sin(w)*(i?i/2:v),t.moveTo(n-h,r-o),t.lineTo(n+h,r+o),t.moveTo(n+f,r-a),t.lineTo(n-f,r+a);break;case"star":h=Math.cos(w)*(i?i/2:v),a=Math.cos(w)*v,o=Math.sin(w)*v,f=Math.sin(w)*(i?i/2:v),t.moveTo(n-h,r-o),t.lineTo(n+h,r+o),t.moveTo(n+f,r-a),t.lineTo(n-f,r+a),w+=Pn,h=Math.cos(w)*(i?i/2:v),a=Math.cos(w)*v,o=Math.sin(w)*v,f=Math.sin(w)*(i?i/2:v),t.moveTo(n-h,r-o),t.lineTo(n+h,r+o),t.moveTo(n+f,r-a),t.lineTo(n-f,r+a);break;case"line":a=i?i/2:Math.cos(w)*v,o=Math.sin(w)*v,t.moveTo(n-a,r-o),t.lineTo(n+a,r+o);break;case"dash":t.moveTo(n,r),t.lineTo(n+Math.cos(w)*(i?i/2:v),r+Math.sin(w)*v);break;case!1:t.closePath();break}t.fill(),e.borderWidth>0&&t.stroke()}}function hg(t,e,n){return n=n||.5,!e||t&&t.x>e.left-n&&t.x<e.right+n&&t.y>e.top-n&&t.y<e.bottom+n}function fu(t,e){t.save(),t.beginPath(),t.rect(e.left,e.top,e.right-e.left,e.bottom-e.top),t.clip()}function mu(t){t.restore()}function aw(t,e){e.translation&&t.translate(e.translation[0],e.translation[1]),q(e.rotation)||t.rotate(e.rotation),e.color&&(t.fillStyle=e.color),e.textAlign&&(t.textAlign=e.textAlign),e.textBaseline&&(t.textBaseline=e.textBaseline)}function ow(t,e,n,r,i){if(i.strikethrough||i.underline){const s=t.measureText(r),a=e-s.actualBoundingBoxLeft,o=e+s.actualBoundingBoxRight,l=n-s.actualBoundingBoxAscent,u=n+s.actualBoundingBoxDescent,d=i.strikethrough?(l+u)/2:u;t.strokeStyle=t.fillStyle,t.beginPath(),t.lineWidth=i.decorationWidth||2,t.moveTo(a,d),t.lineTo(o,d),t.stroke()}}function lw(t,e){const n=t.fillStyle;t.fillStyle=e.color,t.fillRect(e.left,e.top,e.width,e.height),t.fillStyle=n}function Zi(t,e,n,r,i,s={}){const a=xe(e)?e:[e],o=s.strokeWidth>0&&s.strokeColor!=="";let l,u;for(t.save(),t.font=i.string,aw(t,s),l=0;l<a.length;++l)u=a[l],s.backdrop&&lw(t,s.backdrop),o&&(s.strokeColor&&(t.strokeStyle=s.strokeColor),q(s.strokeWidth)||(t.lineWidth=s.strokeWidth),t.strokeText(u,n,r,s.maxWidth)),t.fillText(u,n,r,s.maxWidth),ow(t,n,r,u,s),r+=Number(i.lineHeight);t.restore()}function za(t,e){const{x:n,y:r,w:i,h:s,radius:a}=e;t.arc(n+a.topLeft,r+a.topLeft,a.topLeft,1.5*me,me,!0),t.lineTo(n,r+s-a.bottomLeft),t.arc(n+a.bottomLeft,r+s-a.bottomLeft,a.bottomLeft,me,ye,!0),t.lineTo(n+i-a.bottomRight,r+s),t.arc(n+i-a.bottomRight,r+s-a.bottomRight,a.bottomRight,ye,0,!0),t.lineTo(n+i,r+a.topRight),t.arc(n+i-a.topRight,r+a.topRight,a.topRight,0,-ye,!0),t.lineTo(n+a.topLeft,r)}const cw=/^(normal|(\d+(?:\.\d+)?)(px|em|%)?)$/,uw=/^(normal|italic|initial|inherit|unset|(oblique( -?[0-9]?[0-9]deg)?))$/;function dw(t,e){const n=(""+t).match(cw);if(!n||n[1]==="normal")return e*1.2;switch(t=+n[2],n[3]){case"px":return t;case"%":t/=100;break}return e*t}const hw=t=>+t||0;function pu(t,e){const n={},r=V(e),i=r?Object.keys(e):e,s=V(t)?r?a=>X(t[a],t[e[a]]):a=>t[a]:()=>t;for(const a of i)n[a]=hw(s(a));return n}function fg(t){return pu(t,{top:"y",right:"x",bottom:"y",left:"x"})}function jr(t){return pu(t,["topLeft","topRight","bottomLeft","bottomRight"])}function ut(t){const e=fg(t);return e.width=e.left+e.right,e.height=e.top+e.bottom,e}function Me(t,e){t=t||{},e=e||de.font;let n=X(t.size,e.size);typeof n=="string"&&(n=parseInt(n,10));let r=X(t.style,e.style);r&&!(""+r).match(uw)&&(console.warn('Invalid font style specified: "'+r+'"'),r=void 0);const i={family:X(t.family,e.family),lineHeight:dw(X(t.lineHeight,e.lineHeight),n),size:n,style:r,weight:X(t.weight,e.weight),string:""};return i.string=sw(i),i}function Ms(t,e,n,r){let i,s,a;for(i=0,s=t.length;i<s;++i)if(a=t[i],a!==void 0&&a!==void 0)return a}function fw(t,e,n){const{min:r,max:i}=t,s=tg(e,(i-r)/2),a=(o,l)=>n&&o===0?0:o+l;return{min:a(r,-Math.abs(s)),max:a(i,s)}}function Vr(t,e){return Object.assign(Object.create(t),e)}function gu(t,e=[""],n,r,i=()=>t[0]){const s=n||t;typeof r>"u"&&(r=xg("_fallback",t));const a={[Symbol.toStringTag]:"Object",_cacheable:!0,_scopes:t,_rootScopes:s,_fallback:r,_getTarget:i,override:o=>gu([o,...t],e,s,r)};return new Proxy(a,{deleteProperty(o,l){return delete o[l],delete o._keys,delete t[0][l],!0},get(o,l){return pg(o,l,()=>ww(l,e,t,o))},getOwnPropertyDescriptor(o,l){return Reflect.getOwnPropertyDescriptor(o._scopes[0],l)},getPrototypeOf(){return Reflect.getPrototypeOf(t[0])},has(o,l){return xh(o).includes(l)},ownKeys(o){return xh(o)},set(o,l,u){const d=o._storage||(o._storage=i());return o[l]=d[l]=u,delete o._keys,!0}})}function Fr(t,e,n,r){const i={_cacheable:!1,_proxy:t,_context:e,_subProxy:n,_stack:new Set,_descriptors:mg(t,r),setContext:s=>Fr(t,s,n,r),override:s=>Fr(t.override(s),e,n,r)};return new Proxy(i,{deleteProperty(s,a){return delete s[a],delete t[a],!0},get(s,a,o){return pg(s,a,()=>pw(s,a,o))},getOwnPropertyDescriptor(s,a){return s._descriptors.allKeys?Reflect.has(t,a)?{enumerable:!0,configurable:!0}:void 0:Reflect.getOwnPropertyDescriptor(t,a)},getPrototypeOf(){return Reflect.getPrototypeOf(t)},has(s,a){return Reflect.has(t,a)},ownKeys(){return Reflect.ownKeys(t)},set(s,a,o){return t[a]=o,delete s[a],!0}})}function mg(t,e={scriptable:!0,indexable:!0}){const{_scriptable:n=e.scriptable,_indexable:r=e.indexable,_allKeys:i=e.allKeys}=t;return{allKeys:i,scriptable:n,indexable:r,isScriptable:Nn(n)?n:()=>n,isIndexable:Nn(r)?r:()=>r}}const mw=(t,e)=>t?t+cu(e):e,xu=(t,e)=>V(e)&&t!=="adapters"&&(Object.getPrototypeOf(e)===null||e.constructor===Object);function pg(t,e,n){if(Object.prototype.hasOwnProperty.call(t,e)||e==="constructor")return t[e];const r=n();return t[e]=r,r}function pw(t,e,n){const{_proxy:r,_context:i,_subProxy:s,_descriptors:a}=t;let o=r[e];return Nn(o)&&a.isScriptable(e)&&(o=gw(e,o,t,n)),xe(o)&&o.length&&(o=xw(e,o,t,a.isIndexable)),xu(e,o)&&(o=Fr(o,i,s&&s[e],a)),o}function gw(t,e,n,r){const{_proxy:i,_context:s,_subProxy:a,_stack:o}=n;if(o.has(t))throw new Error("Recursion detected: "+Array.from(o).join("->")+"->"+t);o.add(t);let l=e(s,a||r);return o.delete(t),xu(t,l)&&(l=vu(i._scopes,i,t,l)),l}function xw(t,e,n,r){const{_proxy:i,_context:s,_subProxy:a,_descriptors:o}=n;if(typeof s.index<"u"&&r(t))return e[s.index%e.length];if(V(e[0])){const l=e,u=i._scopes.filter(d=>d!==l);e=[];for(const d of l){const h=vu(u,i,t,d);e.push(Fr(h,s,a&&a[t],o))}}return e}function gg(t,e,n){return Nn(t)?t(e,n):t}const vw=(t,e)=>t===!0?e:typeof t=="string"?Gn(e,t):void 0;function yw(t,e,n,r,i){for(const s of e){const a=vw(n,s);if(a){t.add(a);const o=gg(a._fallback,n,i);if(typeof o<"u"&&o!==n&&o!==r)return o}else if(a===!1&&typeof r<"u"&&n!==r)return null}return!1}function vu(t,e,n,r){const i=e._rootScopes,s=gg(e._fallback,n,r),a=[...t,...i],o=new Set;o.add(r);let l=gh(o,a,n,s||n,r);return l===null||typeof s<"u"&&s!==n&&(l=gh(o,a,s,l,r),l===null)?!1:gu(Array.from(o),[""],i,s,()=>bw(e,n,r))}function gh(t,e,n,r,i){for(;n;)n=yw(t,e,n,r,i);return n}function bw(t,e,n){const r=t._getTarget();e in r||(r[e]={});const i=r[e];return xe(i)&&V(n)?n:i||{}}function ww(t,e,n,r){let i;for(const s of e)if(i=xg(mw(s,t),n),typeof i<"u")return xu(t,i)?vu(n,r,t,i):i}function xg(t,e){for(const n of e){if(!n)continue;const r=n[t];if(typeof r<"u")return r}}function xh(t){let e=t._keys;return e||(e=t._keys=Sw(t._scopes)),e}function Sw(t){const e=new Set;for(const n of t)for(const r of Object.keys(n).filter(i=>!i.startsWith("_")))e.add(r);return Array.from(e)}function yu(){return typeof window<"u"&&typeof document<"u"}function bu(t){let e=t.parentNode;return e&&e.toString()==="[object ShadowRoot]"&&(e=e.host),e}function Ia(t,e,n){let r;return typeof t=="string"?(r=parseInt(t,10),t.indexOf("%")!==-1&&(r=r/100*e.parentNode[n])):r=t,r}const uo=t=>t.ownerDocument.defaultView.getComputedStyle(t,null);function Nw(t,e){return uo(t).getPropertyValue(e)}const _w=["top","right","bottom","left"];function Hn(t,e,n){const r={};n=n?"-"+n:"";for(let i=0;i<4;i++){const s=_w[i];r[s]=parseFloat(t[e+"-"+s+n])||0}return r.width=r.left+r.right,r.height=r.top+r.bottom,r}const kw=(t,e,n)=>(t>0||e>0)&&(!n||!n.shadowRoot);function jw(t,e){const n=t.touches,r=n&&n.length?n[0]:t,{offsetX:i,offsetY:s}=r;let a=!1,o,l;if(kw(i,s,t.target))o=i,l=s;else{const u=e.getBoundingClientRect();o=r.clientX-u.left,l=r.clientY-u.top,a=!0}return{x:o,y:l,box:a}}function Ln(t,e){if("native"in t)return t;const{canvas:n,currentDevicePixelRatio:r}=e,i=uo(n),s=i.boxSizing==="border-box",a=Hn(i,"padding"),o=Hn(i,"border","width"),{x:l,y:u,box:d}=jw(t,n),h=a.left+(d&&o.left),f=a.top+(d&&o.top);let{width:m,height:x}=e;return s&&(m-=a.width+o.width,x-=a.height+o.height),{x:Math.round((l-h)/m*n.width/r),y:Math.round((u-f)/x*n.height/r)}}function Cw(t,e,n){let r,i;if(e===void 0||n===void 0){const s=t&&bu(t);if(!s)e=t.clientWidth,n=t.clientHeight;else{const a=s.getBoundingClientRect(),o=uo(s),l=Hn(o,"border","width"),u=Hn(o,"padding");e=a.width-u.width-l.width,n=a.height-u.height-l.height,r=Ia(o.maxWidth,s,"clientWidth"),i=Ia(o.maxHeight,s,"clientHeight")}}return{width:e,height:n,maxWidth:r||La,maxHeight:i||La}}const $s=t=>Math.round(t*10)/10;function Pw(t,e,n,r){const i=uo(t),s=Hn(i,"margin"),a=Ia(i.maxWidth,t,"clientWidth")||La,o=Ia(i.maxHeight,t,"clientHeight")||La,l=Cw(t,e,n);let{width:u,height:d}=l;if(i.boxSizing==="content-box"){const f=Hn(i,"border","width"),m=Hn(i,"padding");u-=m.width+f.width,d-=m.height+f.height}return u=Math.max(0,u-s.width),d=Math.max(0,r?u/r:d-s.height),u=$s(Math.min(u,a,l.maxWidth)),d=$s(Math.min(d,o,l.maxHeight)),u&&!d&&(d=$s(u/2)),(e!==void 0||n!==void 0)&&r&&l.height&&d>l.height&&(d=l.height,u=$s(Math.floor(d*r))),{width:u,height:d}}function vh(t,e,n){const r=e||1,i=Math.floor(t.height*r),s=Math.floor(t.width*r);t.height=Math.floor(t.height),t.width=Math.floor(t.width);const a=t.canvas;return a.style&&(n||!a.style.height&&!a.style.width)&&(a.style.height=`${t.height}px`,a.style.width=`${t.width}px`),t.currentDevicePixelRatio!==r||a.height!==i||a.width!==s?(t.currentDevicePixelRatio=r,a.height=i,a.width=s,t.ctx.setTransform(r,0,0,r,0,0),!0):!1}const Ew=function(){let t=!1;try{const e={get passive(){return t=!0,!1}};yu()&&(window.addEventListener("test",null,e),window.removeEventListener("test",null,e))}catch{}return t}();function yh(t,e){const n=Nw(t,e),r=n&&n.match(/^(\d+)(\.\d+)?px$/);return r?+r[1]:void 0}const Tw=function(t,e){return{x(n){return t+t+e-n},setWidth(n){e=n},textAlign(n){return n==="center"?n:n==="right"?"left":"right"},xPlus(n,r){return n-r},leftForLtr(n,r){return n-r}}},Mw=function(){return{x(t){return t},setWidth(t){},textAlign(t){return t},xPlus(t,e){return t+e},leftForLtr(t,e){return t}}};function Cr(t,e,n){return t?Tw(e,n):Mw()}function vg(t,e){let n,r;(e==="ltr"||e==="rtl")&&(n=t.canvas.style,r=[n.getPropertyValue("direction"),n.getPropertyPriority("direction")],n.setProperty("direction",e,"important"),t.prevTextDirection=r)}function yg(t,e){e!==void 0&&(delete t.prevTextDirection,t.canvas.style.setProperty("direction",e[0],e[1]))}function Rs(t,e,n){return t.options.clip?t[n]:e[n]}function $w(t,e){const{xScale:n,yScale:r}=t;return n&&r?{left:Rs(n,e,"left"),right:Rs(n,e,"right"),top:Rs(r,e,"top"),bottom:Rs(r,e,"bottom")}:e}function Rw(t,e){const n=e._clip;if(n.disabled)return!1;const r=$w(e,t.chartArea);return{left:n.left===!1?0:r.left-(n.left===!0?0:n.left),right:n.right===!1?t.width:r.right+(n.right===!0?0:n.right),top:n.top===!1?0:r.top-(n.top===!0?0:n.top),bottom:n.bottom===!1?t.height:r.bottom+(n.bottom===!0?0:n.bottom)}}/*!
 * Chart.js v4.4.9
 * https://www.chartjs.org
 * (c) 2025 Chart.js Contributors
 * Released under the MIT License
 */class Dw{constructor(){this._request=null,this._charts=new Map,this._running=!1,this._lastDate=void 0}_notify(e,n,r,i){const s=n.listeners[i],a=n.duration;s.forEach(o=>o({chart:e,initial:n.initial,numSteps:a,currentStep:Math.min(r-n.start,a)}))}_refresh(){this._request||(this._running=!0,this._request=og.call(window,()=>{this._update(),this._request=null,this._running&&this._refresh()}))}_update(e=Date.now()){let n=0;this._charts.forEach((r,i)=>{if(!r.running||!r.items.length)return;const s=r.items;let a=s.length-1,o=!1,l;for(;a>=0;--a)l=s[a],l._active?(l._total>r.duration&&(r.duration=l._total),l.tick(e),o=!0):(s[a]=s[s.length-1],s.pop());o&&(i.draw(),this._notify(i,r,e,"progress")),s.length||(r.running=!1,this._notify(i,r,e,"complete"),r.initial=!1),n+=s.length}),this._lastDate=e,n===0&&(this._running=!1)}_getAnims(e){const n=this._charts;let r=n.get(e);return r||(r={running:!1,initial:!0,items:[],listeners:{complete:[],progress:[]}},n.set(e,r)),r}listen(e,n,r){this._getAnims(e).listeners[n].push(r)}add(e,n){!n||!n.length||this._getAnims(e).items.push(...n)}has(e){return this._getAnims(e).items.length>0}start(e){const n=this._charts.get(e);n&&(n.running=!0,n.start=Date.now(),n.duration=n.items.reduce((r,i)=>Math.max(r,i._duration),0),this._refresh())}running(e){if(!this._running)return!1;const n=this._charts.get(e);return!(!n||!n.running||!n.items.length)}stop(e){const n=this._charts.get(e);if(!n||!n.items.length)return;const r=n.items;let i=r.length-1;for(;i>=0;--i)r[i].cancel();n.items=[],this._notify(e,n,Date.now(),"complete")}remove(e){return this._charts.delete(e)}}var Ot=new Dw;const bh="transparent",Ow={boolean(t,e,n){return n>.5?e:t},color(t,e,n){const r=dh(t||bh),i=r.valid&&dh(e||bh);return i&&i.valid?i.mix(r,n).hexString():e},number(t,e,n){return t+(e-t)*n}};let Lw=class{constructor(e,n,r,i){const s=n[r];i=Ms([e.to,i,s,e.from]);const a=Ms([e.from,s,i]);this._active=!0,this._fn=e.fn||Ow[e.type||typeof a],this._easing=Ci[e.easing]||Ci.linear,this._start=Math.floor(Date.now()+(e.delay||0)),this._duration=this._total=Math.floor(e.duration),this._loop=!!e.loop,this._target=n,this._prop=r,this._from=a,this._to=i,this._promises=void 0}active(){return this._active}update(e,n,r){if(this._active){this._notify(!1);const i=this._target[this._prop],s=r-this._start,a=this._duration-s;this._start=r,this._duration=Math.floor(Math.max(a,e.duration)),this._total+=s,this._loop=!!e.loop,this._to=Ms([e.to,n,i,e.from]),this._from=Ms([e.from,i,n])}}cancel(){this._active&&(this.tick(Date.now()),this._active=!1,this._notify(!1))}tick(e){const n=e-this._start,r=this._duration,i=this._prop,s=this._from,a=this._loop,o=this._to;let l;if(this._active=s!==o&&(a||n<r),!this._active){this._target[i]=o,this._notify(!0);return}if(n<0){this._target[i]=s;return}l=n/r%2,l=a&&l>1?2-l:l,l=this._easing(Math.min(1,Math.max(0,l))),this._target[i]=this._fn(s,o,l)}wait(){const e=this._promises||(this._promises=[]);return new Promise((n,r)=>{e.push({res:n,rej:r})})}_notify(e){const n=e?"res":"rej",r=this._promises||[];for(let i=0;i<r.length;i++)r[i][n]()}};class bg{constructor(e,n){this._chart=e,this._properties=new Map,this.configure(n)}configure(e){if(!V(e))return;const n=Object.keys(de.animation),r=this._properties;Object.getOwnPropertyNames(e).forEach(i=>{const s=e[i];if(!V(s))return;const a={};for(const o of n)a[o]=s[o];(xe(s.properties)&&s.properties||[i]).forEach(o=>{(o===i||!r.has(o))&&r.set(o,a)})})}_animateOptions(e,n){const r=n.options,i=Fw(e,r);if(!i)return[];const s=this._createAnimations(i,r);return r.$shared&&Aw(e.options.$animations,r).then(()=>{e.options=r},()=>{}),s}_createAnimations(e,n){const r=this._properties,i=[],s=e.$animations||(e.$animations={}),a=Object.keys(n),o=Date.now();let l;for(l=a.length-1;l>=0;--l){const u=a[l];if(u.charAt(0)==="$")continue;if(u==="options"){i.push(...this._animateOptions(e,n));continue}const d=n[u];let h=s[u];const f=r.get(u);if(h)if(f&&h.active()){h.update(f,d,o);continue}else h.cancel();if(!f||!f.duration){e[u]=d;continue}s[u]=h=new Lw(f,e,u,d),i.push(h)}return i}update(e,n){if(this._properties.size===0){Object.assign(e,n);return}const r=this._createAnimations(e,n);if(r.length)return Ot.add(this._chart,r),!0}}function Aw(t,e){const n=[],r=Object.keys(e);for(let i=0;i<r.length;i++){const s=t[r[i]];s&&s.active()&&n.push(s.wait())}return Promise.all(n)}function Fw(t,e){if(!e)return;let n=t.options;if(!n){t.options=e;return}return n.$shared&&(t.options=n=Object.assign({},n,{$shared:!1,$animations:{}})),n}function wh(t,e){const n=t&&t.options||{},r=n.reverse,i=n.min===void 0?e:0,s=n.max===void 0?e:0;return{start:r?s:i,end:r?i:s}}function zw(t,e,n){if(n===!1)return!1;const r=wh(t,n),i=wh(e,n);return{top:i.end,right:r.end,bottom:i.start,left:r.start}}function Iw(t){let e,n,r,i;return V(t)?(e=t.top,n=t.right,r=t.bottom,i=t.left):e=n=r=i=t,{top:e,right:n,bottom:r,left:i,disabled:t===!1}}function wg(t,e){const n=[],r=t._getSortedDatasetMetas(e);let i,s;for(i=0,s=r.length;i<s;++i)n.push(r[i].index);return n}function Sh(t,e,n,r={}){const i=t.keys,s=r.mode==="single";let a,o,l,u;if(e===null)return;let d=!1;for(a=0,o=i.length;a<o;++a){if(l=+i[a],l===n){if(d=!0,r.all)continue;break}u=t.values[l],ct(u)&&(s||e===0||bn(e)===bn(u))&&(e+=u)}return!d&&!r.all?0:e}function Uw(t,e){const{iScale:n,vScale:r}=e,i=n.axis==="x"?"x":"y",s=r.axis==="x"?"x":"y",a=Object.keys(t),o=new Array(a.length);let l,u,d;for(l=0,u=a.length;l<u;++l)d=a[l],o[l]={[i]:d,[s]:t[d]};return o}function Yo(t,e){const n=t&&t.options.stacked;return n||n===void 0&&e.stack!==void 0}function Bw(t,e,n){return`${t.id}.${e.id}.${n.stack||n.type}`}function Hw(t){const{min:e,max:n,minDefined:r,maxDefined:i}=t.getUserBounds();return{min:r?e:Number.NEGATIVE_INFINITY,max:i?n:Number.POSITIVE_INFINITY}}function Ww(t,e,n){const r=t[e]||(t[e]={});return r[n]||(r[n]={})}function Nh(t,e,n,r){for(const i of e.getMatchingVisibleMetas(r).reverse()){const s=t[i.index];if(n&&s>0||!n&&s<0)return i.index}return null}function _h(t,e){const{chart:n,_cachedMeta:r}=t,i=n._stacks||(n._stacks={}),{iScale:s,vScale:a,index:o}=r,l=s.axis,u=a.axis,d=Bw(s,a,r),h=e.length;let f;for(let m=0;m<h;++m){const x=e[m],{[l]:v,[u]:w}=x,g=x._stacks||(x._stacks={});f=g[u]=Ww(i,d,v),f[o]=w,f._top=Nh(f,a,!0,r.type),f._bottom=Nh(f,a,!1,r.type);const p=f._visualValues||(f._visualValues={});p[o]=w}}function Xo(t,e){const n=t.scales;return Object.keys(n).filter(r=>n[r].axis===e).shift()}function Vw(t,e){return Vr(t,{active:!1,dataset:void 0,datasetIndex:e,index:e,mode:"default",type:"dataset"})}function Yw(t,e,n){return Vr(t,{active:!1,dataIndex:e,parsed:void 0,raw:void 0,element:n,index:e,mode:"default",type:"data"})}function ri(t,e){const n=t.controller.index,r=t.vScale&&t.vScale.axis;if(r){e=e||t._parsed;for(const i of e){const s=i._stacks;if(!s||s[r]===void 0||s[r][n]===void 0)return;delete s[r][n],s[r]._visualValues!==void 0&&s[r]._visualValues[n]!==void 0&&delete s[r]._visualValues[n]}}}const Ko=t=>t==="reset"||t==="none",kh=(t,e)=>e?t:Object.assign({},t),Xw=(t,e,n)=>t&&!e.hidden&&e._stacked&&{keys:wg(n,!0),values:null};class Pr{constructor(e,n){this.chart=e,this._ctx=e.ctx,this.index=n,this._cachedDataOpts={},this._cachedMeta=this.getMeta(),this._type=this._cachedMeta.type,this.options=void 0,this._parsing=!1,this._data=void 0,this._objectData=void 0,this._sharedOptions=void 0,this._drawStart=void 0,this._drawCount=void 0,this.enableOptionSharing=!1,this.supportsDecimation=!1,this.$context=void 0,this._syncList=[],this.datasetElementType=new.target.datasetElementType,this.dataElementType=new.target.dataElementType,this.initialize()}initialize(){const e=this._cachedMeta;this.configure(),this.linkScales(),e._stacked=Yo(e.vScale,e),this.addElements(),this.options.fill&&!this.chart.isPluginEnabled("filler")&&console.warn("Tried to use the 'fill' option without the 'Filler' plugin enabled. Please import and register the 'Filler' plugin and make sure it is not disabled in the options")}updateIndex(e){this.index!==e&&ri(this._cachedMeta),this.index=e}linkScales(){const e=this.chart,n=this._cachedMeta,r=this.getDataset(),i=(h,f,m,x)=>h==="x"?f:h==="r"?x:m,s=n.xAxisID=X(r.xAxisID,Xo(e,"x")),a=n.yAxisID=X(r.yAxisID,Xo(e,"y")),o=n.rAxisID=X(r.rAxisID,Xo(e,"r")),l=n.indexAxis,u=n.iAxisID=i(l,s,a,o),d=n.vAxisID=i(l,a,s,o);n.xScale=this.getScaleForId(s),n.yScale=this.getScaleForId(a),n.rScale=this.getScaleForId(o),n.iScale=this.getScaleForId(u),n.vScale=this.getScaleForId(d)}getDataset(){return this.chart.data.datasets[this.index]}getMeta(){return this.chart.getDatasetMeta(this.index)}getScaleForId(e){return this.chart.scales[e]}_getOtherScale(e){const n=this._cachedMeta;return e===n.iScale?n.vScale:n.iScale}reset(){this._update("reset")}_destroy(){const e=this._cachedMeta;this._data&&lh(this._data,this),e._stacked&&ri(e)}_dataCheck(){const e=this.getDataset(),n=e.data||(e.data=[]),r=this._data;if(V(n)){const i=this._cachedMeta;this._data=Uw(n,i)}else if(r!==n){if(r){lh(r,this);const i=this._cachedMeta;ri(i),i._parsed=[]}n&&Object.isExtensible(n)&&X1(n,this),this._syncList=[],this._data=n}}addElements(){const e=this._cachedMeta;this._dataCheck(),this.datasetElementType&&(e.dataset=new this.datasetElementType)}buildOrUpdateElements(e){const n=this._cachedMeta,r=this.getDataset();let i=!1;this._dataCheck();const s=n._stacked;n._stacked=Yo(n.vScale,n),n.stack!==r.stack&&(i=!0,ri(n),n.stack=r.stack),this._resyncElements(e),(i||s!==n._stacked)&&(_h(this,n._parsed),n._stacked=Yo(n.vScale,n))}configure(){const e=this.chart.config,n=e.datasetScopeKeys(this._type),r=e.getOptionScopes(this.getDataset(),n,!0);this.options=e.createResolver(r,this.getContext()),this._parsing=this.options.parsing,this._cachedDataOpts={}}parse(e,n){const{_cachedMeta:r,_data:i}=this,{iScale:s,_stacked:a}=r,o=s.axis;let l=e===0&&n===i.length?!0:r._sorted,u=e>0&&r._parsed[e-1],d,h,f;if(this._parsing===!1)r._parsed=i,r._sorted=!0,f=i;else{xe(i[e])?f=this.parseArrayData(r,i,e,n):V(i[e])?f=this.parseObjectData(r,i,e,n):f=this.parsePrimitiveData(r,i,e,n);const m=()=>h[o]===null||u&&h[o]<u[o];for(d=0;d<n;++d)r._parsed[d+e]=h=f[d],l&&(m()&&(l=!1),u=h);r._sorted=l}a&&_h(this,f)}parsePrimitiveData(e,n,r,i){const{iScale:s,vScale:a}=e,o=s.axis,l=a.axis,u=s.getLabels(),d=s===a,h=new Array(i);let f,m,x;for(f=0,m=i;f<m;++f)x=f+r,h[f]={[o]:d||s.parse(u[x],x),[l]:a.parse(n[x],x)};return h}parseArrayData(e,n,r,i){const{xScale:s,yScale:a}=e,o=new Array(i);let l,u,d,h;for(l=0,u=i;l<u;++l)d=l+r,h=n[d],o[l]={x:s.parse(h[0],d),y:a.parse(h[1],d)};return o}parseObjectData(e,n,r,i){const{xScale:s,yScale:a}=e,{xAxisKey:o="x",yAxisKey:l="y"}=this._parsing,u=new Array(i);let d,h,f,m;for(d=0,h=i;d<h;++d)f=d+r,m=n[f],u[d]={x:s.parse(Gn(m,o),f),y:a.parse(Gn(m,l),f)};return u}getParsed(e){return this._cachedMeta._parsed[e]}getDataElement(e){return this._cachedMeta.data[e]}applyStack(e,n,r){const i=this.chart,s=this._cachedMeta,a=n[e.axis],o={keys:wg(i,!0),values:n._stacks[e.axis]._visualValues};return Sh(o,a,s.index,{mode:r})}updateRangeFromParsed(e,n,r,i){const s=r[n.axis];let a=s===null?NaN:s;const o=i&&r._stacks[n.axis];i&&o&&(i.values=o,a=Sh(i,s,this._cachedMeta.index)),e.min=Math.min(e.min,a),e.max=Math.max(e.max,a)}getMinMax(e,n){const r=this._cachedMeta,i=r._parsed,s=r._sorted&&e===r.iScale,a=i.length,o=this._getOtherScale(e),l=Xw(n,r,this.chart),u={min:Number.POSITIVE_INFINITY,max:Number.NEGATIVE_INFINITY},{min:d,max:h}=Hw(o);let f,m;function x(){m=i[f];const v=m[o.axis];return!ct(m[e.axis])||d>v||h<v}for(f=0;f<a&&!(!x()&&(this.updateRangeFromParsed(u,e,m,l),s));++f);if(s){for(f=a-1;f>=0;--f)if(!x()){this.updateRangeFromParsed(u,e,m,l);break}}return u}getAllParsedValues(e){const n=this._cachedMeta._parsed,r=[];let i,s,a;for(i=0,s=n.length;i<s;++i)a=n[i][e.axis],ct(a)&&r.push(a);return r}getMaxOverflow(){return!1}getLabelAndValue(e){const n=this._cachedMeta,r=n.iScale,i=n.vScale,s=this.getParsed(e);return{label:r?""+r.getLabelForValue(s[r.axis]):"",value:i?""+i.getLabelForValue(s[i.axis]):""}}_update(e){const n=this._cachedMeta;this.update(e||"default"),n._clip=Iw(X(this.options.clip,zw(n.xScale,n.yScale,this.getMaxOverflow())))}update(e){}draw(){const e=this._ctx,n=this.chart,r=this._cachedMeta,i=r.data||[],s=n.chartArea,a=[],o=this._drawStart||0,l=this._drawCount||i.length-o,u=this.options.drawActiveElementsOnTop;let d;for(r.dataset&&r.dataset.draw(e,s,o,l),d=o;d<o+l;++d){const h=i[d];h.hidden||(h.active&&u?a.push(h):h.draw(e,s))}for(d=0;d<a.length;++d)a[d].draw(e,s)}getStyle(e,n){const r=n?"active":"default";return e===void 0&&this._cachedMeta.dataset?this.resolveDatasetElementOptions(r):this.resolveDataElementOptions(e||0,r)}getContext(e,n,r){const i=this.getDataset();let s;if(e>=0&&e<this._cachedMeta.data.length){const a=this._cachedMeta.data[e];s=a.$context||(a.$context=Yw(this.getContext(),e,a)),s.parsed=this.getParsed(e),s.raw=i.data[e],s.index=s.dataIndex=e}else s=this.$context||(this.$context=Vw(this.chart.getContext(),this.index)),s.dataset=i,s.index=s.datasetIndex=this.index;return s.active=!!n,s.mode=r,s}resolveDatasetElementOptions(e){return this._resolveElementOptions(this.datasetElementType.id,e)}resolveDataElementOptions(e,n){return this._resolveElementOptions(this.dataElementType.id,n,e)}_resolveElementOptions(e,n="default",r){const i=n==="active",s=this._cachedDataOpts,a=e+"-"+n,o=s[a],l=this.enableOptionSharing&&qi(r);if(o)return kh(o,l);const u=this.chart.config,d=u.datasetElementScopeKeys(this._type,e),h=i?[`${e}Hover`,"hover",e,""]:[e,""],f=u.getOptionScopes(this.getDataset(),d),m=Object.keys(de.elements[e]),x=()=>this.getContext(r,i,n),v=u.resolveNamedOptions(f,m,x,h);return v.$shared&&(v.$shared=l,s[a]=Object.freeze(kh(v,l))),v}_resolveAnimations(e,n,r){const i=this.chart,s=this._cachedDataOpts,a=`animation-${n}`,o=s[a];if(o)return o;let l;if(i.options.animation!==!1){const d=this.chart.config,h=d.datasetAnimationScopeKeys(this._type,n),f=d.getOptionScopes(this.getDataset(),h);l=d.createResolver(f,this.getContext(e,r,n))}const u=new bg(i,l&&l.animations);return l&&l._cacheable&&(s[a]=Object.freeze(u)),u}getSharedOptions(e){if(e.$shared)return this._sharedOptions||(this._sharedOptions=Object.assign({},e))}includeOptions(e,n){return!n||Ko(e)||this.chart._animationsDisabled}_getSharedOptions(e,n){const r=this.resolveDataElementOptions(e,n),i=this._sharedOptions,s=this.getSharedOptions(r),a=this.includeOptions(n,s)||s!==i;return this.updateSharedOptions(s,n,r),{sharedOptions:s,includeOptions:a}}updateElement(e,n,r,i){Ko(i)?Object.assign(e,r):this._resolveAnimations(n,i).update(e,r)}updateSharedOptions(e,n,r){e&&!Ko(n)&&this._resolveAnimations(void 0,n).update(e,r)}_setStyle(e,n,r,i){e.active=i;const s=this.getStyle(n,i);this._resolveAnimations(n,r,i).update(e,{options:!i&&this.getSharedOptions(s)||s})}removeHoverStyle(e,n,r){this._setStyle(e,r,"active",!1)}setHoverStyle(e,n,r){this._setStyle(e,r,"active",!0)}_removeDatasetHoverStyle(){const e=this._cachedMeta.dataset;e&&this._setStyle(e,void 0,"active",!1)}_setDatasetHoverStyle(){const e=this._cachedMeta.dataset;e&&this._setStyle(e,void 0,"active",!0)}_resyncElements(e){const n=this._data,r=this._cachedMeta.data;for(const[o,l,u]of this._syncList)this[o](l,u);this._syncList=[];const i=r.length,s=n.length,a=Math.min(s,i);a&&this.parse(0,a),s>i?this._insertElements(i,s-i,e):s<i&&this._removeElements(s,i-s)}_insertElements(e,n,r=!0){const i=this._cachedMeta,s=i.data,a=e+n;let o;const l=u=>{for(u.length+=n,o=u.length-1;o>=a;o--)u[o]=u[o-n]};for(l(s),o=e;o<a;++o)s[o]=new this.dataElementType;this._parsing&&l(i._parsed),this.parse(e,n),r&&this.updateElements(s,e,n,"reset")}updateElements(e,n,r,i){}_removeElements(e,n){const r=this._cachedMeta;if(this._parsing){const i=r._parsed.splice(e,n);r._stacked&&ri(r,i)}r.data.splice(e,n)}_sync(e){if(this._parsing)this._syncList.push(e);else{const[n,r,i]=e;this[n](r,i)}this.chart._dataChanges.push([this.index,...e])}_onDataPush(){const e=arguments.length;this._sync(["_insertElements",this.getDataset().data.length-e,e])}_onDataPop(){this._sync(["_removeElements",this._cachedMeta.data.length-1,1])}_onDataShift(){this._sync(["_removeElements",0,1])}_onDataSplice(e,n){n&&this._sync(["_removeElements",e,n]);const r=arguments.length-2;r&&this._sync(["_insertElements",e,r])}_onDataUnshift(){this._sync(["_insertElements",0,arguments.length])}}F(Pr,"defaults",{}),F(Pr,"datasetElementType",null),F(Pr,"dataElementType",null);function Kw(t,e){if(!t._cache.$bar){const n=t.getMatchingVisibleMetas(e);let r=[];for(let i=0,s=n.length;i<s;i++)r=r.concat(n[i].controller.getAllParsedValues(t));t._cache.$bar=ag(r.sort((i,s)=>i-s))}return t._cache.$bar}function Qw(t){const e=t.iScale,n=Kw(e,t.type);let r=e._length,i,s,a,o;const l=()=>{a===32767||a===-32768||(qi(o)&&(r=Math.min(r,Math.abs(a-o)||r)),o=a)};for(i=0,s=n.length;i<s;++i)a=e.getPixelForValue(n[i]),l();for(o=void 0,i=0,s=e.ticks.length;i<s;++i)a=e.getPixelForTick(i),l();return r}function Gw(t,e,n,r){const i=n.barThickness;let s,a;return q(i)?(s=e.min*n.categoryPercentage,a=n.barPercentage):(s=i*r,a=1),{chunk:s/r,ratio:a,start:e.pixels[t]-s/2}}function qw(t,e,n,r){const i=e.pixels,s=i[t];let a=t>0?i[t-1]:null,o=t<i.length-1?i[t+1]:null;const l=n.categoryPercentage;a===null&&(a=s-(o===null?e.end-e.start:o-s)),o===null&&(o=s+s-a);const u=s-(s-Math.min(a,o))/2*l;return{chunk:Math.abs(o-a)/2*l/r,ratio:n.barPercentage,start:u}}function Zw(t,e,n,r){const i=n.parse(t[0],r),s=n.parse(t[1],r),a=Math.min(i,s),o=Math.max(i,s);let l=a,u=o;Math.abs(a)>Math.abs(o)&&(l=o,u=a),e[n.axis]=u,e._custom={barStart:l,barEnd:u,start:i,end:s,min:a,max:o}}function Sg(t,e,n,r){return xe(t)?Zw(t,e,n,r):e[n.axis]=n.parse(t,r),e}function jh(t,e,n,r){const i=t.iScale,s=t.vScale,a=i.getLabels(),o=i===s,l=[];let u,d,h,f;for(u=n,d=n+r;u<d;++u)f=e[u],h={},h[i.axis]=o||i.parse(a[u],u),l.push(Sg(f,h,s,u));return l}function Qo(t){return t&&t.barStart!==void 0&&t.barEnd!==void 0}function Jw(t,e,n){return t!==0?bn(t):(e.isHorizontal()?1:-1)*(e.min>=n?1:-1)}function e2(t){let e,n,r,i,s;return t.horizontal?(e=t.base>t.x,n="left",r="right"):(e=t.base<t.y,n="bottom",r="top"),e?(i="end",s="start"):(i="start",s="end"),{start:n,end:r,reverse:e,top:i,bottom:s}}function t2(t,e,n,r){let i=e.borderSkipped;const s={};if(!i){t.borderSkipped=s;return}if(i===!0){t.borderSkipped={top:!0,right:!0,bottom:!0,left:!0};return}const{start:a,end:o,reverse:l,top:u,bottom:d}=e2(t);i==="middle"&&n&&(t.enableBorderRadius=!0,(n._top||0)===r?i=u:(n._bottom||0)===r?i=d:(s[Ch(d,a,o,l)]=!0,i=u)),s[Ch(i,a,o,l)]=!0,t.borderSkipped=s}function Ch(t,e,n,r){return r?(t=n2(t,e,n),t=Ph(t,n,e)):t=Ph(t,e,n),t}function n2(t,e,n){return t===e?n:t===n?e:t}function Ph(t,e,n){return t==="start"?e:t==="end"?n:t}function r2(t,{inflateAmount:e},n){t.inflateAmount=e==="auto"?n===1?.33:0:e}class Js extends Pr{parsePrimitiveData(e,n,r,i){return jh(e,n,r,i)}parseArrayData(e,n,r,i){return jh(e,n,r,i)}parseObjectData(e,n,r,i){const{iScale:s,vScale:a}=e,{xAxisKey:o="x",yAxisKey:l="y"}=this._parsing,u=s.axis==="x"?o:l,d=a.axis==="x"?o:l,h=[];let f,m,x,v;for(f=r,m=r+i;f<m;++f)v=n[f],x={},x[s.axis]=s.parse(Gn(v,u),f),h.push(Sg(Gn(v,d),x,a,f));return h}updateRangeFromParsed(e,n,r,i){super.updateRangeFromParsed(e,n,r,i);const s=r._custom;s&&n===this._cachedMeta.vScale&&(e.min=Math.min(e.min,s.min),e.max=Math.max(e.max,s.max))}getMaxOverflow(){return 0}getLabelAndValue(e){const n=this._cachedMeta,{iScale:r,vScale:i}=n,s=this.getParsed(e),a=s._custom,o=Qo(a)?"["+a.start+", "+a.end+"]":""+i.getLabelForValue(s[i.axis]);return{label:""+r.getLabelForValue(s[r.axis]),value:o}}initialize(){this.enableOptionSharing=!0,super.initialize();const e=this._cachedMeta;e.stack=this.getDataset().stack}update(e){const n=this._cachedMeta;this.updateElements(n.data,0,n.data.length,e)}updateElements(e,n,r,i){const s=i==="reset",{index:a,_cachedMeta:{vScale:o}}=this,l=o.getBasePixel(),u=o.isHorizontal(),d=this._getRuler(),{sharedOptions:h,includeOptions:f}=this._getSharedOptions(n,i);for(let m=n;m<n+r;m++){const x=this.getParsed(m),v=s||q(x[o.axis])?{base:l,head:l}:this._calculateBarValuePixels(m),w=this._calculateBarIndexPixels(m,d),g=(x._stacks||{})[o.axis],p={horizontal:u,base:v.base,enableBorderRadius:!g||Qo(x._custom)||a===g._top||a===g._bottom,x:u?v.head:w.center,y:u?w.center:v.head,height:u?w.size:Math.abs(v.size),width:u?Math.abs(v.size):w.size};f&&(p.options=h||this.resolveDataElementOptions(m,e[m].active?"active":i));const y=p.options||e[m].options;t2(p,y,g,a),r2(p,y,d.ratio),this.updateElement(e[m],m,p,i)}}_getStacks(e,n){const{iScale:r}=this._cachedMeta,i=r.getMatchingVisibleMetas(this._type).filter(d=>d.controller.options.grouped),s=r.options.stacked,a=[],o=this._cachedMeta.controller.getParsed(n),l=o&&o[r.axis],u=d=>{const h=d._parsed.find(m=>m[r.axis]===l),f=h&&h[d.vScale.axis];if(q(f)||isNaN(f))return!0};for(const d of i)if(!(n!==void 0&&u(d))&&((s===!1||a.indexOf(d.stack)===-1||s===void 0&&d.stack===void 0)&&a.push(d.stack),d.index===e))break;return a.length||a.push(void 0),a}_getStackCount(e){return this._getStacks(void 0,e).length}_getStackIndex(e,n,r){const i=this._getStacks(e,r),s=n!==void 0?i.indexOf(n):-1;return s===-1?i.length-1:s}_getRuler(){const e=this.options,n=this._cachedMeta,r=n.iScale,i=[];let s,a;for(s=0,a=n.data.length;s<a;++s)i.push(r.getPixelForValue(this.getParsed(s)[r.axis],s));const o=e.barThickness;return{min:o||Qw(n),pixels:i,start:r._startPixel,end:r._endPixel,stackCount:this._getStackCount(),scale:r,grouped:e.grouped,ratio:o?1:e.categoryPercentage*e.barPercentage}}_calculateBarValuePixels(e){const{_cachedMeta:{vScale:n,_stacked:r,index:i},options:{base:s,minBarLength:a}}=this,o=s||0,l=this.getParsed(e),u=l._custom,d=Qo(u);let h=l[n.axis],f=0,m=r?this.applyStack(n,l,r):h,x,v;m!==h&&(f=m-h,m=h),d&&(h=u.barStart,m=u.barEnd-u.barStart,h!==0&&bn(h)!==bn(u.barEnd)&&(f=0),f+=h);const w=!q(s)&&!d?s:f;let g=n.getPixelForValue(w);if(this.chart.getDataVisibility(e)?x=n.getPixelForValue(f+m):x=g,v=x-g,Math.abs(v)<a){v=Jw(v,n,o)*a,h===o&&(g-=v/2);const p=n.getPixelForDecimal(0),y=n.getPixelForDecimal(1),b=Math.min(p,y),S=Math.max(p,y);g=Math.max(Math.min(g,S),b),x=g+v,r&&!d&&(l._stacks[n.axis]._visualValues[i]=n.getValueForPixel(x)-n.getValueForPixel(g))}if(g===n.getPixelForValue(o)){const p=bn(v)*n.getLineWidthForValue(o)/2;g+=p,v-=p}return{size:v,base:g,head:x,center:x+v/2}}_calculateBarIndexPixels(e,n){const r=n.scale,i=this.options,s=i.skipNull,a=X(i.maxBarThickness,1/0);let o,l;if(n.grouped){const u=s?this._getStackCount(e):n.stackCount,d=i.barThickness==="flex"?qw(e,n,i,u):Gw(e,n,i,u),h=this._getStackIndex(this.index,this._cachedMeta.stack,s?e:void 0);o=d.start+d.chunk*h+d.chunk/2,l=Math.min(a,d.chunk*d.ratio)}else o=r.getPixelForValue(this.getParsed(e)[r.axis],e),l=Math.min(a,n.min*n.ratio);return{base:o-l/2,head:o+l/2,center:o,size:l}}draw(){const e=this._cachedMeta,n=e.vScale,r=e.data,i=r.length;let s=0;for(;s<i;++s)this.getParsed(s)[n.axis]!==null&&!r[s].hidden&&r[s].draw(this._ctx)}}F(Js,"id","bar"),F(Js,"defaults",{datasetElementType:!1,dataElementType:"bar",categoryPercentage:.8,barPercentage:.9,grouped:!0,animations:{numbers:{type:"number",properties:["x","y","base","width","height"]}}}),F(Js,"overrides",{scales:{_index_:{type:"category",offset:!0,grid:{offset:!0}},_value_:{type:"linear",beginAtZero:!0}}});function i2(t,e,n){let r=1,i=1,s=0,a=0;if(e<fe){const o=t,l=o+e,u=Math.cos(o),d=Math.sin(o),h=Math.cos(l),f=Math.sin(l),m=(y,b,S)=>Fa(y,o,l,!0)?1:Math.max(b,b*n,S,S*n),x=(y,b,S)=>Fa(y,o,l,!0)?-1:Math.min(b,b*n,S,S*n),v=m(0,u,h),w=m(ye,d,f),g=x(me,u,h),p=x(me+ye,d,f);r=(v-g)/2,i=(w-p)/2,s=-(v+g)/2,a=-(w+p)/2}return{ratioX:r,ratioY:i,offsetX:s,offsetY:a}}class fi extends Pr{constructor(e,n){super(e,n),this.enableOptionSharing=!0,this.innerRadius=void 0,this.outerRadius=void 0,this.offsetX=void 0,this.offsetY=void 0}linkScales(){}parse(e,n){const r=this.getDataset().data,i=this._cachedMeta;if(this._parsing===!1)i._parsed=r;else{let s=l=>+r[l];if(V(r[e])){const{key:l="value"}=this._parsing;s=u=>+Gn(r[u],l)}let a,o;for(a=e,o=e+n;a<o;++a)i._parsed[a]=s(a)}}_getRotation(){return Bt(this.options.rotation-90)}_getCircumference(){return Bt(this.options.circumference)}_getRotationExtents(){let e=fe,n=-fe;for(let r=0;r<this.chart.data.datasets.length;++r)if(this.chart.isDatasetVisible(r)&&this.chart.getDatasetMeta(r).type===this._type){const i=this.chart.getDatasetMeta(r).controller,s=i._getRotation(),a=i._getCircumference();e=Math.min(e,s),n=Math.max(n,s+a)}return{rotation:e,circumference:n-e}}update(e){const n=this.chart,{chartArea:r}=n,i=this._cachedMeta,s=i.data,a=this.getMaxBorderWidth()+this.getMaxOffset(s)+this.options.spacing,o=Math.max((Math.min(r.width,r.height)-a)/2,0),l=Math.min(M1(this.options.cutout,o),1),u=this._getRingWeight(this.index),{circumference:d,rotation:h}=this._getRotationExtents(),{ratioX:f,ratioY:m,offsetX:x,offsetY:v}=i2(h,d,l),w=(r.width-a)/f,g=(r.height-a)/m,p=Math.max(Math.min(w,g)/2,0),y=tg(this.options.radius,p),b=Math.max(y*l,0),S=(y-b)/this._getVisibleDatasetWeightTotal();this.offsetX=x*y,this.offsetY=v*y,i.total=this.calculateTotal(),this.outerRadius=y-S*this._getRingWeightOffset(this.index),this.innerRadius=Math.max(this.outerRadius-S*u,0),this.updateElements(s,0,s.length,e)}_circumference(e,n){const r=this.options,i=this._cachedMeta,s=this._getCircumference();return n&&r.animation.animateRotate||!this.chart.getDataVisibility(e)||i._parsed[e]===null||i.data[e].hidden?0:this.calculateCircumference(i._parsed[e]*s/fe)}updateElements(e,n,r,i){const s=i==="reset",a=this.chart,o=a.chartArea,u=a.options.animation,d=(o.left+o.right)/2,h=(o.top+o.bottom)/2,f=s&&u.animateScale,m=f?0:this.innerRadius,x=f?0:this.outerRadius,{sharedOptions:v,includeOptions:w}=this._getSharedOptions(n,i);let g=this._getRotation(),p;for(p=0;p<n;++p)g+=this._circumference(p,s);for(p=n;p<n+r;++p){const y=this._circumference(p,s),b=e[p],S={x:d+this.offsetX,y:h+this.offsetY,startAngle:g,endAngle:g+y,circumference:y,outerRadius:x,innerRadius:m};w&&(S.options=v||this.resolveDataElementOptions(p,b.active?"active":i)),g+=y,this.updateElement(b,p,S,i)}}calculateTotal(){const e=this._cachedMeta,n=e.data;let r=0,i;for(i=0;i<n.length;i++){const s=e._parsed[i];s!==null&&!isNaN(s)&&this.chart.getDataVisibility(i)&&!n[i].hidden&&(r+=Math.abs(s))}return r}calculateCircumference(e){const n=this._cachedMeta.total;return n>0&&!isNaN(e)?fe*(Math.abs(e)/n):0}getLabelAndValue(e){const n=this._cachedMeta,r=this.chart,i=r.data.labels||[],s=hu(n._parsed[e],r.options.locale);return{label:i[e]||"",value:s}}getMaxBorderWidth(e){let n=0;const r=this.chart;let i,s,a,o,l;if(!e){for(i=0,s=r.data.datasets.length;i<s;++i)if(r.isDatasetVisible(i)){a=r.getDatasetMeta(i),e=a.data,o=a.controller;break}}if(!e)return 0;for(i=0,s=e.length;i<s;++i)l=o.resolveDataElementOptions(i),l.borderAlign!=="inner"&&(n=Math.max(n,l.borderWidth||0,l.hoverBorderWidth||0));return n}getMaxOffset(e){let n=0;for(let r=0,i=e.length;r<i;++r){const s=this.resolveDataElementOptions(r);n=Math.max(n,s.offset||0,s.hoverOffset||0)}return n}_getRingWeightOffset(e){let n=0;for(let r=0;r<e;++r)this.chart.isDatasetVisible(r)&&(n+=this._getRingWeight(r));return n}_getRingWeight(e){return Math.max(X(this.chart.data.datasets[e].weight,1),0)}_getVisibleDatasetWeightTotal(){return this._getRingWeightOffset(this.chart.data.datasets.length)||1}}F(fi,"id","doughnut"),F(fi,"defaults",{datasetElementType:!1,dataElementType:"arc",animation:{animateRotate:!0,animateScale:!1},animations:{numbers:{type:"number",properties:["circumference","endAngle","innerRadius","outerRadius","startAngle","x","y","offset","borderWidth","spacing"]}},cutout:"50%",rotation:0,circumference:360,radius:"100%",spacing:0,indexAxis:"r"}),F(fi,"descriptors",{_scriptable:e=>e!=="spacing",_indexable:e=>e!=="spacing"&&!e.startsWith("borderDash")&&!e.startsWith("hoverBorderDash")}),F(fi,"overrides",{aspectRatio:1,plugins:{legend:{labels:{generateLabels(e){const n=e.data;if(n.labels.length&&n.datasets.length){const{labels:{pointStyle:r,color:i}}=e.legend.options;return n.labels.map((s,a)=>{const l=e.getDatasetMeta(0).controller.getStyle(a);return{text:s,fillStyle:l.backgroundColor,strokeStyle:l.borderColor,fontColor:i,lineWidth:l.borderWidth,pointStyle:r,hidden:!e.getDataVisibility(a),index:a}})}return[]}},onClick(e,n,r){r.chart.toggleDataVisibility(n.index),r.chart.update()}}}});function Mn(){throw new Error("This method is not implemented: Check that a complete date adapter is provided.")}class wu{constructor(e){F(this,"options");this.options=e||{}}static override(e){Object.assign(wu.prototype,e)}init(){}formats(){return Mn()}parse(){return Mn()}format(){return Mn()}add(){return Mn()}diff(){return Mn()}startOf(){return Mn()}endOf(){return Mn()}}var s2={_date:wu};function a2(t,e,n,r){const{controller:i,data:s,_sorted:a}=t,o=i._cachedMeta.iScale,l=t.dataset&&t.dataset.options?t.dataset.options.spanGaps:null;if(o&&e===o.axis&&e!=="r"&&a&&s.length){const u=o._reversePixels?V1:nc;if(r){if(i._sharedOptions){const d=s[0],h=typeof d.getRange=="function"&&d.getRange(e);if(h){const f=u(s,e,n-h),m=u(s,e,n+h);return{lo:f.lo,hi:m.hi}}}}else{const d=u(s,e,n);if(l){const{vScale:h}=i._cachedMeta,{_parsed:f}=t,m=f.slice(0,d.lo+1).reverse().findIndex(v=>!q(v[h.axis]));d.lo-=Math.max(0,m);const x=f.slice(d.hi).findIndex(v=>!q(v[h.axis]));d.hi+=Math.max(0,x)}return d}}return{lo:0,hi:s.length-1}}function ho(t,e,n,r,i){const s=t.getSortedVisibleDatasetMetas(),a=n[e];for(let o=0,l=s.length;o<l;++o){const{index:u,data:d}=s[o],{lo:h,hi:f}=a2(s[o],e,a,i);for(let m=h;m<=f;++m){const x=d[m];x.skip||r(x,u,m)}}}function o2(t){const e=t.indexOf("x")!==-1,n=t.indexOf("y")!==-1;return function(r,i){const s=e?Math.abs(r.x-i.x):0,a=n?Math.abs(r.y-i.y):0;return Math.sqrt(Math.pow(s,2)+Math.pow(a,2))}}function Go(t,e,n,r,i){const s=[];return!i&&!t.isPointInArea(e)||ho(t,n,e,function(o,l,u){!i&&!hg(o,t.chartArea,0)||o.inRange(e.x,e.y,r)&&s.push({element:o,datasetIndex:l,index:u})},!0),s}function l2(t,e,n,r){let i=[];function s(a,o,l){const{startAngle:u,endAngle:d}=a.getProps(["startAngle","endAngle"],r),{angle:h}=ig(a,{x:e.x,y:e.y});Fa(h,u,d)&&i.push({element:a,datasetIndex:o,index:l})}return ho(t,n,e,s),i}function c2(t,e,n,r,i,s){let a=[];const o=o2(n);let l=Number.POSITIVE_INFINITY;function u(d,h,f){const m=d.inRange(e.x,e.y,i);if(r&&!m)return;const x=d.getCenterPoint(i);if(!(!!s||t.isPointInArea(x))&&!m)return;const w=o(e,x);w<l?(a=[{element:d,datasetIndex:h,index:f}],l=w):w===l&&a.push({element:d,datasetIndex:h,index:f})}return ho(t,n,e,u),a}function qo(t,e,n,r,i,s){return!s&&!t.isPointInArea(e)?[]:n==="r"&&!r?l2(t,e,n,i):c2(t,e,n,r,i,s)}function Eh(t,e,n,r,i){const s=[],a=n==="x"?"inXRange":"inYRange";let o=!1;return ho(t,n,e,(l,u,d)=>{l[a]&&l[a](e[n],i)&&(s.push({element:l,datasetIndex:u,index:d}),o=o||l.inRange(e.x,e.y,i))}),r&&!o?[]:s}var u2={modes:{index(t,e,n,r){const i=Ln(e,t),s=n.axis||"x",a=n.includeInvisible||!1,o=n.intersect?Go(t,i,s,r,a):qo(t,i,s,!1,r,a),l=[];return o.length?(t.getSortedVisibleDatasetMetas().forEach(u=>{const d=o[0].index,h=u.data[d];h&&!h.skip&&l.push({element:h,datasetIndex:u.index,index:d})}),l):[]},dataset(t,e,n,r){const i=Ln(e,t),s=n.axis||"xy",a=n.includeInvisible||!1;let o=n.intersect?Go(t,i,s,r,a):qo(t,i,s,!1,r,a);if(o.length>0){const l=o[0].datasetIndex,u=t.getDatasetMeta(l).data;o=[];for(let d=0;d<u.length;++d)o.push({element:u[d],datasetIndex:l,index:d})}return o},point(t,e,n,r){const i=Ln(e,t),s=n.axis||"xy",a=n.includeInvisible||!1;return Go(t,i,s,r,a)},nearest(t,e,n,r){const i=Ln(e,t),s=n.axis||"xy",a=n.includeInvisible||!1;return qo(t,i,s,n.intersect,r,a)},x(t,e,n,r){const i=Ln(e,t);return Eh(t,i,"x",n.intersect,r)},y(t,e,n,r){const i=Ln(e,t);return Eh(t,i,"y",n.intersect,r)}}};const Ng=["left","top","right","bottom"];function ii(t,e){return t.filter(n=>n.pos===e)}function Th(t,e){return t.filter(n=>Ng.indexOf(n.pos)===-1&&n.box.axis===e)}function si(t,e){return t.sort((n,r)=>{const i=e?r:n,s=e?n:r;return i.weight===s.weight?i.index-s.index:i.weight-s.weight})}function d2(t){const e=[];let n,r,i,s,a,o;for(n=0,r=(t||[]).length;n<r;++n)i=t[n],{position:s,options:{stack:a,stackWeight:o=1}}=i,e.push({index:n,box:i,pos:s,horizontal:i.isHorizontal(),weight:i.weight,stack:a&&s+a,stackWeight:o});return e}function h2(t){const e={};for(const n of t){const{stack:r,pos:i,stackWeight:s}=n;if(!r||!Ng.includes(i))continue;const a=e[r]||(e[r]={count:0,placed:0,weight:0,size:0});a.count++,a.weight+=s}return e}function f2(t,e){const n=h2(t),{vBoxMaxWidth:r,hBoxMaxHeight:i}=e;let s,a,o;for(s=0,a=t.length;s<a;++s){o=t[s];const{fullSize:l}=o.box,u=n[o.stack],d=u&&o.stackWeight/u.weight;o.horizontal?(o.width=d?d*r:l&&e.availableWidth,o.height=i):(o.width=r,o.height=d?d*i:l&&e.availableHeight)}return n}function m2(t){const e=d2(t),n=si(e.filter(u=>u.box.fullSize),!0),r=si(ii(e,"left"),!0),i=si(ii(e,"right")),s=si(ii(e,"top"),!0),a=si(ii(e,"bottom")),o=Th(e,"x"),l=Th(e,"y");return{fullSize:n,leftAndTop:r.concat(s),rightAndBottom:i.concat(l).concat(a).concat(o),chartArea:ii(e,"chartArea"),vertical:r.concat(i).concat(l),horizontal:s.concat(a).concat(o)}}function Mh(t,e,n,r){return Math.max(t[n],e[n])+Math.max(t[r],e[r])}function _g(t,e){t.top=Math.max(t.top,e.top),t.left=Math.max(t.left,e.left),t.bottom=Math.max(t.bottom,e.bottom),t.right=Math.max(t.right,e.right)}function p2(t,e,n,r){const{pos:i,box:s}=n,a=t.maxPadding;if(!V(i)){n.size&&(t[i]-=n.size);const h=r[n.stack]||{size:0,count:1};h.size=Math.max(h.size,n.horizontal?s.height:s.width),n.size=h.size/h.count,t[i]+=n.size}s.getPadding&&_g(a,s.getPadding());const o=Math.max(0,e.outerWidth-Mh(a,t,"left","right")),l=Math.max(0,e.outerHeight-Mh(a,t,"top","bottom")),u=o!==t.w,d=l!==t.h;return t.w=o,t.h=l,n.horizontal?{same:u,other:d}:{same:d,other:u}}function g2(t){const e=t.maxPadding;function n(r){const i=Math.max(e[r]-t[r],0);return t[r]+=i,i}t.y+=n("top"),t.x+=n("left"),n("right"),n("bottom")}function x2(t,e){const n=e.maxPadding;function r(i){const s={left:0,top:0,right:0,bottom:0};return i.forEach(a=>{s[a]=Math.max(e[a],n[a])}),s}return r(t?["left","right"]:["top","bottom"])}function mi(t,e,n,r){const i=[];let s,a,o,l,u,d;for(s=0,a=t.length,u=0;s<a;++s){o=t[s],l=o.box,l.update(o.width||e.w,o.height||e.h,x2(o.horizontal,e));const{same:h,other:f}=p2(e,n,o,r);u|=h&&i.length,d=d||f,l.fullSize||i.push(o)}return u&&mi(i,e,n,r)||d}function Ds(t,e,n,r,i){t.top=n,t.left=e,t.right=e+r,t.bottom=n+i,t.width=r,t.height=i}function $h(t,e,n,r){const i=n.padding;let{x:s,y:a}=e;for(const o of t){const l=o.box,u=r[o.stack]||{placed:0,weight:1},d=o.stackWeight/u.weight||1;if(o.horizontal){const h=e.w*d,f=u.size||l.height;qi(u.start)&&(a=u.start),l.fullSize?Ds(l,i.left,a,n.outerWidth-i.right-i.left,f):Ds(l,e.left+u.placed,a,h,f),u.start=a,u.placed+=h,a=l.bottom}else{const h=e.h*d,f=u.size||l.width;qi(u.start)&&(s=u.start),l.fullSize?Ds(l,s,i.top,f,n.outerHeight-i.bottom-i.top):Ds(l,s,e.top+u.placed,f,h),u.start=s,u.placed+=h,s=l.right}}e.x=s,e.y=a}var st={addBox(t,e){t.boxes||(t.boxes=[]),e.fullSize=e.fullSize||!1,e.position=e.position||"top",e.weight=e.weight||0,e._layers=e._layers||function(){return[{z:0,draw(n){e.draw(n)}}]},t.boxes.push(e)},removeBox(t,e){const n=t.boxes?t.boxes.indexOf(e):-1;n!==-1&&t.boxes.splice(n,1)},configure(t,e,n){e.fullSize=n.fullSize,e.position=n.position,e.weight=n.weight},update(t,e,n,r){if(!t)return;const i=ut(t.options.layout.padding),s=Math.max(e-i.width,0),a=Math.max(n-i.height,0),o=m2(t.boxes),l=o.vertical,u=o.horizontal;Q(t.boxes,v=>{typeof v.beforeLayout=="function"&&v.beforeLayout()});const d=l.reduce((v,w)=>w.box.options&&w.box.options.display===!1?v:v+1,0)||1,h=Object.freeze({outerWidth:e,outerHeight:n,padding:i,availableWidth:s,availableHeight:a,vBoxMaxWidth:s/2/d,hBoxMaxHeight:a/2}),f=Object.assign({},i);_g(f,ut(r));const m=Object.assign({maxPadding:f,w:s,h:a,x:i.left,y:i.top},i),x=f2(l.concat(u),h);mi(o.fullSize,m,h,x),mi(l,m,h,x),mi(u,m,h,x)&&mi(l,m,h,x),g2(m),$h(o.leftAndTop,m,h,x),m.x+=m.w,m.y+=m.h,$h(o.rightAndBottom,m,h,x),t.chartArea={left:m.left,top:m.top,right:m.left+m.w,bottom:m.top+m.h,height:m.h,width:m.w},Q(o.chartArea,v=>{const w=v.box;Object.assign(w,t.chartArea),w.update(m.w,m.h,{left:0,top:0,right:0,bottom:0})})}};class kg{acquireContext(e,n){}releaseContext(e){return!1}addEventListener(e,n,r){}removeEventListener(e,n,r){}getDevicePixelRatio(){return 1}getMaximumSize(e,n,r,i){return n=Math.max(0,n||e.width),r=r||e.height,{width:n,height:Math.max(0,i?Math.floor(n/i):r)}}isAttached(e){return!0}updateConfig(e){}}class v2 extends kg{acquireContext(e){return e&&e.getContext&&e.getContext("2d")||null}updateConfig(e){e.options.animation=!1}}const ea="$chartjs",y2={touchstart:"mousedown",touchmove:"mousemove",touchend:"mouseup",pointerenter:"mouseenter",pointerdown:"mousedown",pointermove:"mousemove",pointerup:"mouseup",pointerleave:"mouseout",pointerout:"mouseout"},Rh=t=>t===null||t==="";function b2(t,e){const n=t.style,r=t.getAttribute("height"),i=t.getAttribute("width");if(t[ea]={initial:{height:r,width:i,style:{display:n.display,height:n.height,width:n.width}}},n.display=n.display||"block",n.boxSizing=n.boxSizing||"border-box",Rh(i)){const s=yh(t,"width");s!==void 0&&(t.width=s)}if(Rh(r))if(t.style.height==="")t.height=t.width/(e||2);else{const s=yh(t,"height");s!==void 0&&(t.height=s)}return t}const jg=Ew?{passive:!0}:!1;function w2(t,e,n){t&&t.addEventListener(e,n,jg)}function S2(t,e,n){t&&t.canvas&&t.canvas.removeEventListener(e,n,jg)}function N2(t,e){const n=y2[t.type]||t.type,{x:r,y:i}=Ln(t,e);return{type:n,chart:e,native:t,x:r!==void 0?r:null,y:i!==void 0?i:null}}function Ua(t,e){for(const n of t)if(n===e||n.contains(e))return!0}function _2(t,e,n){const r=t.canvas,i=new MutationObserver(s=>{let a=!1;for(const o of s)a=a||Ua(o.addedNodes,r),a=a&&!Ua(o.removedNodes,r);a&&n()});return i.observe(document,{childList:!0,subtree:!0}),i}function k2(t,e,n){const r=t.canvas,i=new MutationObserver(s=>{let a=!1;for(const o of s)a=a||Ua(o.removedNodes,r),a=a&&!Ua(o.addedNodes,r);a&&n()});return i.observe(document,{childList:!0,subtree:!0}),i}const Ji=new Map;let Dh=0;function Cg(){const t=window.devicePixelRatio;t!==Dh&&(Dh=t,Ji.forEach((e,n)=>{n.currentDevicePixelRatio!==t&&e()}))}function j2(t,e){Ji.size||window.addEventListener("resize",Cg),Ji.set(t,e)}function C2(t){Ji.delete(t),Ji.size||window.removeEventListener("resize",Cg)}function P2(t,e,n){const r=t.canvas,i=r&&bu(r);if(!i)return;const s=lg((o,l)=>{const u=i.clientWidth;n(o,l),u<i.clientWidth&&n()},window),a=new ResizeObserver(o=>{const l=o[0],u=l.contentRect.width,d=l.contentRect.height;u===0&&d===0||s(u,d)});return a.observe(i),j2(t,s),a}function Zo(t,e,n){n&&n.disconnect(),e==="resize"&&C2(t)}function E2(t,e,n){const r=t.canvas,i=lg(s=>{t.ctx!==null&&n(N2(s,t))},t);return w2(r,e,i),i}class T2 extends kg{acquireContext(e,n){const r=e&&e.getContext&&e.getContext("2d");return r&&r.canvas===e?(b2(e,n),r):null}releaseContext(e){const n=e.canvas;if(!n[ea])return!1;const r=n[ea].initial;["height","width"].forEach(s=>{const a=r[s];q(a)?n.removeAttribute(s):n.setAttribute(s,a)});const i=r.style||{};return Object.keys(i).forEach(s=>{n.style[s]=i[s]}),n.width=n.width,delete n[ea],!0}addEventListener(e,n,r){this.removeEventListener(e,n);const i=e.$proxies||(e.$proxies={}),a={attach:_2,detach:k2,resize:P2}[n]||E2;i[n]=a(e,n,r)}removeEventListener(e,n){const r=e.$proxies||(e.$proxies={}),i=r[n];if(!i)return;({attach:Zo,detach:Zo,resize:Zo}[n]||S2)(e,n,i),r[n]=void 0}getDevicePixelRatio(){return window.devicePixelRatio}getMaximumSize(e,n,r,i){return Pw(e,n,r,i)}isAttached(e){const n=e&&bu(e);return!!(n&&n.isConnected)}}function M2(t){return!yu()||typeof OffscreenCanvas<"u"&&t instanceof OffscreenCanvas?v2:T2}class Kt{constructor(){F(this,"x");F(this,"y");F(this,"active",!1);F(this,"options");F(this,"$animations")}tooltipPosition(e){const{x:n,y:r}=this.getProps(["x","y"],e);return{x:n,y:r}}hasValue(){return Aa(this.x)&&Aa(this.y)}getProps(e,n){const r=this.$animations;if(!n||!r)return this;const i={};return e.forEach(s=>{i[s]=r[s]&&r[s].active()?r[s]._to:this[s]}),i}}F(Kt,"defaults",{}),F(Kt,"defaultRoutes");function $2(t,e){const n=t.options.ticks,r=R2(t),i=Math.min(n.maxTicksLimit||r,r),s=n.major.enabled?O2(e):[],a=s.length,o=s[0],l=s[a-1],u=[];if(a>i)return L2(e,u,s,a/i),u;const d=D2(s,e,i);if(a>0){let h,f;const m=a>1?Math.round((l-o)/(a-1)):null;for(Os(e,u,d,q(m)?0:o-m,o),h=0,f=a-1;h<f;h++)Os(e,u,d,s[h],s[h+1]);return Os(e,u,d,l,q(m)?e.length:l+m),u}return Os(e,u,d),u}function R2(t){const e=t.options.offset,n=t._tickSize(),r=t._length/n+(e?0:1),i=t._maxLength/n;return Math.floor(Math.min(r,i))}function D2(t,e,n){const r=A2(t),i=e.length/n;if(!r)return Math.max(i,1);const s=F1(r);for(let a=0,o=s.length-1;a<o;a++){const l=s[a];if(l>i)return l}return Math.max(i,1)}function O2(t){const e=[];let n,r;for(n=0,r=t.length;n<r;n++)t[n].major&&e.push(n);return e}function L2(t,e,n,r){let i=0,s=n[0],a;for(r=Math.ceil(r),a=0;a<t.length;a++)a===s&&(e.push(t[a]),i++,s=n[i*r])}function Os(t,e,n,r,i){const s=X(r,0),a=Math.min(X(i,t.length),t.length);let o=0,l,u,d;for(n=Math.ceil(n),i&&(l=i-r,n=l/Math.floor(l/n)),d=s;d<0;)o++,d=Math.round(s+o*n);for(u=Math.max(s,0);u<a;u++)u===d&&(e.push(t[u]),o++,d=Math.round(s+o*n))}function A2(t){const e=t.length;let n,r;if(e<2)return!1;for(r=t[0],n=1;n<e;++n)if(t[n]-t[n-1]!==r)return!1;return r}const F2=t=>t==="left"?"right":t==="right"?"left":t,Oh=(t,e,n)=>e==="top"||e==="left"?t[e]+n:t[e]-n,Lh=(t,e)=>Math.min(e||t,t);function Ah(t,e){const n=[],r=t.length/e,i=t.length;let s=0;for(;s<i;s+=r)n.push(t[Math.floor(s)]);return n}function z2(t,e,n){const r=t.ticks.length,i=Math.min(e,r-1),s=t._startPixel,a=t._endPixel,o=1e-6;let l=t.getPixelForTick(i),u;if(!(n&&(r===1?u=Math.max(l-s,a-l):e===0?u=(t.getPixelForTick(1)-l)/2:u=(l-t.getPixelForTick(i-1))/2,l+=i<e?u:-u,l<s-o||l>a+o)))return l}function I2(t,e){Q(t,n=>{const r=n.gc,i=r.length/2;let s;if(i>e){for(s=0;s<i;++s)delete n.data[r[s]];r.splice(0,i)}})}function ai(t){return t.drawTicks?t.tickLength:0}function Fh(t,e){if(!t.display)return 0;const n=Me(t.font,e),r=ut(t.padding);return(xe(t.text)?t.text.length:1)*n.lineHeight+r.height}function U2(t,e){return Vr(t,{scale:e,type:"scale"})}function B2(t,e,n){return Vr(t,{tick:n,index:e,type:"tick"})}function H2(t,e,n){let r=du(t);return(n&&e!=="right"||!n&&e==="right")&&(r=F2(r)),r}function W2(t,e,n,r){const{top:i,left:s,bottom:a,right:o,chart:l}=t,{chartArea:u,scales:d}=l;let h=0,f,m,x;const v=a-i,w=o-s;if(t.isHorizontal()){if(m=Pe(r,s,o),V(n)){const g=Object.keys(n)[0],p=n[g];x=d[g].getPixelForValue(p)+v-e}else n==="center"?x=(u.bottom+u.top)/2+v-e:x=Oh(t,n,e);f=o-s}else{if(V(n)){const g=Object.keys(n)[0],p=n[g];m=d[g].getPixelForValue(p)-w+e}else n==="center"?m=(u.left+u.right)/2-w+e:m=Oh(t,n,e);x=Pe(r,a,i),h=n==="left"?-ye:ye}return{titleX:m,titleY:x,maxWidth:f,rotation:h}}class Yr extends Kt{constructor(e){super(),this.id=e.id,this.type=e.type,this.options=void 0,this.ctx=e.ctx,this.chart=e.chart,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this._margins={left:0,right:0,top:0,bottom:0},this.maxWidth=void 0,this.maxHeight=void 0,this.paddingTop=void 0,this.paddingBottom=void 0,this.paddingLeft=void 0,this.paddingRight=void 0,this.axis=void 0,this.labelRotation=void 0,this.min=void 0,this.max=void 0,this._range=void 0,this.ticks=[],this._gridLineItems=null,this._labelItems=null,this._labelSizes=null,this._length=0,this._maxLength=0,this._longestTextCache={},this._startPixel=void 0,this._endPixel=void 0,this._reversePixels=!1,this._userMax=void 0,this._userMin=void 0,this._suggestedMax=void 0,this._suggestedMin=void 0,this._ticksLength=0,this._borderValue=0,this._cache={},this._dataLimitsCached=!1,this.$context=void 0}init(e){this.options=e.setContext(this.getContext()),this.axis=e.axis,this._userMin=this.parse(e.min),this._userMax=this.parse(e.max),this._suggestedMin=this.parse(e.suggestedMin),this._suggestedMax=this.parse(e.suggestedMax)}parse(e,n){return e}getUserBounds(){let{_userMin:e,_userMax:n,_suggestedMin:r,_suggestedMax:i}=this;return e=kt(e,Number.POSITIVE_INFINITY),n=kt(n,Number.NEGATIVE_INFINITY),r=kt(r,Number.POSITIVE_INFINITY),i=kt(i,Number.NEGATIVE_INFINITY),{min:kt(e,r),max:kt(n,i),minDefined:ct(e),maxDefined:ct(n)}}getMinMax(e){let{min:n,max:r,minDefined:i,maxDefined:s}=this.getUserBounds(),a;if(i&&s)return{min:n,max:r};const o=this.getMatchingVisibleMetas();for(let l=0,u=o.length;l<u;++l)a=o[l].controller.getMinMax(this,e),i||(n=Math.min(n,a.min)),s||(r=Math.max(r,a.max));return n=s&&n>r?r:n,r=i&&n>r?n:r,{min:kt(n,kt(r,n)),max:kt(r,kt(n,r))}}getPadding(){return{left:this.paddingLeft||0,top:this.paddingTop||0,right:this.paddingRight||0,bottom:this.paddingBottom||0}}getTicks(){return this.ticks}getLabels(){const e=this.chart.data;return this.options.labels||(this.isHorizontal()?e.xLabels:e.yLabels)||e.labels||[]}getLabelItems(e=this.chart.chartArea){return this._labelItems||(this._labelItems=this._computeLabelItems(e))}beforeLayout(){this._cache={},this._dataLimitsCached=!1}beforeUpdate(){ee(this.options.beforeUpdate,[this])}update(e,n,r){const{beginAtZero:i,grace:s,ticks:a}=this.options,o=a.sampleSize;this.beforeUpdate(),this.maxWidth=e,this.maxHeight=n,this._margins=r=Object.assign({left:0,right:0,top:0,bottom:0},r),this.ticks=null,this._labelSizes=null,this._gridLineItems=null,this._labelItems=null,this.beforeSetDimensions(),this.setDimensions(),this.afterSetDimensions(),this._maxLength=this.isHorizontal()?this.width+r.left+r.right:this.height+r.top+r.bottom,this._dataLimitsCached||(this.beforeDataLimits(),this.determineDataLimits(),this.afterDataLimits(),this._range=fw(this,s,i),this._dataLimitsCached=!0),this.beforeBuildTicks(),this.ticks=this.buildTicks()||[],this.afterBuildTicks();const l=o<this.ticks.length;this._convertTicksToLabels(l?Ah(this.ticks,o):this.ticks),this.configure(),this.beforeCalculateLabelRotation(),this.calculateLabelRotation(),this.afterCalculateLabelRotation(),a.display&&(a.autoSkip||a.source==="auto")&&(this.ticks=$2(this,this.ticks),this._labelSizes=null,this.afterAutoSkip()),l&&this._convertTicksToLabels(this.ticks),this.beforeFit(),this.fit(),this.afterFit(),this.afterUpdate()}configure(){let e=this.options.reverse,n,r;this.isHorizontal()?(n=this.left,r=this.right):(n=this.top,r=this.bottom,e=!e),this._startPixel=n,this._endPixel=r,this._reversePixels=e,this._length=r-n,this._alignToPixels=this.options.alignToPixels}afterUpdate(){ee(this.options.afterUpdate,[this])}beforeSetDimensions(){ee(this.options.beforeSetDimensions,[this])}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=0,this.right=this.width):(this.height=this.maxHeight,this.top=0,this.bottom=this.height),this.paddingLeft=0,this.paddingTop=0,this.paddingRight=0,this.paddingBottom=0}afterSetDimensions(){ee(this.options.afterSetDimensions,[this])}_callHooks(e){this.chart.notifyPlugins(e,this.getContext()),ee(this.options[e],[this])}beforeDataLimits(){this._callHooks("beforeDataLimits")}determineDataLimits(){}afterDataLimits(){this._callHooks("afterDataLimits")}beforeBuildTicks(){this._callHooks("beforeBuildTicks")}buildTicks(){return[]}afterBuildTicks(){this._callHooks("afterBuildTicks")}beforeTickToLabelConversion(){ee(this.options.beforeTickToLabelConversion,[this])}generateTickLabels(e){const n=this.options.ticks;let r,i,s;for(r=0,i=e.length;r<i;r++)s=e[r],s.label=ee(n.callback,[s.value,r,e],this)}afterTickToLabelConversion(){ee(this.options.afterTickToLabelConversion,[this])}beforeCalculateLabelRotation(){ee(this.options.beforeCalculateLabelRotation,[this])}calculateLabelRotation(){const e=this.options,n=e.ticks,r=Lh(this.ticks.length,e.ticks.maxTicksLimit),i=n.minRotation||0,s=n.maxRotation;let a=i,o,l,u;if(!this._isVisible()||!n.display||i>=s||r<=1||!this.isHorizontal()){this.labelRotation=i;return}const d=this._getLabelSizes(),h=d.widest.width,f=d.highest.height,m=We(this.chart.width-h,0,this.maxWidth);o=e.offset?this.maxWidth/r:m/(r-1),h+6>o&&(o=m/(r-(e.offset?.5:1)),l=this.maxHeight-ai(e.grid)-n.padding-Fh(e.title,this.chart.options.font),u=Math.sqrt(h*h+f*f),a=B1(Math.min(Math.asin(We((d.highest.height+6)/o,-1,1)),Math.asin(We(l/u,-1,1))-Math.asin(We(f/u,-1,1)))),a=Math.max(i,Math.min(s,a))),this.labelRotation=a}afterCalculateLabelRotation(){ee(this.options.afterCalculateLabelRotation,[this])}afterAutoSkip(){}beforeFit(){ee(this.options.beforeFit,[this])}fit(){const e={width:0,height:0},{chart:n,options:{ticks:r,title:i,grid:s}}=this,a=this._isVisible(),o=this.isHorizontal();if(a){const l=Fh(i,n.options.font);if(o?(e.width=this.maxWidth,e.height=ai(s)+l):(e.height=this.maxHeight,e.width=ai(s)+l),r.display&&this.ticks.length){const{first:u,last:d,widest:h,highest:f}=this._getLabelSizes(),m=r.padding*2,x=Bt(this.labelRotation),v=Math.cos(x),w=Math.sin(x);if(o){const g=r.mirror?0:w*h.width+v*f.height;e.height=Math.min(this.maxHeight,e.height+g+m)}else{const g=r.mirror?0:v*h.width+w*f.height;e.width=Math.min(this.maxWidth,e.width+g+m)}this._calculatePadding(u,d,w,v)}}this._handleMargins(),o?(this.width=this._length=n.width-this._margins.left-this._margins.right,this.height=e.height):(this.width=e.width,this.height=this._length=n.height-this._margins.top-this._margins.bottom)}_calculatePadding(e,n,r,i){const{ticks:{align:s,padding:a},position:o}=this.options,l=this.labelRotation!==0,u=o!=="top"&&this.axis==="x";if(this.isHorizontal()){const d=this.getPixelForTick(0)-this.left,h=this.right-this.getPixelForTick(this.ticks.length-1);let f=0,m=0;l?u?(f=i*e.width,m=r*n.height):(f=r*e.height,m=i*n.width):s==="start"?m=n.width:s==="end"?f=e.width:s!=="inner"&&(f=e.width/2,m=n.width/2),this.paddingLeft=Math.max((f-d+a)*this.width/(this.width-d),0),this.paddingRight=Math.max((m-h+a)*this.width/(this.width-h),0)}else{let d=n.height/2,h=e.height/2;s==="start"?(d=0,h=e.height):s==="end"&&(d=n.height,h=0),this.paddingTop=d+a,this.paddingBottom=h+a}}_handleMargins(){this._margins&&(this._margins.left=Math.max(this.paddingLeft,this._margins.left),this._margins.top=Math.max(this.paddingTop,this._margins.top),this._margins.right=Math.max(this.paddingRight,this._margins.right),this._margins.bottom=Math.max(this.paddingBottom,this._margins.bottom))}afterFit(){ee(this.options.afterFit,[this])}isHorizontal(){const{axis:e,position:n}=this.options;return n==="top"||n==="bottom"||e==="x"}isFullSize(){return this.options.fullSize}_convertTicksToLabels(e){this.beforeTickToLabelConversion(),this.generateTickLabels(e);let n,r;for(n=0,r=e.length;n<r;n++)q(e[n].label)&&(e.splice(n,1),r--,n--);this.afterTickToLabelConversion()}_getLabelSizes(){let e=this._labelSizes;if(!e){const n=this.options.ticks.sampleSize;let r=this.ticks;n<r.length&&(r=Ah(r,n)),this._labelSizes=e=this._computeLabelSizes(r,r.length,this.options.ticks.maxTicksLimit)}return e}_computeLabelSizes(e,n,r){const{ctx:i,_longestTextCache:s}=this,a=[],o=[],l=Math.floor(n/Lh(n,r));let u=0,d=0,h,f,m,x,v,w,g,p,y,b,S;for(h=0;h<n;h+=l){if(x=e[h].label,v=this._resolveTickFontOptions(h),i.font=w=v.string,g=s[w]=s[w]||{data:{},gc:[]},p=v.lineHeight,y=b=0,!q(x)&&!xe(x))y=fh(i,g.data,g.gc,y,x),b=p;else if(xe(x))for(f=0,m=x.length;f<m;++f)S=x[f],!q(S)&&!xe(S)&&(y=fh(i,g.data,g.gc,y,S),b+=p);a.push(y),o.push(b),u=Math.max(y,u),d=Math.max(b,d)}I2(s,n);const j=a.indexOf(u),N=o.indexOf(d),k=C=>({width:a[C]||0,height:o[C]||0});return{first:k(0),last:k(n-1),widest:k(j),highest:k(N),widths:a,heights:o}}getLabelForValue(e){return e}getPixelForValue(e,n){return NaN}getValueForPixel(e){}getPixelForTick(e){const n=this.ticks;return e<0||e>n.length-1?null:this.getPixelForValue(n[e].value)}getPixelForDecimal(e){this._reversePixels&&(e=1-e);const n=this._startPixel+e*this._length;return W1(this._alignToPixels?Tn(this.chart,n,0):n)}getDecimalForPixel(e){const n=(e-this._startPixel)/this._length;return this._reversePixels?1-n:n}getBasePixel(){return this.getPixelForValue(this.getBaseValue())}getBaseValue(){const{min:e,max:n}=this;return e<0&&n<0?n:e>0&&n>0?e:0}getContext(e){const n=this.ticks||[];if(e>=0&&e<n.length){const r=n[e];return r.$context||(r.$context=B2(this.getContext(),e,r))}return this.$context||(this.$context=U2(this.chart.getContext(),this))}_tickSize(){const e=this.options.ticks,n=Bt(this.labelRotation),r=Math.abs(Math.cos(n)),i=Math.abs(Math.sin(n)),s=this._getLabelSizes(),a=e.autoSkipPadding||0,o=s?s.widest.width+a:0,l=s?s.highest.height+a:0;return this.isHorizontal()?l*r>o*i?o/r:l/i:l*i<o*r?l/r:o/i}_isVisible(){const e=this.options.display;return e!=="auto"?!!e:this.getMatchingVisibleMetas().length>0}_computeGridLineItems(e){const n=this.axis,r=this.chart,i=this.options,{grid:s,position:a,border:o}=i,l=s.offset,u=this.isHorizontal(),h=this.ticks.length+(l?1:0),f=ai(s),m=[],x=o.setContext(this.getContext()),v=x.display?x.width:0,w=v/2,g=function(B){return Tn(r,B,v)};let p,y,b,S,j,N,k,C,P,M,D,$;if(a==="top")p=g(this.bottom),N=this.bottom-f,C=p-w,M=g(e.top)+w,$=e.bottom;else if(a==="bottom")p=g(this.top),M=e.top,$=g(e.bottom)-w,N=p+w,C=this.top+f;else if(a==="left")p=g(this.right),j=this.right-f,k=p-w,P=g(e.left)+w,D=e.right;else if(a==="right")p=g(this.left),P=e.left,D=g(e.right)-w,j=p+w,k=this.left+f;else if(n==="x"){if(a==="center")p=g((e.top+e.bottom)/2+.5);else if(V(a)){const B=Object.keys(a)[0],Y=a[B];p=g(this.chart.scales[B].getPixelForValue(Y))}M=e.top,$=e.bottom,N=p+w,C=N+f}else if(n==="y"){if(a==="center")p=g((e.left+e.right)/2);else if(V(a)){const B=Object.keys(a)[0],Y=a[B];p=g(this.chart.scales[B].getPixelForValue(Y))}j=p-w,k=j-f,P=e.left,D=e.right}const U=X(i.ticks.maxTicksLimit,h),z=Math.max(1,Math.ceil(h/U));for(y=0;y<h;y+=z){const B=this.getContext(y),Y=s.setContext(B),T=o.setContext(B),L=Y.lineWidth,A=Y.color,R=T.dash||[],H=T.dashOffset,ze=Y.tickWidth,pe=Y.tickColor,dt=Y.tickBorderDash||[],Se=Y.tickBorderDashOffset;b=z2(this,y,l),b!==void 0&&(S=Tn(r,b,L),u?j=k=P=D=S:N=C=M=$=S,m.push({tx1:j,ty1:N,tx2:k,ty2:C,x1:P,y1:M,x2:D,y2:$,width:L,color:A,borderDash:R,borderDashOffset:H,tickWidth:ze,tickColor:pe,tickBorderDash:dt,tickBorderDashOffset:Se}))}return this._ticksLength=h,this._borderValue=p,m}_computeLabelItems(e){const n=this.axis,r=this.options,{position:i,ticks:s}=r,a=this.isHorizontal(),o=this.ticks,{align:l,crossAlign:u,padding:d,mirror:h}=s,f=ai(r.grid),m=f+d,x=h?-d:m,v=-Bt(this.labelRotation),w=[];let g,p,y,b,S,j,N,k,C,P,M,D,$="middle";if(i==="top")j=this.bottom-x,N=this._getXAxisLabelAlignment();else if(i==="bottom")j=this.top+x,N=this._getXAxisLabelAlignment();else if(i==="left"){const z=this._getYAxisLabelAlignment(f);N=z.textAlign,S=z.x}else if(i==="right"){const z=this._getYAxisLabelAlignment(f);N=z.textAlign,S=z.x}else if(n==="x"){if(i==="center")j=(e.top+e.bottom)/2+m;else if(V(i)){const z=Object.keys(i)[0],B=i[z];j=this.chart.scales[z].getPixelForValue(B)+m}N=this._getXAxisLabelAlignment()}else if(n==="y"){if(i==="center")S=(e.left+e.right)/2-m;else if(V(i)){const z=Object.keys(i)[0],B=i[z];S=this.chart.scales[z].getPixelForValue(B)}N=this._getYAxisLabelAlignment(f).textAlign}n==="y"&&(l==="start"?$="top":l==="end"&&($="bottom"));const U=this._getLabelSizes();for(g=0,p=o.length;g<p;++g){y=o[g],b=y.label;const z=s.setContext(this.getContext(g));k=this.getPixelForTick(g)+s.labelOffset,C=this._resolveTickFontOptions(g),P=C.lineHeight,M=xe(b)?b.length:1;const B=M/2,Y=z.color,T=z.textStrokeColor,L=z.textStrokeWidth;let A=N;a?(S=k,N==="inner"&&(g===p-1?A=this.options.reverse?"left":"right":g===0?A=this.options.reverse?"right":"left":A="center"),i==="top"?u==="near"||v!==0?D=-M*P+P/2:u==="center"?D=-U.highest.height/2-B*P+P:D=-U.highest.height+P/2:u==="near"||v!==0?D=P/2:u==="center"?D=U.highest.height/2-B*P:D=U.highest.height-M*P,h&&(D*=-1),v!==0&&!z.showLabelBackdrop&&(S+=P/2*Math.sin(v))):(j=k,D=(1-M)*P/2);let R;if(z.showLabelBackdrop){const H=ut(z.backdropPadding),ze=U.heights[g],pe=U.widths[g];let dt=D-H.top,Se=0-H.left;switch($){case"middle":dt-=ze/2;break;case"bottom":dt-=ze;break}switch(N){case"center":Se-=pe/2;break;case"right":Se-=pe;break;case"inner":g===p-1?Se-=pe:g>0&&(Se-=pe/2);break}R={left:Se,top:dt,width:pe+H.width,height:ze+H.height,color:z.backdropColor}}w.push({label:b,font:C,textOffset:D,options:{rotation:v,color:Y,strokeColor:T,strokeWidth:L,textAlign:A,textBaseline:$,translation:[S,j],backdrop:R}})}return w}_getXAxisLabelAlignment(){const{position:e,ticks:n}=this.options;if(-Bt(this.labelRotation))return e==="top"?"left":"right";let i="center";return n.align==="start"?i="left":n.align==="end"?i="right":n.align==="inner"&&(i="inner"),i}_getYAxisLabelAlignment(e){const{position:n,ticks:{crossAlign:r,mirror:i,padding:s}}=this.options,a=this._getLabelSizes(),o=e+s,l=a.widest.width;let u,d;return n==="left"?i?(d=this.right+s,r==="near"?u="left":r==="center"?(u="center",d+=l/2):(u="right",d+=l)):(d=this.right-o,r==="near"?u="right":r==="center"?(u="center",d-=l/2):(u="left",d=this.left)):n==="right"?i?(d=this.left+s,r==="near"?u="right":r==="center"?(u="center",d-=l/2):(u="left",d-=l)):(d=this.left+o,r==="near"?u="left":r==="center"?(u="center",d+=l/2):(u="right",d=this.right)):u="right",{textAlign:u,x:d}}_computeLabelArea(){if(this.options.ticks.mirror)return;const e=this.chart,n=this.options.position;if(n==="left"||n==="right")return{top:0,left:this.left,bottom:e.height,right:this.right};if(n==="top"||n==="bottom")return{top:this.top,left:0,bottom:this.bottom,right:e.width}}drawBackground(){const{ctx:e,options:{backgroundColor:n},left:r,top:i,width:s,height:a}=this;n&&(e.save(),e.fillStyle=n,e.fillRect(r,i,s,a),e.restore())}getLineWidthForValue(e){const n=this.options.grid;if(!this._isVisible()||!n.display)return 0;const i=this.ticks.findIndex(s=>s.value===e);return i>=0?n.setContext(this.getContext(i)).lineWidth:0}drawGrid(e){const n=this.options.grid,r=this.ctx,i=this._gridLineItems||(this._gridLineItems=this._computeGridLineItems(e));let s,a;const o=(l,u,d)=>{!d.width||!d.color||(r.save(),r.lineWidth=d.width,r.strokeStyle=d.color,r.setLineDash(d.borderDash||[]),r.lineDashOffset=d.borderDashOffset,r.beginPath(),r.moveTo(l.x,l.y),r.lineTo(u.x,u.y),r.stroke(),r.restore())};if(n.display)for(s=0,a=i.length;s<a;++s){const l=i[s];n.drawOnChartArea&&o({x:l.x1,y:l.y1},{x:l.x2,y:l.y2},l),n.drawTicks&&o({x:l.tx1,y:l.ty1},{x:l.tx2,y:l.ty2},{color:l.tickColor,width:l.tickWidth,borderDash:l.tickBorderDash,borderDashOffset:l.tickBorderDashOffset})}}drawBorder(){const{chart:e,ctx:n,options:{border:r,grid:i}}=this,s=r.setContext(this.getContext()),a=r.display?s.width:0;if(!a)return;const o=i.setContext(this.getContext(0)).lineWidth,l=this._borderValue;let u,d,h,f;this.isHorizontal()?(u=Tn(e,this.left,a)-a/2,d=Tn(e,this.right,o)+o/2,h=f=l):(h=Tn(e,this.top,a)-a/2,f=Tn(e,this.bottom,o)+o/2,u=d=l),n.save(),n.lineWidth=s.width,n.strokeStyle=s.color,n.beginPath(),n.moveTo(u,h),n.lineTo(d,f),n.stroke(),n.restore()}drawLabels(e){if(!this.options.ticks.display)return;const r=this.ctx,i=this._computeLabelArea();i&&fu(r,i);const s=this.getLabelItems(e);for(const a of s){const o=a.options,l=a.font,u=a.label,d=a.textOffset;Zi(r,u,0,d,l,o)}i&&mu(r)}drawTitle(){const{ctx:e,options:{position:n,title:r,reverse:i}}=this;if(!r.display)return;const s=Me(r.font),a=ut(r.padding),o=r.align;let l=s.lineHeight/2;n==="bottom"||n==="center"||V(n)?(l+=a.bottom,xe(r.text)&&(l+=s.lineHeight*(r.text.length-1))):l+=a.top;const{titleX:u,titleY:d,maxWidth:h,rotation:f}=W2(this,l,n,o);Zi(e,r.text,0,0,s,{color:r.color,maxWidth:h,rotation:f,textAlign:H2(o,n,i),textBaseline:"middle",translation:[u,d]})}draw(e){this._isVisible()&&(this.drawBackground(),this.drawGrid(e),this.drawBorder(),this.drawTitle(),this.drawLabels(e))}_layers(){const e=this.options,n=e.ticks&&e.ticks.z||0,r=X(e.grid&&e.grid.z,-1),i=X(e.border&&e.border.z,0);return!this._isVisible()||this.draw!==Yr.prototype.draw?[{z:n,draw:s=>{this.draw(s)}}]:[{z:r,draw:s=>{this.drawBackground(),this.drawGrid(s),this.drawTitle()}},{z:i,draw:()=>{this.drawBorder()}},{z:n,draw:s=>{this.drawLabels(s)}}]}getMatchingVisibleMetas(e){const n=this.chart.getSortedVisibleDatasetMetas(),r=this.axis+"AxisID",i=[];let s,a;for(s=0,a=n.length;s<a;++s){const o=n[s];o[r]===this.id&&(!e||o.type===e)&&i.push(o)}return i}_resolveTickFontOptions(e){const n=this.options.ticks.setContext(this.getContext(e));return Me(n.font)}_maxDigits(){const e=this._resolveTickFontOptions(0).lineHeight;return(this.isHorizontal()?this.width:this.height)/e}}class Ls{constructor(e,n,r){this.type=e,this.scope=n,this.override=r,this.items=Object.create(null)}isForType(e){return Object.prototype.isPrototypeOf.call(this.type.prototype,e.prototype)}register(e){const n=Object.getPrototypeOf(e);let r;X2(n)&&(r=this.register(n));const i=this.items,s=e.id,a=this.scope+"."+s;if(!s)throw new Error("class does not have id: "+e);return s in i||(i[s]=e,V2(e,a,r),this.override&&de.override(e.id,e.overrides)),a}get(e){return this.items[e]}unregister(e){const n=this.items,r=e.id,i=this.scope;r in n&&delete n[r],i&&r in de[i]&&(delete de[i][r],this.override&&delete qn[r])}}function V2(t,e,n){const r=Gi(Object.create(null),[n?de.get(n):{},de.get(e),t.defaults]);de.set(e,r),t.defaultRoutes&&Y2(e,t.defaultRoutes),t.descriptors&&de.describe(e,t.descriptors)}function Y2(t,e){Object.keys(e).forEach(n=>{const r=n.split("."),i=r.pop(),s=[t].concat(r).join("."),a=e[n].split("."),o=a.pop(),l=a.join(".");de.route(s,i,l,o)})}function X2(t){return"id"in t&&"defaults"in t}class K2{constructor(){this.controllers=new Ls(Pr,"datasets",!0),this.elements=new Ls(Kt,"elements"),this.plugins=new Ls(Object,"plugins"),this.scales=new Ls(Yr,"scales"),this._typedRegistries=[this.controllers,this.scales,this.elements]}add(...e){this._each("register",e)}remove(...e){this._each("unregister",e)}addControllers(...e){this._each("register",e,this.controllers)}addElements(...e){this._each("register",e,this.elements)}addPlugins(...e){this._each("register",e,this.plugins)}addScales(...e){this._each("register",e,this.scales)}getController(e){return this._get(e,this.controllers,"controller")}getElement(e){return this._get(e,this.elements,"element")}getPlugin(e){return this._get(e,this.plugins,"plugin")}getScale(e){return this._get(e,this.scales,"scale")}removeControllers(...e){this._each("unregister",e,this.controllers)}removeElements(...e){this._each("unregister",e,this.elements)}removePlugins(...e){this._each("unregister",e,this.plugins)}removeScales(...e){this._each("unregister",e,this.scales)}_each(e,n,r){[...n].forEach(i=>{const s=r||this._getRegistryForType(i);r||s.isForType(i)||s===this.plugins&&i.id?this._exec(e,s,i):Q(i,a=>{const o=r||this._getRegistryForType(a);this._exec(e,o,a)})})}_exec(e,n,r){const i=cu(e);ee(r["before"+i],[],r),n[e](r),ee(r["after"+i],[],r)}_getRegistryForType(e){for(let n=0;n<this._typedRegistries.length;n++){const r=this._typedRegistries[n];if(r.isForType(e))return r}return this.plugins}_get(e,n,r){const i=n.get(e);if(i===void 0)throw new Error('"'+e+'" is not a registered '+r+".");return i}}var Pt=new K2;class Q2{constructor(){this._init=[]}notify(e,n,r,i){n==="beforeInit"&&(this._init=this._createDescriptors(e,!0),this._notify(this._init,e,"install"));const s=i?this._descriptors(e).filter(i):this._descriptors(e),a=this._notify(s,e,n,r);return n==="afterDestroy"&&(this._notify(s,e,"stop"),this._notify(this._init,e,"uninstall")),a}_notify(e,n,r,i){i=i||{};for(const s of e){const a=s.plugin,o=a[r],l=[n,i,s.options];if(ee(o,l,a)===!1&&i.cancelable)return!1}return!0}invalidate(){q(this._cache)||(this._oldCache=this._cache,this._cache=void 0)}_descriptors(e){if(this._cache)return this._cache;const n=this._cache=this._createDescriptors(e);return this._notifyStateChanges(e),n}_createDescriptors(e,n){const r=e&&e.config,i=X(r.options&&r.options.plugins,{}),s=G2(r);return i===!1&&!n?[]:Z2(e,s,i,n)}_notifyStateChanges(e){const n=this._oldCache||[],r=this._cache,i=(s,a)=>s.filter(o=>!a.some(l=>o.plugin.id===l.plugin.id));this._notify(i(n,r),e,"stop"),this._notify(i(r,n),e,"start")}}function G2(t){const e={},n=[],r=Object.keys(Pt.plugins.items);for(let s=0;s<r.length;s++)n.push(Pt.getPlugin(r[s]));const i=t.plugins||[];for(let s=0;s<i.length;s++){const a=i[s];n.indexOf(a)===-1&&(n.push(a),e[a.id]=!0)}return{plugins:n,localIds:e}}function q2(t,e){return!e&&t===!1?null:t===!0?{}:t}function Z2(t,{plugins:e,localIds:n},r,i){const s=[],a=t.getContext();for(const o of e){const l=o.id,u=q2(r[l],i);u!==null&&s.push({plugin:o,options:J2(t.config,{plugin:o,local:n[l]},u,a)})}return s}function J2(t,{plugin:e,local:n},r,i){const s=t.pluginScopeKeys(e),a=t.getOptionScopes(r,s);return n&&e.defaults&&a.push(e.defaults),t.createResolver(a,i,[""],{scriptable:!1,indexable:!1,allKeys:!0})}function ic(t,e){const n=de.datasets[t]||{};return((e.datasets||{})[t]||{}).indexAxis||e.indexAxis||n.indexAxis||"x"}function eS(t,e){let n=t;return t==="_index_"?n=e:t==="_value_"&&(n=e==="x"?"y":"x"),n}function tS(t,e){return t===e?"_index_":"_value_"}function zh(t){if(t==="x"||t==="y"||t==="r")return t}function nS(t){if(t==="top"||t==="bottom")return"x";if(t==="left"||t==="right")return"y"}function sc(t,...e){if(zh(t))return t;for(const n of e){const r=n.axis||nS(n.position)||t.length>1&&zh(t[0].toLowerCase());if(r)return r}throw new Error(`Cannot determine type of '${t}' axis. Please provide 'axis' or 'position' option.`)}function Ih(t,e,n){if(n[e+"AxisID"]===t)return{axis:e}}function rS(t,e){if(e.data&&e.data.datasets){const n=e.data.datasets.filter(r=>r.xAxisID===t||r.yAxisID===t);if(n.length)return Ih(t,"x",n[0])||Ih(t,"y",n[0])}return{}}function iS(t,e){const n=qn[t.type]||{scales:{}},r=e.scales||{},i=ic(t.type,e),s=Object.create(null);return Object.keys(r).forEach(a=>{const o=r[a];if(!V(o))return console.error(`Invalid scale configuration for scale: ${a}`);if(o._proxy)return console.warn(`Ignoring resolver passed as options for scale: ${a}`);const l=sc(a,o,rS(a,t),de.scales[o.type]),u=tS(l,i),d=n.scales||{};s[a]=ji(Object.create(null),[{axis:l},o,d[l],d[u]])}),t.data.datasets.forEach(a=>{const o=a.type||t.type,l=a.indexAxis||ic(o,e),d=(qn[o]||{}).scales||{};Object.keys(d).forEach(h=>{const f=eS(h,l),m=a[f+"AxisID"]||f;s[m]=s[m]||Object.create(null),ji(s[m],[{axis:f},r[m],d[h]])})}),Object.keys(s).forEach(a=>{const o=s[a];ji(o,[de.scales[o.type],de.scale])}),s}function Pg(t){const e=t.options||(t.options={});e.plugins=X(e.plugins,{}),e.scales=iS(t,e)}function Eg(t){return t=t||{},t.datasets=t.datasets||[],t.labels=t.labels||[],t}function sS(t){return t=t||{},t.data=Eg(t.data),Pg(t),t}const Uh=new Map,Tg=new Set;function As(t,e){let n=Uh.get(t);return n||(n=e(),Uh.set(t,n),Tg.add(n)),n}const oi=(t,e,n)=>{const r=Gn(e,n);r!==void 0&&t.add(r)};class aS{constructor(e){this._config=sS(e),this._scopeCache=new Map,this._resolverCache=new Map}get platform(){return this._config.platform}get type(){return this._config.type}set type(e){this._config.type=e}get data(){return this._config.data}set data(e){this._config.data=Eg(e)}get options(){return this._config.options}set options(e){this._config.options=e}get plugins(){return this._config.plugins}update(){const e=this._config;this.clearCache(),Pg(e)}clearCache(){this._scopeCache.clear(),this._resolverCache.clear()}datasetScopeKeys(e){return As(e,()=>[[`datasets.${e}`,""]])}datasetAnimationScopeKeys(e,n){return As(`${e}.transition.${n}`,()=>[[`datasets.${e}.transitions.${n}`,`transitions.${n}`],[`datasets.${e}`,""]])}datasetElementScopeKeys(e,n){return As(`${e}-${n}`,()=>[[`datasets.${e}.elements.${n}`,`datasets.${e}`,`elements.${n}`,""]])}pluginScopeKeys(e){const n=e.id,r=this.type;return As(`${r}-plugin-${n}`,()=>[[`plugins.${n}`,...e.additionalOptionScopes||[]]])}_cachedScopes(e,n){const r=this._scopeCache;let i=r.get(e);return(!i||n)&&(i=new Map,r.set(e,i)),i}getOptionScopes(e,n,r){const{options:i,type:s}=this,a=this._cachedScopes(e,r),o=a.get(n);if(o)return o;const l=new Set;n.forEach(d=>{e&&(l.add(e),d.forEach(h=>oi(l,e,h))),d.forEach(h=>oi(l,i,h)),d.forEach(h=>oi(l,qn[s]||{},h)),d.forEach(h=>oi(l,de,h)),d.forEach(h=>oi(l,rc,h))});const u=Array.from(l);return u.length===0&&u.push(Object.create(null)),Tg.has(n)&&a.set(n,u),u}chartOptionScopes(){const{options:e,type:n}=this;return[e,qn[n]||{},de.datasets[n]||{},{type:n},de,rc]}resolveNamedOptions(e,n,r,i=[""]){const s={$shared:!0},{resolver:a,subPrefixes:o}=Bh(this._resolverCache,e,i);let l=a;if(lS(a,n)){s.$shared=!1,r=Nn(r)?r():r;const u=this.createResolver(e,r,o);l=Fr(a,r,u)}for(const u of n)s[u]=l[u];return s}createResolver(e,n,r=[""],i){const{resolver:s}=Bh(this._resolverCache,e,r);return V(n)?Fr(s,n,void 0,i):s}}function Bh(t,e,n){let r=t.get(e);r||(r=new Map,t.set(e,r));const i=n.join();let s=r.get(i);return s||(s={resolver:gu(e,n),subPrefixes:n.filter(o=>!o.toLowerCase().includes("hover"))},r.set(i,s)),s}const oS=t=>V(t)&&Object.getOwnPropertyNames(t).some(e=>Nn(t[e]));function lS(t,e){const{isScriptable:n,isIndexable:r}=mg(t);for(const i of e){const s=n(i),a=r(i),o=(a||s)&&t[i];if(s&&(Nn(o)||oS(o))||a&&xe(o))return!0}return!1}var cS="4.4.9";const uS=["top","bottom","left","right","chartArea"];function Hh(t,e){return t==="top"||t==="bottom"||uS.indexOf(t)===-1&&e==="x"}function Wh(t,e){return function(n,r){return n[t]===r[t]?n[e]-r[e]:n[t]-r[t]}}function Vh(t){const e=t.chart,n=e.options.animation;e.notifyPlugins("afterRender"),ee(n&&n.onComplete,[t],e)}function dS(t){const e=t.chart,n=e.options.animation;ee(n&&n.onProgress,[t],e)}function Mg(t){return yu()&&typeof t=="string"?t=document.getElementById(t):t&&t.length&&(t=t[0]),t&&t.canvas&&(t=t.canvas),t}const ta={},Yh=t=>{const e=Mg(t);return Object.values(ta).filter(n=>n.canvas===e).pop()};function hS(t,e,n){const r=Object.keys(t);for(const i of r){const s=+i;if(s>=e){const a=t[i];delete t[i],(n>0||s>e)&&(t[s+n]=a)}}}function fS(t,e,n,r){return!n||t.type==="mouseout"?null:r?e:t}var Zt;let os=(Zt=class{static register(...e){Pt.add(...e),Xh()}static unregister(...e){Pt.remove(...e),Xh()}constructor(e,n){const r=this.config=new aS(n),i=Mg(e),s=Yh(i);if(s)throw new Error("Canvas is already in use. Chart with ID '"+s.id+"' must be destroyed before the canvas with ID '"+s.canvas.id+"' can be reused.");const a=r.createResolver(r.chartOptionScopes(),this.getContext());this.platform=new(r.platform||M2(i)),this.platform.updateConfig(r);const o=this.platform.acquireContext(i,a.aspectRatio),l=o&&o.canvas,u=l&&l.height,d=l&&l.width;if(this.id=T1(),this.ctx=o,this.canvas=l,this.width=d,this.height=u,this._options=a,this._aspectRatio=this.aspectRatio,this._layers=[],this._metasets=[],this._stacks=void 0,this.boxes=[],this.currentDevicePixelRatio=void 0,this.chartArea=void 0,this._active=[],this._lastEvent=void 0,this._listeners={},this._responsiveListeners=void 0,this._sortedMetasets=[],this.scales={},this._plugins=new Q2,this.$proxies={},this._hiddenIndices={},this.attached=!1,this._animationsDisabled=void 0,this.$context=void 0,this._doResize=K1(h=>this.update(h),a.resizeDelay||0),this._dataChanges=[],ta[this.id]=this,!o||!l){console.error("Failed to create chart: can't acquire context from the given item");return}Ot.listen(this,"complete",Vh),Ot.listen(this,"progress",dS),this._initialize(),this.attached&&this.update()}get aspectRatio(){const{options:{aspectRatio:e,maintainAspectRatio:n},width:r,height:i,_aspectRatio:s}=this;return q(e)?n&&s?s:i?r/i:null:e}get data(){return this.config.data}set data(e){this.config.data=e}get options(){return this._options}set options(e){this.config.options=e}get registry(){return Pt}_initialize(){return this.notifyPlugins("beforeInit"),this.options.responsive?this.resize():vh(this,this.options.devicePixelRatio),this.bindEvents(),this.notifyPlugins("afterInit"),this}clear(){return mh(this.canvas,this.ctx),this}stop(){return Ot.stop(this),this}resize(e,n){Ot.running(this)?this._resizeBeforeDraw={width:e,height:n}:this._resize(e,n)}_resize(e,n){const r=this.options,i=this.canvas,s=r.maintainAspectRatio&&this.aspectRatio,a=this.platform.getMaximumSize(i,e,n,s),o=r.devicePixelRatio||this.platform.getDevicePixelRatio(),l=this.width?"resize":"attach";this.width=a.width,this.height=a.height,this._aspectRatio=this.aspectRatio,vh(this,o,!0)&&(this.notifyPlugins("resize",{size:a}),ee(r.onResize,[this,a],this),this.attached&&this._doResize(l)&&this.render())}ensureScalesHaveIDs(){const n=this.options.scales||{};Q(n,(r,i)=>{r.id=i})}buildOrUpdateScales(){const e=this.options,n=e.scales,r=this.scales,i=Object.keys(r).reduce((a,o)=>(a[o]=!1,a),{});let s=[];n&&(s=s.concat(Object.keys(n).map(a=>{const o=n[a],l=sc(a,o),u=l==="r",d=l==="x";return{options:o,dposition:u?"chartArea":d?"bottom":"left",dtype:u?"radialLinear":d?"category":"linear"}}))),Q(s,a=>{const o=a.options,l=o.id,u=sc(l,o),d=X(o.type,a.dtype);(o.position===void 0||Hh(o.position,u)!==Hh(a.dposition))&&(o.position=a.dposition),i[l]=!0;let h=null;if(l in r&&r[l].type===d)h=r[l];else{const f=Pt.getScale(d);h=new f({id:l,type:d,ctx:this.ctx,chart:this}),r[h.id]=h}h.init(o,e)}),Q(i,(a,o)=>{a||delete r[o]}),Q(r,a=>{st.configure(this,a,a.options),st.addBox(this,a)})}_updateMetasets(){const e=this._metasets,n=this.data.datasets.length,r=e.length;if(e.sort((i,s)=>i.index-s.index),r>n){for(let i=n;i<r;++i)this._destroyDatasetMeta(i);e.splice(n,r-n)}this._sortedMetasets=e.slice(0).sort(Wh("order","index"))}_removeUnreferencedMetasets(){const{_metasets:e,data:{datasets:n}}=this;e.length>n.length&&delete this._stacks,e.forEach((r,i)=>{n.filter(s=>s===r._dataset).length===0&&this._destroyDatasetMeta(i)})}buildOrUpdateControllers(){const e=[],n=this.data.datasets;let r,i;for(this._removeUnreferencedMetasets(),r=0,i=n.length;r<i;r++){const s=n[r];let a=this.getDatasetMeta(r);const o=s.type||this.config.type;if(a.type&&a.type!==o&&(this._destroyDatasetMeta(r),a=this.getDatasetMeta(r)),a.type=o,a.indexAxis=s.indexAxis||ic(o,this.options),a.order=s.order||0,a.index=r,a.label=""+s.label,a.visible=this.isDatasetVisible(r),a.controller)a.controller.updateIndex(r),a.controller.linkScales();else{const l=Pt.getController(o),{datasetElementType:u,dataElementType:d}=de.datasets[o];Object.assign(l,{dataElementType:Pt.getElement(d),datasetElementType:u&&Pt.getElement(u)}),a.controller=new l(this,r),e.push(a.controller)}}return this._updateMetasets(),e}_resetElements(){Q(this.data.datasets,(e,n)=>{this.getDatasetMeta(n).controller.reset()},this)}reset(){this._resetElements(),this.notifyPlugins("reset")}update(e){const n=this.config;n.update();const r=this._options=n.createResolver(n.chartOptionScopes(),this.getContext()),i=this._animationsDisabled=!r.animation;if(this._updateScales(),this._checkEventBindings(),this._updateHiddenIndices(),this._plugins.invalidate(),this.notifyPlugins("beforeUpdate",{mode:e,cancelable:!0})===!1)return;const s=this.buildOrUpdateControllers();this.notifyPlugins("beforeElementsUpdate");let a=0;for(let u=0,d=this.data.datasets.length;u<d;u++){const{controller:h}=this.getDatasetMeta(u),f=!i&&s.indexOf(h)===-1;h.buildOrUpdateElements(f),a=Math.max(+h.getMaxOverflow(),a)}a=this._minPadding=r.layout.autoPadding?a:0,this._updateLayout(a),i||Q(s,u=>{u.reset()}),this._updateDatasets(e),this.notifyPlugins("afterUpdate",{mode:e}),this._layers.sort(Wh("z","_idx"));const{_active:o,_lastEvent:l}=this;l?this._eventHandler(l,!0):o.length&&this._updateHoverStyles(o,o,!0),this.render()}_updateScales(){Q(this.scales,e=>{st.removeBox(this,e)}),this.ensureScalesHaveIDs(),this.buildOrUpdateScales()}_checkEventBindings(){const e=this.options,n=new Set(Object.keys(this._listeners)),r=new Set(e.events);(!ih(n,r)||!!this._responsiveListeners!==e.responsive)&&(this.unbindEvents(),this.bindEvents())}_updateHiddenIndices(){const{_hiddenIndices:e}=this,n=this._getUniformDataChanges()||[];for(const{method:r,start:i,count:s}of n){const a=r==="_removeElements"?-s:s;hS(e,i,a)}}_getUniformDataChanges(){const e=this._dataChanges;if(!e||!e.length)return;this._dataChanges=[];const n=this.data.datasets.length,r=s=>new Set(e.filter(a=>a[0]===s).map((a,o)=>o+","+a.splice(1).join(","))),i=r(0);for(let s=1;s<n;s++)if(!ih(i,r(s)))return;return Array.from(i).map(s=>s.split(",")).map(s=>({method:s[1],start:+s[2],count:+s[3]}))}_updateLayout(e){if(this.notifyPlugins("beforeLayout",{cancelable:!0})===!1)return;st.update(this,this.width,this.height,e);const n=this.chartArea,r=n.width<=0||n.height<=0;this._layers=[],Q(this.boxes,i=>{r&&i.position==="chartArea"||(i.configure&&i.configure(),this._layers.push(...i._layers()))},this),this._layers.forEach((i,s)=>{i._idx=s}),this.notifyPlugins("afterLayout")}_updateDatasets(e){if(this.notifyPlugins("beforeDatasetsUpdate",{mode:e,cancelable:!0})!==!1){for(let n=0,r=this.data.datasets.length;n<r;++n)this.getDatasetMeta(n).controller.configure();for(let n=0,r=this.data.datasets.length;n<r;++n)this._updateDataset(n,Nn(e)?e({datasetIndex:n}):e);this.notifyPlugins("afterDatasetsUpdate",{mode:e})}}_updateDataset(e,n){const r=this.getDatasetMeta(e),i={meta:r,index:e,mode:n,cancelable:!0};this.notifyPlugins("beforeDatasetUpdate",i)!==!1&&(r.controller._update(n),i.cancelable=!1,this.notifyPlugins("afterDatasetUpdate",i))}render(){this.notifyPlugins("beforeRender",{cancelable:!0})!==!1&&(Ot.has(this)?this.attached&&!Ot.running(this)&&Ot.start(this):(this.draw(),Vh({chart:this})))}draw(){let e;if(this._resizeBeforeDraw){const{width:r,height:i}=this._resizeBeforeDraw;this._resizeBeforeDraw=null,this._resize(r,i)}if(this.clear(),this.width<=0||this.height<=0||this.notifyPlugins("beforeDraw",{cancelable:!0})===!1)return;const n=this._layers;for(e=0;e<n.length&&n[e].z<=0;++e)n[e].draw(this.chartArea);for(this._drawDatasets();e<n.length;++e)n[e].draw(this.chartArea);this.notifyPlugins("afterDraw")}_getSortedDatasetMetas(e){const n=this._sortedMetasets,r=[];let i,s;for(i=0,s=n.length;i<s;++i){const a=n[i];(!e||a.visible)&&r.push(a)}return r}getSortedVisibleDatasetMetas(){return this._getSortedDatasetMetas(!0)}_drawDatasets(){if(this.notifyPlugins("beforeDatasetsDraw",{cancelable:!0})===!1)return;const e=this.getSortedVisibleDatasetMetas();for(let n=e.length-1;n>=0;--n)this._drawDataset(e[n]);this.notifyPlugins("afterDatasetsDraw")}_drawDataset(e){const n=this.ctx,r={meta:e,index:e.index,cancelable:!0},i=Rw(this,e);this.notifyPlugins("beforeDatasetDraw",r)!==!1&&(i&&fu(n,i),e.controller.draw(),i&&mu(n),r.cancelable=!1,this.notifyPlugins("afterDatasetDraw",r))}isPointInArea(e){return hg(e,this.chartArea,this._minPadding)}getElementsAtEventForMode(e,n,r,i){const s=u2.modes[n];return typeof s=="function"?s(this,e,r,i):[]}getDatasetMeta(e){const n=this.data.datasets[e],r=this._metasets;let i=r.filter(s=>s&&s._dataset===n).pop();return i||(i={type:null,data:[],dataset:null,controller:null,hidden:null,xAxisID:null,yAxisID:null,order:n&&n.order||0,index:e,_dataset:n,_parsed:[],_sorted:!1},r.push(i)),i}getContext(){return this.$context||(this.$context=Vr(null,{chart:this,type:"chart"}))}getVisibleDatasetCount(){return this.getSortedVisibleDatasetMetas().length}isDatasetVisible(e){const n=this.data.datasets[e];if(!n)return!1;const r=this.getDatasetMeta(e);return typeof r.hidden=="boolean"?!r.hidden:!n.hidden}setDatasetVisibility(e,n){const r=this.getDatasetMeta(e);r.hidden=!n}toggleDataVisibility(e){this._hiddenIndices[e]=!this._hiddenIndices[e]}getDataVisibility(e){return!this._hiddenIndices[e]}_updateVisibility(e,n,r){const i=r?"show":"hide",s=this.getDatasetMeta(e),a=s.controller._resolveAnimations(void 0,i);qi(n)?(s.data[n].hidden=!r,this.update()):(this.setDatasetVisibility(e,r),a.update(s,{visible:r}),this.update(o=>o.datasetIndex===e?i:void 0))}hide(e,n){this._updateVisibility(e,n,!1)}show(e,n){this._updateVisibility(e,n,!0)}_destroyDatasetMeta(e){const n=this._metasets[e];n&&n.controller&&n.controller._destroy(),delete this._metasets[e]}_stop(){let e,n;for(this.stop(),Ot.remove(this),e=0,n=this.data.datasets.length;e<n;++e)this._destroyDatasetMeta(e)}destroy(){this.notifyPlugins("beforeDestroy");const{canvas:e,ctx:n}=this;this._stop(),this.config.clearCache(),e&&(this.unbindEvents(),mh(e,n),this.platform.releaseContext(n),this.canvas=null,this.ctx=null),delete ta[this.id],this.notifyPlugins("afterDestroy")}toBase64Image(...e){return this.canvas.toDataURL(...e)}bindEvents(){this.bindUserEvents(),this.options.responsive?this.bindResponsiveEvents():this.attached=!0}bindUserEvents(){const e=this._listeners,n=this.platform,r=(s,a)=>{n.addEventListener(this,s,a),e[s]=a},i=(s,a,o)=>{s.offsetX=a,s.offsetY=o,this._eventHandler(s)};Q(this.options.events,s=>r(s,i))}bindResponsiveEvents(){this._responsiveListeners||(this._responsiveListeners={});const e=this._responsiveListeners,n=this.platform,r=(l,u)=>{n.addEventListener(this,l,u),e[l]=u},i=(l,u)=>{e[l]&&(n.removeEventListener(this,l,u),delete e[l])},s=(l,u)=>{this.canvas&&this.resize(l,u)};let a;const o=()=>{i("attach",o),this.attached=!0,this.resize(),r("resize",s),r("detach",a)};a=()=>{this.attached=!1,i("resize",s),this._stop(),this._resize(0,0),r("attach",o)},n.isAttached(this.canvas)?o():a()}unbindEvents(){Q(this._listeners,(e,n)=>{this.platform.removeEventListener(this,n,e)}),this._listeners={},Q(this._responsiveListeners,(e,n)=>{this.platform.removeEventListener(this,n,e)}),this._responsiveListeners=void 0}updateHoverStyle(e,n,r){const i=r?"set":"remove";let s,a,o,l;for(n==="dataset"&&(s=this.getDatasetMeta(e[0].datasetIndex),s.controller["_"+i+"DatasetHoverStyle"]()),o=0,l=e.length;o<l;++o){a=e[o];const u=a&&this.getDatasetMeta(a.datasetIndex).controller;u&&u[i+"HoverStyle"](a.element,a.datasetIndex,a.index)}}getActiveElements(){return this._active||[]}setActiveElements(e){const n=this._active||[],r=e.map(({datasetIndex:s,index:a})=>{const o=this.getDatasetMeta(s);if(!o)throw new Error("No dataset found at index "+s);return{datasetIndex:s,element:o.data[a],index:a}});!Da(r,n)&&(this._active=r,this._lastEvent=null,this._updateHoverStyles(r,n))}notifyPlugins(e,n,r){return this._plugins.notify(this,e,n,r)}isPluginEnabled(e){return this._plugins._cache.filter(n=>n.plugin.id===e).length===1}_updateHoverStyles(e,n,r){const i=this.options.hover,s=(l,u)=>l.filter(d=>!u.some(h=>d.datasetIndex===h.datasetIndex&&d.index===h.index)),a=s(n,e),o=r?e:s(e,n);a.length&&this.updateHoverStyle(a,i.mode,!1),o.length&&i.mode&&this.updateHoverStyle(o,i.mode,!0)}_eventHandler(e,n){const r={event:e,replay:n,cancelable:!0,inChartArea:this.isPointInArea(e)},i=a=>(a.options.events||this.options.events).includes(e.native.type);if(this.notifyPlugins("beforeEvent",r,i)===!1)return;const s=this._handleEvent(e,n,r.inChartArea);return r.cancelable=!1,this.notifyPlugins("afterEvent",r,i),(s||r.changed)&&this.render(),this}_handleEvent(e,n,r){const{_active:i=[],options:s}=this,a=n,o=this._getActiveElements(e,i,r,a),l=L1(e),u=fS(e,this._lastEvent,r,l);r&&(this._lastEvent=null,ee(s.onHover,[e,o,this],this),l&&ee(s.onClick,[e,o,this],this));const d=!Da(o,i);return(d||n)&&(this._active=o,this._updateHoverStyles(o,i,n)),this._lastEvent=u,d}_getActiveElements(e,n,r,i){if(e.type==="mouseout")return[];if(!r)return n;const s=this.options.hover;return this.getElementsAtEventForMode(e,s.mode,s,i)}},F(Zt,"defaults",de),F(Zt,"instances",ta),F(Zt,"overrides",qn),F(Zt,"registry",Pt),F(Zt,"version",cS),F(Zt,"getChart",Yh),Zt);function Xh(){return Q(os.instances,t=>t._plugins.invalidate())}function mS(t,e,n){const{startAngle:r,pixelMargin:i,x:s,y:a,outerRadius:o,innerRadius:l}=e;let u=i/o;t.beginPath(),t.arc(s,a,o,r-u,n+u),l>i?(u=i/l,t.arc(s,a,l,n+u,r-u,!0)):t.arc(s,a,i,n+ye,r-ye),t.closePath(),t.clip()}function pS(t){return pu(t,["outerStart","outerEnd","innerStart","innerEnd"])}function gS(t,e,n,r){const i=pS(t.options.borderRadius),s=(n-e)/2,a=Math.min(s,r*e/2),o=l=>{const u=(n-Math.min(s,l))*r/2;return We(l,0,Math.min(s,u))};return{outerStart:o(i.outerStart),outerEnd:o(i.outerEnd),innerStart:We(i.innerStart,0,a),innerEnd:We(i.innerEnd,0,a)}}function sr(t,e,n,r){return{x:n+t*Math.cos(e),y:r+t*Math.sin(e)}}function Ba(t,e,n,r,i,s){const{x:a,y:o,startAngle:l,pixelMargin:u,innerRadius:d}=e,h=Math.max(e.outerRadius+r+n-u,0),f=d>0?d+r+n+u:0;let m=0;const x=i-l;if(r){const z=d>0?d-r:0,B=h>0?h-r:0,Y=(z+B)/2,T=Y!==0?x*Y/(Y+r):x;m=(x-T)/2}const v=Math.max(.001,x*h-n/me)/h,w=(x-v)/2,g=l+w+m,p=i-w-m,{outerStart:y,outerEnd:b,innerStart:S,innerEnd:j}=gS(e,f,h,p-g),N=h-y,k=h-b,C=g+y/N,P=p-b/k,M=f+S,D=f+j,$=g+S/M,U=p-j/D;if(t.beginPath(),s){const z=(C+P)/2;if(t.arc(a,o,h,C,z),t.arc(a,o,h,z,P),b>0){const L=sr(k,P,a,o);t.arc(L.x,L.y,b,P,p+ye)}const B=sr(D,p,a,o);if(t.lineTo(B.x,B.y),j>0){const L=sr(D,U,a,o);t.arc(L.x,L.y,j,p+ye,U+Math.PI)}const Y=(p-j/f+(g+S/f))/2;if(t.arc(a,o,f,p-j/f,Y,!0),t.arc(a,o,f,Y,g+S/f,!0),S>0){const L=sr(M,$,a,o);t.arc(L.x,L.y,S,$+Math.PI,g-ye)}const T=sr(N,g,a,o);if(t.lineTo(T.x,T.y),y>0){const L=sr(N,C,a,o);t.arc(L.x,L.y,y,g-ye,C)}}else{t.moveTo(a,o);const z=Math.cos(C)*h+a,B=Math.sin(C)*h+o;t.lineTo(z,B);const Y=Math.cos(P)*h+a,T=Math.sin(P)*h+o;t.lineTo(Y,T)}t.closePath()}function xS(t,e,n,r,i){const{fullCircles:s,startAngle:a,circumference:o}=e;let l=e.endAngle;if(s){Ba(t,e,n,r,l,i);for(let u=0;u<s;++u)t.fill();isNaN(o)||(l=a+(o%fe||fe))}return Ba(t,e,n,r,l,i),t.fill(),l}function vS(t,e,n,r,i){const{fullCircles:s,startAngle:a,circumference:o,options:l}=e,{borderWidth:u,borderJoinStyle:d,borderDash:h,borderDashOffset:f}=l,m=l.borderAlign==="inner";if(!u)return;t.setLineDash(h||[]),t.lineDashOffset=f,m?(t.lineWidth=u*2,t.lineJoin=d||"round"):(t.lineWidth=u,t.lineJoin=d||"bevel");let x=e.endAngle;if(s){Ba(t,e,n,r,x,i);for(let v=0;v<s;++v)t.stroke();isNaN(o)||(x=a+(o%fe||fe))}m&&mS(t,e,x),s||(Ba(t,e,n,r,x,i),t.stroke())}class pi extends Kt{constructor(n){super();F(this,"circumference");F(this,"endAngle");F(this,"fullCircles");F(this,"innerRadius");F(this,"outerRadius");F(this,"pixelMargin");F(this,"startAngle");this.options=void 0,this.circumference=void 0,this.startAngle=void 0,this.endAngle=void 0,this.innerRadius=void 0,this.outerRadius=void 0,this.pixelMargin=0,this.fullCircles=0,n&&Object.assign(this,n)}inRange(n,r,i){const s=this.getProps(["x","y"],i),{angle:a,distance:o}=ig(s,{x:n,y:r}),{startAngle:l,endAngle:u,innerRadius:d,outerRadius:h,circumference:f}=this.getProps(["startAngle","endAngle","innerRadius","outerRadius","circumference"],i),m=(this.options.spacing+this.options.borderWidth)/2,x=X(f,u-l),v=Fa(a,l,u)&&l!==u,w=x>=fe||v,g=In(o,d+m,h+m);return w&&g}getCenterPoint(n){const{x:r,y:i,startAngle:s,endAngle:a,innerRadius:o,outerRadius:l}=this.getProps(["x","y","startAngle","endAngle","innerRadius","outerRadius"],n),{offset:u,spacing:d}=this.options,h=(s+a)/2,f=(o+l+d+u)/2;return{x:r+Math.cos(h)*f,y:i+Math.sin(h)*f}}tooltipPosition(n){return this.getCenterPoint(n)}draw(n){const{options:r,circumference:i}=this,s=(r.offset||0)/4,a=(r.spacing||0)/2,o=r.circular;if(this.pixelMargin=r.borderAlign==="inner"?.33:0,this.fullCircles=i>fe?Math.floor(i/fe):0,i===0||this.innerRadius<0||this.outerRadius<0)return;n.save();const l=(this.startAngle+this.endAngle)/2;n.translate(Math.cos(l)*s,Math.sin(l)*s);const u=1-Math.sin(Math.min(me,i||0)),d=s*u;n.fillStyle=r.backgroundColor,n.strokeStyle=r.borderColor,xS(n,this,d,a,o),vS(n,this,d,a,o),n.restore()}}F(pi,"id","arc"),F(pi,"defaults",{borderAlign:"center",borderColor:"#fff",borderDash:[],borderDashOffset:0,borderJoinStyle:void 0,borderRadius:0,borderWidth:2,offset:0,spacing:0,angle:void 0,circular:!0}),F(pi,"defaultRoutes",{backgroundColor:"backgroundColor"}),F(pi,"descriptors",{_scriptable:!0,_indexable:n=>n!=="borderDash"});function $g(t,e){const{x:n,y:r,base:i,width:s,height:a}=t.getProps(["x","y","base","width","height"],e);let o,l,u,d,h;return t.horizontal?(h=a/2,o=Math.min(n,i),l=Math.max(n,i),u=r-h,d=r+h):(h=s/2,o=n-h,l=n+h,u=Math.min(r,i),d=Math.max(r,i)),{left:o,top:u,right:l,bottom:d}}function ln(t,e,n,r){return t?0:We(e,n,r)}function yS(t,e,n){const r=t.options.borderWidth,i=t.borderSkipped,s=fg(r);return{t:ln(i.top,s.top,0,n),r:ln(i.right,s.right,0,e),b:ln(i.bottom,s.bottom,0,n),l:ln(i.left,s.left,0,e)}}function bS(t,e,n){const{enableBorderRadius:r}=t.getProps(["enableBorderRadius"]),i=t.options.borderRadius,s=jr(i),a=Math.min(e,n),o=t.borderSkipped,l=r||V(i);return{topLeft:ln(!l||o.top||o.left,s.topLeft,0,a),topRight:ln(!l||o.top||o.right,s.topRight,0,a),bottomLeft:ln(!l||o.bottom||o.left,s.bottomLeft,0,a),bottomRight:ln(!l||o.bottom||o.right,s.bottomRight,0,a)}}function wS(t){const e=$g(t),n=e.right-e.left,r=e.bottom-e.top,i=yS(t,n/2,r/2),s=bS(t,n/2,r/2);return{outer:{x:e.left,y:e.top,w:n,h:r,radius:s},inner:{x:e.left+i.l,y:e.top+i.t,w:n-i.l-i.r,h:r-i.t-i.b,radius:{topLeft:Math.max(0,s.topLeft-Math.max(i.t,i.l)),topRight:Math.max(0,s.topRight-Math.max(i.t,i.r)),bottomLeft:Math.max(0,s.bottomLeft-Math.max(i.b,i.l)),bottomRight:Math.max(0,s.bottomRight-Math.max(i.b,i.r))}}}}function Jo(t,e,n,r){const i=e===null,s=n===null,o=t&&!(i&&s)&&$g(t,r);return o&&(i||In(e,o.left,o.right))&&(s||In(n,o.top,o.bottom))}function SS(t){return t.topLeft||t.topRight||t.bottomLeft||t.bottomRight}function NS(t,e){t.rect(e.x,e.y,e.w,e.h)}function el(t,e,n={}){const r=t.x!==n.x?-e:0,i=t.y!==n.y?-e:0,s=(t.x+t.w!==n.x+n.w?e:0)-r,a=(t.y+t.h!==n.y+n.h?e:0)-i;return{x:t.x+r,y:t.y+i,w:t.w+s,h:t.h+a,radius:t.radius}}class na extends Kt{constructor(e){super(),this.options=void 0,this.horizontal=void 0,this.base=void 0,this.width=void 0,this.height=void 0,this.inflateAmount=void 0,e&&Object.assign(this,e)}draw(e){const{inflateAmount:n,options:{borderColor:r,backgroundColor:i}}=this,{inner:s,outer:a}=wS(this),o=SS(a.radius)?za:NS;e.save(),(a.w!==s.w||a.h!==s.h)&&(e.beginPath(),o(e,el(a,n,s)),e.clip(),o(e,el(s,-n,a)),e.fillStyle=r,e.fill("evenodd")),e.beginPath(),o(e,el(s,n)),e.fillStyle=i,e.fill(),e.restore()}inRange(e,n,r){return Jo(this,e,n,r)}inXRange(e,n){return Jo(this,e,null,n)}inYRange(e,n){return Jo(this,null,e,n)}getCenterPoint(e){const{x:n,y:r,base:i,horizontal:s}=this.getProps(["x","y","base","horizontal"],e);return{x:s?(n+i)/2:n,y:s?r:(r+i)/2}}getRange(e){return e==="x"?this.width/2:this.height/2}}F(na,"id","bar"),F(na,"defaults",{borderSkipped:"start",borderWidth:0,borderRadius:0,inflateAmount:"auto",pointStyle:void 0}),F(na,"defaultRoutes",{backgroundColor:"backgroundColor",borderColor:"borderColor"});const Kh=(t,e)=>{let{boxHeight:n=e,boxWidth:r=e}=t;return t.usePointStyle&&(n=Math.min(n,e),r=t.pointStyleWidth||Math.min(r,e)),{boxWidth:r,boxHeight:n,itemHeight:Math.max(e,n)}},_S=(t,e)=>t!==null&&e!==null&&t.datasetIndex===e.datasetIndex&&t.index===e.index;class Qh extends Kt{constructor(e){super(),this._added=!1,this.legendHitBoxes=[],this._hoveredItem=null,this.doughnutMode=!1,this.chart=e.chart,this.options=e.options,this.ctx=e.ctx,this.legendItems=void 0,this.columnSizes=void 0,this.lineWidths=void 0,this.maxHeight=void 0,this.maxWidth=void 0,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.height=void 0,this.width=void 0,this._margins=void 0,this.position=void 0,this.weight=void 0,this.fullSize=void 0}update(e,n,r){this.maxWidth=e,this.maxHeight=n,this._margins=r,this.setDimensions(),this.buildLabels(),this.fit()}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=this._margins.left,this.right=this.width):(this.height=this.maxHeight,this.top=this._margins.top,this.bottom=this.height)}buildLabels(){const e=this.options.labels||{};let n=ee(e.generateLabels,[this.chart],this)||[];e.filter&&(n=n.filter(r=>e.filter(r,this.chart.data))),e.sort&&(n=n.sort((r,i)=>e.sort(r,i,this.chart.data))),this.options.reverse&&n.reverse(),this.legendItems=n}fit(){const{options:e,ctx:n}=this;if(!e.display){this.width=this.height=0;return}const r=e.labels,i=Me(r.font),s=i.size,a=this._computeTitleHeight(),{boxWidth:o,itemHeight:l}=Kh(r,s);let u,d;n.font=i.string,this.isHorizontal()?(u=this.maxWidth,d=this._fitRows(a,s,o,l)+10):(d=this.maxHeight,u=this._fitCols(a,i,o,l)+10),this.width=Math.min(u,e.maxWidth||this.maxWidth),this.height=Math.min(d,e.maxHeight||this.maxHeight)}_fitRows(e,n,r,i){const{ctx:s,maxWidth:a,options:{labels:{padding:o}}}=this,l=this.legendHitBoxes=[],u=this.lineWidths=[0],d=i+o;let h=e;s.textAlign="left",s.textBaseline="middle";let f=-1,m=-d;return this.legendItems.forEach((x,v)=>{const w=r+n/2+s.measureText(x.text).width;(v===0||u[u.length-1]+w+2*o>a)&&(h+=d,u[u.length-(v>0?0:1)]=0,m+=d,f++),l[v]={left:0,top:m,row:f,width:w,height:i},u[u.length-1]+=w+o}),h}_fitCols(e,n,r,i){const{ctx:s,maxHeight:a,options:{labels:{padding:o}}}=this,l=this.legendHitBoxes=[],u=this.columnSizes=[],d=a-e;let h=o,f=0,m=0,x=0,v=0;return this.legendItems.forEach((w,g)=>{const{itemWidth:p,itemHeight:y}=kS(r,n,s,w,i);g>0&&m+y+2*o>d&&(h+=f+o,u.push({width:f,height:m}),x+=f+o,v++,f=m=0),l[g]={left:x,top:m,col:v,width:p,height:y},f=Math.max(f,p),m+=y+o}),h+=f,u.push({width:f,height:m}),h}adjustHitBoxes(){if(!this.options.display)return;const e=this._computeTitleHeight(),{legendHitBoxes:n,options:{align:r,labels:{padding:i},rtl:s}}=this,a=Cr(s,this.left,this.width);if(this.isHorizontal()){let o=0,l=Pe(r,this.left+i,this.right-this.lineWidths[o]);for(const u of n)o!==u.row&&(o=u.row,l=Pe(r,this.left+i,this.right-this.lineWidths[o])),u.top+=this.top+e+i,u.left=a.leftForLtr(a.x(l),u.width),l+=u.width+i}else{let o=0,l=Pe(r,this.top+e+i,this.bottom-this.columnSizes[o].height);for(const u of n)u.col!==o&&(o=u.col,l=Pe(r,this.top+e+i,this.bottom-this.columnSizes[o].height)),u.top=l,u.left+=this.left+i,u.left=a.leftForLtr(a.x(u.left),u.width),l+=u.height+i}}isHorizontal(){return this.options.position==="top"||this.options.position==="bottom"}draw(){if(this.options.display){const e=this.ctx;fu(e,this),this._draw(),mu(e)}}_draw(){const{options:e,columnSizes:n,lineWidths:r,ctx:i}=this,{align:s,labels:a}=e,o=de.color,l=Cr(e.rtl,this.left,this.width),u=Me(a.font),{padding:d}=a,h=u.size,f=h/2;let m;this.drawTitle(),i.textAlign=l.textAlign("left"),i.textBaseline="middle",i.lineWidth=.5,i.font=u.string;const{boxWidth:x,boxHeight:v,itemHeight:w}=Kh(a,h),g=function(j,N,k){if(isNaN(x)||x<=0||isNaN(v)||v<0)return;i.save();const C=X(k.lineWidth,1);if(i.fillStyle=X(k.fillStyle,o),i.lineCap=X(k.lineCap,"butt"),i.lineDashOffset=X(k.lineDashOffset,0),i.lineJoin=X(k.lineJoin,"miter"),i.lineWidth=C,i.strokeStyle=X(k.strokeStyle,o),i.setLineDash(X(k.lineDash,[])),a.usePointStyle){const P={radius:v*Math.SQRT2/2,pointStyle:k.pointStyle,rotation:k.rotation,borderWidth:C},M=l.xPlus(j,x/2),D=N+f;dg(i,P,M,D,a.pointStyleWidth&&x)}else{const P=N+Math.max((h-v)/2,0),M=l.leftForLtr(j,x),D=jr(k.borderRadius);i.beginPath(),Object.values(D).some($=>$!==0)?za(i,{x:M,y:P,w:x,h:v,radius:D}):i.rect(M,P,x,v),i.fill(),C!==0&&i.stroke()}i.restore()},p=function(j,N,k){Zi(i,k.text,j,N+w/2,u,{strikethrough:k.hidden,textAlign:l.textAlign(k.textAlign)})},y=this.isHorizontal(),b=this._computeTitleHeight();y?m={x:Pe(s,this.left+d,this.right-r[0]),y:this.top+d+b,line:0}:m={x:this.left+d,y:Pe(s,this.top+b+d,this.bottom-n[0].height),line:0},vg(this.ctx,e.textDirection);const S=w+d;this.legendItems.forEach((j,N)=>{i.strokeStyle=j.fontColor,i.fillStyle=j.fontColor;const k=i.measureText(j.text).width,C=l.textAlign(j.textAlign||(j.textAlign=a.textAlign)),P=x+f+k;let M=m.x,D=m.y;l.setWidth(this.width),y?N>0&&M+P+d>this.right&&(D=m.y+=S,m.line++,M=m.x=Pe(s,this.left+d,this.right-r[m.line])):N>0&&D+S>this.bottom&&(M=m.x=M+n[m.line].width+d,m.line++,D=m.y=Pe(s,this.top+b+d,this.bottom-n[m.line].height));const $=l.x(M);if(g($,D,j),M=Q1(C,M+x+f,y?M+P:this.right,e.rtl),p(l.x(M),D,j),y)m.x+=P+d;else if(typeof j.text!="string"){const U=u.lineHeight;m.y+=Rg(j,U)+d}else m.y+=S}),yg(this.ctx,e.textDirection)}drawTitle(){const e=this.options,n=e.title,r=Me(n.font),i=ut(n.padding);if(!n.display)return;const s=Cr(e.rtl,this.left,this.width),a=this.ctx,o=n.position,l=r.size/2,u=i.top+l;let d,h=this.left,f=this.width;if(this.isHorizontal())f=Math.max(...this.lineWidths),d=this.top+u,h=Pe(e.align,h,this.right-f);else{const x=this.columnSizes.reduce((v,w)=>Math.max(v,w.height),0);d=u+Pe(e.align,this.top,this.bottom-x-e.labels.padding-this._computeTitleHeight())}const m=Pe(o,h,h+f);a.textAlign=s.textAlign(du(o)),a.textBaseline="middle",a.strokeStyle=n.color,a.fillStyle=n.color,a.font=r.string,Zi(a,n.text,m,d,r)}_computeTitleHeight(){const e=this.options.title,n=Me(e.font),r=ut(e.padding);return e.display?n.lineHeight+r.height:0}_getLegendItemAt(e,n){let r,i,s;if(In(e,this.left,this.right)&&In(n,this.top,this.bottom)){for(s=this.legendHitBoxes,r=0;r<s.length;++r)if(i=s[r],In(e,i.left,i.left+i.width)&&In(n,i.top,i.top+i.height))return this.legendItems[r]}return null}handleEvent(e){const n=this.options;if(!PS(e.type,n))return;const r=this._getLegendItemAt(e.x,e.y);if(e.type==="mousemove"||e.type==="mouseout"){const i=this._hoveredItem,s=_S(i,r);i&&!s&&ee(n.onLeave,[e,i,this],this),this._hoveredItem=r,r&&!s&&ee(n.onHover,[e,r,this],this)}else r&&ee(n.onClick,[e,r,this],this)}}function kS(t,e,n,r,i){const s=jS(r,t,e,n),a=CS(i,r,e.lineHeight);return{itemWidth:s,itemHeight:a}}function jS(t,e,n,r){let i=t.text;return i&&typeof i!="string"&&(i=i.reduce((s,a)=>s.length>a.length?s:a)),e+n.size/2+r.measureText(i).width}function CS(t,e,n){let r=t;return typeof e.text!="string"&&(r=Rg(e,n)),r}function Rg(t,e){const n=t.text?t.text.length:0;return e*n}function PS(t,e){return!!((t==="mousemove"||t==="mouseout")&&(e.onHover||e.onLeave)||e.onClick&&(t==="click"||t==="mouseup"))}var Dg={id:"legend",_element:Qh,start(t,e,n){const r=t.legend=new Qh({ctx:t.ctx,options:n,chart:t});st.configure(t,r,n),st.addBox(t,r)},stop(t){st.removeBox(t,t.legend),delete t.legend},beforeUpdate(t,e,n){const r=t.legend;st.configure(t,r,n),r.options=n},afterUpdate(t){const e=t.legend;e.buildLabels(),e.adjustHitBoxes()},afterEvent(t,e){e.replay||t.legend.handleEvent(e.event)},defaults:{display:!0,position:"top",align:"center",fullSize:!0,reverse:!1,weight:1e3,onClick(t,e,n){const r=e.datasetIndex,i=n.chart;i.isDatasetVisible(r)?(i.hide(r),e.hidden=!0):(i.show(r),e.hidden=!1)},onHover:null,onLeave:null,labels:{color:t=>t.chart.options.color,boxWidth:40,padding:10,generateLabels(t){const e=t.data.datasets,{labels:{usePointStyle:n,pointStyle:r,textAlign:i,color:s,useBorderRadius:a,borderRadius:o}}=t.legend.options;return t._getSortedDatasetMetas().map(l=>{const u=l.controller.getStyle(n?0:void 0),d=ut(u.borderWidth);return{text:e[l.index].label,fillStyle:u.backgroundColor,fontColor:s,hidden:!l.visible,lineCap:u.borderCapStyle,lineDash:u.borderDash,lineDashOffset:u.borderDashOffset,lineJoin:u.borderJoinStyle,lineWidth:(d.width+d.height)/4,strokeStyle:u.borderColor,pointStyle:r||u.pointStyle,rotation:u.rotation,textAlign:i||u.textAlign,borderRadius:a&&(o||u.borderRadius),datasetIndex:l.index}},this)}},title:{color:t=>t.chart.options.color,display:!1,position:"center",text:""}},descriptors:{_scriptable:t=>!t.startsWith("on"),labels:{_scriptable:t=>!["generateLabels","filter","sort"].includes(t)}}};class Og extends Kt{constructor(e){super(),this.chart=e.chart,this.options=e.options,this.ctx=e.ctx,this._padding=void 0,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this.position=void 0,this.weight=void 0,this.fullSize=void 0}update(e,n){const r=this.options;if(this.left=0,this.top=0,!r.display){this.width=this.height=this.right=this.bottom=0;return}this.width=this.right=e,this.height=this.bottom=n;const i=xe(r.text)?r.text.length:1;this._padding=ut(r.padding);const s=i*Me(r.font).lineHeight+this._padding.height;this.isHorizontal()?this.height=s:this.width=s}isHorizontal(){const e=this.options.position;return e==="top"||e==="bottom"}_drawArgs(e){const{top:n,left:r,bottom:i,right:s,options:a}=this,o=a.align;let l=0,u,d,h;return this.isHorizontal()?(d=Pe(o,r,s),h=n+e,u=s-r):(a.position==="left"?(d=r+e,h=Pe(o,i,n),l=me*-.5):(d=s-e,h=Pe(o,n,i),l=me*.5),u=i-n),{titleX:d,titleY:h,maxWidth:u,rotation:l}}draw(){const e=this.ctx,n=this.options;if(!n.display)return;const r=Me(n.font),s=r.lineHeight/2+this._padding.top,{titleX:a,titleY:o,maxWidth:l,rotation:u}=this._drawArgs(s);Zi(e,n.text,0,0,r,{color:n.color,maxWidth:l,rotation:u,textAlign:du(n.align),textBaseline:"middle",translation:[a,o]})}}function ES(t,e){const n=new Og({ctx:t.ctx,options:e,chart:t});st.configure(t,n,e),st.addBox(t,n),t.titleBlock=n}var TS={id:"title",_element:Og,start(t,e,n){ES(t,n)},stop(t){const e=t.titleBlock;st.removeBox(t,e),delete t.titleBlock},beforeUpdate(t,e,n){const r=t.titleBlock;st.configure(t,r,n),r.options=n},defaults:{align:"center",display:!1,font:{weight:"bold"},fullSize:!0,padding:10,position:"top",text:"",weight:2e3},defaultRoutes:{color:"color"},descriptors:{_scriptable:!0,_indexable:!1}};const gi={average(t){if(!t.length)return!1;let e,n,r=new Set,i=0,s=0;for(e=0,n=t.length;e<n;++e){const o=t[e].element;if(o&&o.hasValue()){const l=o.tooltipPosition();r.add(l.x),i+=l.y,++s}}return s===0||r.size===0?!1:{x:[...r].reduce((o,l)=>o+l)/r.size,y:i/s}},nearest(t,e){if(!t.length)return!1;let n=e.x,r=e.y,i=Number.POSITIVE_INFINITY,s,a,o;for(s=0,a=t.length;s<a;++s){const l=t[s].element;if(l&&l.hasValue()){const u=l.getCenterPoint(),d=H1(e,u);d<i&&(i=d,o=l)}}if(o){const l=o.tooltipPosition();n=l.x,r=l.y}return{x:n,y:r}}};function jt(t,e){return e&&(xe(e)?Array.prototype.push.apply(t,e):t.push(e)),t}function Lt(t){return(typeof t=="string"||t instanceof String)&&t.indexOf(`
`)>-1?t.split(`
`):t}function MS(t,e){const{element:n,datasetIndex:r,index:i}=e,s=t.getDatasetMeta(r).controller,{label:a,value:o}=s.getLabelAndValue(i);return{chart:t,label:a,parsed:s.getParsed(i),raw:t.data.datasets[r].data[i],formattedValue:o,dataset:s.getDataset(),dataIndex:i,datasetIndex:r,element:n}}function Gh(t,e){const n=t.chart.ctx,{body:r,footer:i,title:s}=t,{boxWidth:a,boxHeight:o}=e,l=Me(e.bodyFont),u=Me(e.titleFont),d=Me(e.footerFont),h=s.length,f=i.length,m=r.length,x=ut(e.padding);let v=x.height,w=0,g=r.reduce((b,S)=>b+S.before.length+S.lines.length+S.after.length,0);if(g+=t.beforeBody.length+t.afterBody.length,h&&(v+=h*u.lineHeight+(h-1)*e.titleSpacing+e.titleMarginBottom),g){const b=e.displayColors?Math.max(o,l.lineHeight):l.lineHeight;v+=m*b+(g-m)*l.lineHeight+(g-1)*e.bodySpacing}f&&(v+=e.footerMarginTop+f*d.lineHeight+(f-1)*e.footerSpacing);let p=0;const y=function(b){w=Math.max(w,n.measureText(b).width+p)};return n.save(),n.font=u.string,Q(t.title,y),n.font=l.string,Q(t.beforeBody.concat(t.afterBody),y),p=e.displayColors?a+2+e.boxPadding:0,Q(r,b=>{Q(b.before,y),Q(b.lines,y),Q(b.after,y)}),p=0,n.font=d.string,Q(t.footer,y),n.restore(),w+=x.width,{width:w,height:v}}function $S(t,e){const{y:n,height:r}=e;return n<r/2?"top":n>t.height-r/2?"bottom":"center"}function RS(t,e,n,r){const{x:i,width:s}=r,a=n.caretSize+n.caretPadding;if(t==="left"&&i+s+a>e.width||t==="right"&&i-s-a<0)return!0}function DS(t,e,n,r){const{x:i,width:s}=n,{width:a,chartArea:{left:o,right:l}}=t;let u="center";return r==="center"?u=i<=(o+l)/2?"left":"right":i<=s/2?u="left":i>=a-s/2&&(u="right"),RS(u,t,e,n)&&(u="center"),u}function qh(t,e,n){const r=n.yAlign||e.yAlign||$S(t,n);return{xAlign:n.xAlign||e.xAlign||DS(t,e,n,r),yAlign:r}}function OS(t,e){let{x:n,width:r}=t;return e==="right"?n-=r:e==="center"&&(n-=r/2),n}function LS(t,e,n){let{y:r,height:i}=t;return e==="top"?r+=n:e==="bottom"?r-=i+n:r-=i/2,r}function Zh(t,e,n,r){const{caretSize:i,caretPadding:s,cornerRadius:a}=t,{xAlign:o,yAlign:l}=n,u=i+s,{topLeft:d,topRight:h,bottomLeft:f,bottomRight:m}=jr(a);let x=OS(e,o);const v=LS(e,l,u);return l==="center"?o==="left"?x+=u:o==="right"&&(x-=u):o==="left"?x-=Math.max(d,f)+i:o==="right"&&(x+=Math.max(h,m)+i),{x:We(x,0,r.width-e.width),y:We(v,0,r.height-e.height)}}function Fs(t,e,n){const r=ut(n.padding);return e==="center"?t.x+t.width/2:e==="right"?t.x+t.width-r.right:t.x+r.left}function Jh(t){return jt([],Lt(t))}function AS(t,e,n){return Vr(t,{tooltip:e,tooltipItems:n,type:"tooltip"})}function ef(t,e){const n=e&&e.dataset&&e.dataset.tooltip&&e.dataset.tooltip.callbacks;return n?t.override(n):t}const Lg={beforeTitle:Dt,title(t){if(t.length>0){const e=t[0],n=e.chart.data.labels,r=n?n.length:0;if(this&&this.options&&this.options.mode==="dataset")return e.dataset.label||"";if(e.label)return e.label;if(r>0&&e.dataIndex<r)return n[e.dataIndex]}return""},afterTitle:Dt,beforeBody:Dt,beforeLabel:Dt,label(t){if(this&&this.options&&this.options.mode==="dataset")return t.label+": "+t.formattedValue||t.formattedValue;let e=t.dataset.label||"";e&&(e+=": ");const n=t.formattedValue;return q(n)||(e+=n),e},labelColor(t){const n=t.chart.getDatasetMeta(t.datasetIndex).controller.getStyle(t.dataIndex);return{borderColor:n.borderColor,backgroundColor:n.backgroundColor,borderWidth:n.borderWidth,borderDash:n.borderDash,borderDashOffset:n.borderDashOffset,borderRadius:0}},labelTextColor(){return this.options.bodyColor},labelPointStyle(t){const n=t.chart.getDatasetMeta(t.datasetIndex).controller.getStyle(t.dataIndex);return{pointStyle:n.pointStyle,rotation:n.rotation}},afterLabel:Dt,afterBody:Dt,beforeFooter:Dt,footer:Dt,afterFooter:Dt};function Ie(t,e,n,r){const i=t[e].call(n,r);return typeof i>"u"?Lg[e].call(n,r):i}class ac extends Kt{constructor(e){super(),this.opacity=0,this._active=[],this._eventPosition=void 0,this._size=void 0,this._cachedAnimations=void 0,this._tooltipItems=[],this.$animations=void 0,this.$context=void 0,this.chart=e.chart,this.options=e.options,this.dataPoints=void 0,this.title=void 0,this.beforeBody=void 0,this.body=void 0,this.afterBody=void 0,this.footer=void 0,this.xAlign=void 0,this.yAlign=void 0,this.x=void 0,this.y=void 0,this.height=void 0,this.width=void 0,this.caretX=void 0,this.caretY=void 0,this.labelColors=void 0,this.labelPointStyles=void 0,this.labelTextColors=void 0}initialize(e){this.options=e,this._cachedAnimations=void 0,this.$context=void 0}_resolveAnimations(){const e=this._cachedAnimations;if(e)return e;const n=this.chart,r=this.options.setContext(this.getContext()),i=r.enabled&&n.options.animation&&r.animations,s=new bg(this.chart,i);return i._cacheable&&(this._cachedAnimations=Object.freeze(s)),s}getContext(){return this.$context||(this.$context=AS(this.chart.getContext(),this,this._tooltipItems))}getTitle(e,n){const{callbacks:r}=n,i=Ie(r,"beforeTitle",this,e),s=Ie(r,"title",this,e),a=Ie(r,"afterTitle",this,e);let o=[];return o=jt(o,Lt(i)),o=jt(o,Lt(s)),o=jt(o,Lt(a)),o}getBeforeBody(e,n){return Jh(Ie(n.callbacks,"beforeBody",this,e))}getBody(e,n){const{callbacks:r}=n,i=[];return Q(e,s=>{const a={before:[],lines:[],after:[]},o=ef(r,s);jt(a.before,Lt(Ie(o,"beforeLabel",this,s))),jt(a.lines,Ie(o,"label",this,s)),jt(a.after,Lt(Ie(o,"afterLabel",this,s))),i.push(a)}),i}getAfterBody(e,n){return Jh(Ie(n.callbacks,"afterBody",this,e))}getFooter(e,n){const{callbacks:r}=n,i=Ie(r,"beforeFooter",this,e),s=Ie(r,"footer",this,e),a=Ie(r,"afterFooter",this,e);let o=[];return o=jt(o,Lt(i)),o=jt(o,Lt(s)),o=jt(o,Lt(a)),o}_createItems(e){const n=this._active,r=this.chart.data,i=[],s=[],a=[];let o=[],l,u;for(l=0,u=n.length;l<u;++l)o.push(MS(this.chart,n[l]));return e.filter&&(o=o.filter((d,h,f)=>e.filter(d,h,f,r))),e.itemSort&&(o=o.sort((d,h)=>e.itemSort(d,h,r))),Q(o,d=>{const h=ef(e.callbacks,d);i.push(Ie(h,"labelColor",this,d)),s.push(Ie(h,"labelPointStyle",this,d)),a.push(Ie(h,"labelTextColor",this,d))}),this.labelColors=i,this.labelPointStyles=s,this.labelTextColors=a,this.dataPoints=o,o}update(e,n){const r=this.options.setContext(this.getContext()),i=this._active;let s,a=[];if(!i.length)this.opacity!==0&&(s={opacity:0});else{const o=gi[r.position].call(this,i,this._eventPosition);a=this._createItems(r),this.title=this.getTitle(a,r),this.beforeBody=this.getBeforeBody(a,r),this.body=this.getBody(a,r),this.afterBody=this.getAfterBody(a,r),this.footer=this.getFooter(a,r);const l=this._size=Gh(this,r),u=Object.assign({},o,l),d=qh(this.chart,r,u),h=Zh(r,u,d,this.chart);this.xAlign=d.xAlign,this.yAlign=d.yAlign,s={opacity:1,x:h.x,y:h.y,width:l.width,height:l.height,caretX:o.x,caretY:o.y}}this._tooltipItems=a,this.$context=void 0,s&&this._resolveAnimations().update(this,s),e&&r.external&&r.external.call(this,{chart:this.chart,tooltip:this,replay:n})}drawCaret(e,n,r,i){const s=this.getCaretPosition(e,r,i);n.lineTo(s.x1,s.y1),n.lineTo(s.x2,s.y2),n.lineTo(s.x3,s.y3)}getCaretPosition(e,n,r){const{xAlign:i,yAlign:s}=this,{caretSize:a,cornerRadius:o}=r,{topLeft:l,topRight:u,bottomLeft:d,bottomRight:h}=jr(o),{x:f,y:m}=e,{width:x,height:v}=n;let w,g,p,y,b,S;return s==="center"?(b=m+v/2,i==="left"?(w=f,g=w-a,y=b+a,S=b-a):(w=f+x,g=w+a,y=b-a,S=b+a),p=w):(i==="left"?g=f+Math.max(l,d)+a:i==="right"?g=f+x-Math.max(u,h)-a:g=this.caretX,s==="top"?(y=m,b=y-a,w=g-a,p=g+a):(y=m+v,b=y+a,w=g+a,p=g-a),S=y),{x1:w,x2:g,x3:p,y1:y,y2:b,y3:S}}drawTitle(e,n,r){const i=this.title,s=i.length;let a,o,l;if(s){const u=Cr(r.rtl,this.x,this.width);for(e.x=Fs(this,r.titleAlign,r),n.textAlign=u.textAlign(r.titleAlign),n.textBaseline="middle",a=Me(r.titleFont),o=r.titleSpacing,n.fillStyle=r.titleColor,n.font=a.string,l=0;l<s;++l)n.fillText(i[l],u.x(e.x),e.y+a.lineHeight/2),e.y+=a.lineHeight+o,l+1===s&&(e.y+=r.titleMarginBottom-o)}}_drawColorBox(e,n,r,i,s){const a=this.labelColors[r],o=this.labelPointStyles[r],{boxHeight:l,boxWidth:u}=s,d=Me(s.bodyFont),h=Fs(this,"left",s),f=i.x(h),m=l<d.lineHeight?(d.lineHeight-l)/2:0,x=n.y+m;if(s.usePointStyle){const v={radius:Math.min(u,l)/2,pointStyle:o.pointStyle,rotation:o.rotation,borderWidth:1},w=i.leftForLtr(f,u)+u/2,g=x+l/2;e.strokeStyle=s.multiKeyBackground,e.fillStyle=s.multiKeyBackground,ph(e,v,w,g),e.strokeStyle=a.borderColor,e.fillStyle=a.backgroundColor,ph(e,v,w,g)}else{e.lineWidth=V(a.borderWidth)?Math.max(...Object.values(a.borderWidth)):a.borderWidth||1,e.strokeStyle=a.borderColor,e.setLineDash(a.borderDash||[]),e.lineDashOffset=a.borderDashOffset||0;const v=i.leftForLtr(f,u),w=i.leftForLtr(i.xPlus(f,1),u-2),g=jr(a.borderRadius);Object.values(g).some(p=>p!==0)?(e.beginPath(),e.fillStyle=s.multiKeyBackground,za(e,{x:v,y:x,w:u,h:l,radius:g}),e.fill(),e.stroke(),e.fillStyle=a.backgroundColor,e.beginPath(),za(e,{x:w,y:x+1,w:u-2,h:l-2,radius:g}),e.fill()):(e.fillStyle=s.multiKeyBackground,e.fillRect(v,x,u,l),e.strokeRect(v,x,u,l),e.fillStyle=a.backgroundColor,e.fillRect(w,x+1,u-2,l-2))}e.fillStyle=this.labelTextColors[r]}drawBody(e,n,r){const{body:i}=this,{bodySpacing:s,bodyAlign:a,displayColors:o,boxHeight:l,boxWidth:u,boxPadding:d}=r,h=Me(r.bodyFont);let f=h.lineHeight,m=0;const x=Cr(r.rtl,this.x,this.width),v=function(k){n.fillText(k,x.x(e.x+m),e.y+f/2),e.y+=f+s},w=x.textAlign(a);let g,p,y,b,S,j,N;for(n.textAlign=a,n.textBaseline="middle",n.font=h.string,e.x=Fs(this,w,r),n.fillStyle=r.bodyColor,Q(this.beforeBody,v),m=o&&w!=="right"?a==="center"?u/2+d:u+2+d:0,b=0,j=i.length;b<j;++b){for(g=i[b],p=this.labelTextColors[b],n.fillStyle=p,Q(g.before,v),y=g.lines,o&&y.length&&(this._drawColorBox(n,e,b,x,r),f=Math.max(h.lineHeight,l)),S=0,N=y.length;S<N;++S)v(y[S]),f=h.lineHeight;Q(g.after,v)}m=0,f=h.lineHeight,Q(this.afterBody,v),e.y-=s}drawFooter(e,n,r){const i=this.footer,s=i.length;let a,o;if(s){const l=Cr(r.rtl,this.x,this.width);for(e.x=Fs(this,r.footerAlign,r),e.y+=r.footerMarginTop,n.textAlign=l.textAlign(r.footerAlign),n.textBaseline="middle",a=Me(r.footerFont),n.fillStyle=r.footerColor,n.font=a.string,o=0;o<s;++o)n.fillText(i[o],l.x(e.x),e.y+a.lineHeight/2),e.y+=a.lineHeight+r.footerSpacing}}drawBackground(e,n,r,i){const{xAlign:s,yAlign:a}=this,{x:o,y:l}=e,{width:u,height:d}=r,{topLeft:h,topRight:f,bottomLeft:m,bottomRight:x}=jr(i.cornerRadius);n.fillStyle=i.backgroundColor,n.strokeStyle=i.borderColor,n.lineWidth=i.borderWidth,n.beginPath(),n.moveTo(o+h,l),a==="top"&&this.drawCaret(e,n,r,i),n.lineTo(o+u-f,l),n.quadraticCurveTo(o+u,l,o+u,l+f),a==="center"&&s==="right"&&this.drawCaret(e,n,r,i),n.lineTo(o+u,l+d-x),n.quadraticCurveTo(o+u,l+d,o+u-x,l+d),a==="bottom"&&this.drawCaret(e,n,r,i),n.lineTo(o+m,l+d),n.quadraticCurveTo(o,l+d,o,l+d-m),a==="center"&&s==="left"&&this.drawCaret(e,n,r,i),n.lineTo(o,l+h),n.quadraticCurveTo(o,l,o+h,l),n.closePath(),n.fill(),i.borderWidth>0&&n.stroke()}_updateAnimationTarget(e){const n=this.chart,r=this.$animations,i=r&&r.x,s=r&&r.y;if(i||s){const a=gi[e.position].call(this,this._active,this._eventPosition);if(!a)return;const o=this._size=Gh(this,e),l=Object.assign({},a,this._size),u=qh(n,e,l),d=Zh(e,l,u,n);(i._to!==d.x||s._to!==d.y)&&(this.xAlign=u.xAlign,this.yAlign=u.yAlign,this.width=o.width,this.height=o.height,this.caretX=a.x,this.caretY=a.y,this._resolveAnimations().update(this,d))}}_willRender(){return!!this.opacity}draw(e){const n=this.options.setContext(this.getContext());let r=this.opacity;if(!r)return;this._updateAnimationTarget(n);const i={width:this.width,height:this.height},s={x:this.x,y:this.y};r=Math.abs(r)<.001?0:r;const a=ut(n.padding),o=this.title.length||this.beforeBody.length||this.body.length||this.afterBody.length||this.footer.length;n.enabled&&o&&(e.save(),e.globalAlpha=r,this.drawBackground(s,e,i,n),vg(e,n.textDirection),s.y+=a.top,this.drawTitle(s,e,n),this.drawBody(s,e,n),this.drawFooter(s,e,n),yg(e,n.textDirection),e.restore())}getActiveElements(){return this._active||[]}setActiveElements(e,n){const r=this._active,i=e.map(({datasetIndex:o,index:l})=>{const u=this.chart.getDatasetMeta(o);if(!u)throw new Error("Cannot find a dataset at index "+o);return{datasetIndex:o,element:u.data[l],index:l}}),s=!Da(r,i),a=this._positionChanged(i,n);(s||a)&&(this._active=i,this._eventPosition=n,this._ignoreReplayEvents=!0,this.update(!0))}handleEvent(e,n,r=!0){if(n&&this._ignoreReplayEvents)return!1;this._ignoreReplayEvents=!1;const i=this.options,s=this._active||[],a=this._getActiveElements(e,s,n,r),o=this._positionChanged(a,e),l=n||!Da(a,s)||o;return l&&(this._active=a,(i.enabled||i.external)&&(this._eventPosition={x:e.x,y:e.y},this.update(!0,n))),l}_getActiveElements(e,n,r,i){const s=this.options;if(e.type==="mouseout")return[];if(!i)return n.filter(o=>this.chart.data.datasets[o.datasetIndex]&&this.chart.getDatasetMeta(o.datasetIndex).controller.getParsed(o.index)!==void 0);const a=this.chart.getElementsAtEventForMode(e,s.mode,s,r);return s.reverse&&a.reverse(),a}_positionChanged(e,n){const{caretX:r,caretY:i,options:s}=this,a=gi[s.position].call(this,e,n);return a!==!1&&(r!==a.x||i!==a.y)}}F(ac,"positioners",gi);var Ag={id:"tooltip",_element:ac,positioners:gi,afterInit(t,e,n){n&&(t.tooltip=new ac({chart:t,options:n}))},beforeUpdate(t,e,n){t.tooltip&&t.tooltip.initialize(n)},reset(t,e,n){t.tooltip&&t.tooltip.initialize(n)},afterDraw(t){const e=t.tooltip;if(e&&e._willRender()){const n={tooltip:e};if(t.notifyPlugins("beforeTooltipDraw",{...n,cancelable:!0})===!1)return;e.draw(t.ctx),t.notifyPlugins("afterTooltipDraw",n)}},afterEvent(t,e){if(t.tooltip){const n=e.replay;t.tooltip.handleEvent(e.event,n,e.inChartArea)&&(e.changed=!0)}},defaults:{enabled:!0,external:null,position:"average",backgroundColor:"rgba(0,0,0,0.8)",titleColor:"#fff",titleFont:{weight:"bold"},titleSpacing:2,titleMarginBottom:6,titleAlign:"left",bodyColor:"#fff",bodySpacing:2,bodyFont:{},bodyAlign:"left",footerColor:"#fff",footerSpacing:2,footerMarginTop:6,footerFont:{weight:"bold"},footerAlign:"left",padding:6,caretPadding:2,caretSize:5,cornerRadius:6,boxHeight:(t,e)=>e.bodyFont.size,boxWidth:(t,e)=>e.bodyFont.size,multiKeyBackground:"#fff",displayColors:!0,boxPadding:0,borderColor:"rgba(0,0,0,0)",borderWidth:0,animation:{duration:400,easing:"easeOutQuart"},animations:{numbers:{type:"number",properties:["x","y","width","height","caretX","caretY"]},opacity:{easing:"linear",duration:200}},callbacks:Lg},defaultRoutes:{bodyFont:"font",footerFont:"font",titleFont:"font"},descriptors:{_scriptable:t=>t!=="filter"&&t!=="itemSort"&&t!=="external",_indexable:!1,callbacks:{_scriptable:!1,_indexable:!1},animation:{_fallback:!1},animations:{_fallback:"animation"}},additionalOptionScopes:["interaction"]};const FS=(t,e,n,r)=>(typeof e=="string"?(n=t.push(e)-1,r.unshift({index:n,label:e})):isNaN(e)&&(n=null),n);function zS(t,e,n,r){const i=t.indexOf(e);if(i===-1)return FS(t,e,n,r);const s=t.lastIndexOf(e);return i!==s?n:i}const IS=(t,e)=>t===null?null:We(Math.round(t),0,e);function tf(t){const e=this.getLabels();return t>=0&&t<e.length?e[t]:t}class oc extends Yr{constructor(e){super(e),this._startValue=void 0,this._valueRange=0,this._addedLabels=[]}init(e){const n=this._addedLabels;if(n.length){const r=this.getLabels();for(const{index:i,label:s}of n)r[i]===s&&r.splice(i,1);this._addedLabels=[]}super.init(e)}parse(e,n){if(q(e))return null;const r=this.getLabels();return n=isFinite(n)&&r[n]===e?n:zS(r,e,X(n,e),this._addedLabels),IS(n,r.length-1)}determineDataLimits(){const{minDefined:e,maxDefined:n}=this.getUserBounds();let{min:r,max:i}=this.getMinMax(!0);this.options.bounds==="ticks"&&(e||(r=0),n||(i=this.getLabels().length-1)),this.min=r,this.max=i}buildTicks(){const e=this.min,n=this.max,r=this.options.offset,i=[];let s=this.getLabels();s=e===0&&n===s.length-1?s:s.slice(e,n+1),this._valueRange=Math.max(s.length-(r?0:1),1),this._startValue=this.min-(r?.5:0);for(let a=e;a<=n;a++)i.push({value:a});return i}getLabelForValue(e){return tf.call(this,e)}configure(){super.configure(),this.isHorizontal()||(this._reversePixels=!this._reversePixels)}getPixelForValue(e){return typeof e!="number"&&(e=this.parse(e)),e===null?NaN:this.getPixelForDecimal((e-this._startValue)/this._valueRange)}getPixelForTick(e){const n=this.ticks;return e<0||e>n.length-1?null:this.getPixelForValue(n[e].value)}getValueForPixel(e){return Math.round(this._startValue+this.getDecimalForPixel(e)*this._valueRange)}getBasePixel(){return this.bottom}}F(oc,"id","category"),F(oc,"defaults",{ticks:{callback:tf}});function US(t,e){const n=[],{bounds:i,step:s,min:a,max:o,precision:l,count:u,maxTicks:d,maxDigits:h,includeBounds:f}=t,m=s||1,x=d-1,{min:v,max:w}=e,g=!q(a),p=!q(o),y=!q(u),b=(w-v)/(h+1);let S=ah((w-v)/x/m)*m,j,N,k,C;if(S<1e-14&&!g&&!p)return[{value:v},{value:w}];C=Math.ceil(w/S)-Math.floor(v/S),C>x&&(S=ah(C*S/x/m)*m),q(l)||(j=Math.pow(10,l),S=Math.ceil(S*j)/j),i==="ticks"?(N=Math.floor(v/S)*S,k=Math.ceil(w/S)*S):(N=v,k=w),g&&p&&s&&I1((o-a)/s,S/1e3)?(C=Math.round(Math.min((o-a)/S,d)),S=(o-a)/C,N=a,k=o):y?(N=g?a:N,k=p?o:k,C=u-1,S=(k-N)/C):(C=(k-N)/S,Zs(C,Math.round(C),S/1e3)?C=Math.round(C):C=Math.ceil(C));const P=Math.max(oh(S),oh(N));j=Math.pow(10,q(l)?P:l),N=Math.round(N*j)/j,k=Math.round(k*j)/j;let M=0;for(g&&(f&&N!==a?(n.push({value:a}),N<a&&M++,Zs(Math.round((N+M*S)*j)/j,a,nf(a,b,t))&&M++):N<a&&M++);M<C;++M){const D=Math.round((N+M*S)*j)/j;if(p&&D>o)break;n.push({value:D})}return p&&f&&k!==o?n.length&&Zs(n[n.length-1].value,o,nf(o,b,t))?n[n.length-1].value=o:n.push({value:o}):(!p||k===o)&&n.push({value:k}),n}function nf(t,e,{horizontal:n,minRotation:r}){const i=Bt(r),s=(n?Math.sin(i):Math.cos(i))||.001,a=.75*e*(""+t).length;return Math.min(e/s,a)}class BS extends Yr{constructor(e){super(e),this.start=void 0,this.end=void 0,this._startValue=void 0,this._endValue=void 0,this._valueRange=0}parse(e,n){return q(e)||(typeof e=="number"||e instanceof Number)&&!isFinite(+e)?null:+e}handleTickRangeOptions(){const{beginAtZero:e}=this.options,{minDefined:n,maxDefined:r}=this.getUserBounds();let{min:i,max:s}=this;const a=l=>i=n?i:l,o=l=>s=r?s:l;if(e){const l=bn(i),u=bn(s);l<0&&u<0?o(0):l>0&&u>0&&a(0)}if(i===s){let l=s===0?1:Math.abs(s*.05);o(s+l),e||a(i-l)}this.min=i,this.max=s}getTickLimit(){const e=this.options.ticks;let{maxTicksLimit:n,stepSize:r}=e,i;return r?(i=Math.ceil(this.max/r)-Math.floor(this.min/r)+1,i>1e3&&(console.warn(`scales.${this.id}.ticks.stepSize: ${r} would result generating up to ${i} ticks. Limiting to 1000.`),i=1e3)):(i=this.computeTickLimit(),n=n||11),n&&(i=Math.min(n,i)),i}computeTickLimit(){return Number.POSITIVE_INFINITY}buildTicks(){const e=this.options,n=e.ticks;let r=this.getTickLimit();r=Math.max(2,r);const i={maxTicks:r,bounds:e.bounds,min:e.min,max:e.max,precision:n.precision,step:n.stepSize,count:n.count,maxDigits:this._maxDigits(),horizontal:this.isHorizontal(),minRotation:n.minRotation||0,includeBounds:n.includeBounds!==!1},s=this._range||this,a=US(i,s);return e.bounds==="ticks"&&U1(a,this,"value"),e.reverse?(a.reverse(),this.start=this.max,this.end=this.min):(this.start=this.min,this.end=this.max),a}configure(){const e=this.ticks;let n=this.min,r=this.max;if(super.configure(),this.options.offset&&e.length){const i=(r-n)/Math.max(e.length-1,1)/2;n-=i,r+=i}this._startValue=n,this._endValue=r,this._valueRange=r-n}getLabelForValue(e){return hu(e,this.chart.options.locale,this.options.ticks.format)}}class lc extends BS{determineDataLimits(){const{min:e,max:n}=this.getMinMax(!0);this.min=ct(e)?e:0,this.max=ct(n)?n:1,this.handleTickRangeOptions()}computeTickLimit(){const e=this.isHorizontal(),n=e?this.width:this.height,r=Bt(this.options.ticks.minRotation),i=(e?Math.sin(r):Math.cos(r))||.001,s=this._resolveTickFontOptions(0);return Math.ceil(n/Math.min(40,s.lineHeight/i))}getPixelForValue(e){return e===null?NaN:this.getPixelForDecimal((e-this._startValue)/this._valueRange)}getValueForPixel(e){return this._startValue+this.getDecimalForPixel(e)*this._valueRange}}F(lc,"id","linear"),F(lc,"defaults",{ticks:{callback:ug.formatters.numeric}});const fo={millisecond:{common:!0,size:1,steps:1e3},second:{common:!0,size:1e3,steps:60},minute:{common:!0,size:6e4,steps:60},hour:{common:!0,size:36e5,steps:24},day:{common:!0,size:864e5,steps:30},week:{common:!1,size:6048e5,steps:4},month:{common:!0,size:2628e6,steps:12},quarter:{common:!1,size:7884e6,steps:4},year:{common:!0,size:3154e7}},Be=Object.keys(fo);function rf(t,e){return t-e}function sf(t,e){if(q(e))return null;const n=t._adapter,{parser:r,round:i,isoWeekday:s}=t._parseOpts;let a=e;return typeof r=="function"&&(a=r(a)),ct(a)||(a=typeof r=="string"?n.parse(a,r):n.parse(a)),a===null?null:(i&&(a=i==="week"&&(Aa(s)||s===!0)?n.startOf(a,"isoWeek",s):n.startOf(a,i)),+a)}function af(t,e,n,r){const i=Be.length;for(let s=Be.indexOf(t);s<i-1;++s){const a=fo[Be[s]],o=a.steps?a.steps:Number.MAX_SAFE_INTEGER;if(a.common&&Math.ceil((n-e)/(o*a.size))<=r)return Be[s]}return Be[i-1]}function HS(t,e,n,r,i){for(let s=Be.length-1;s>=Be.indexOf(n);s--){const a=Be[s];if(fo[a].common&&t._adapter.diff(i,r,a)>=e-1)return a}return Be[n?Be.indexOf(n):0]}function WS(t){for(let e=Be.indexOf(t)+1,n=Be.length;e<n;++e)if(fo[Be[e]].common)return Be[e]}function of(t,e,n){if(!n)t[e]=!0;else if(n.length){const{lo:r,hi:i}=uu(n,e),s=n[r]>=e?n[r]:n[i];t[s]=!0}}function VS(t,e,n,r){const i=t._adapter,s=+i.startOf(e[0].value,r),a=e[e.length-1].value;let o,l;for(o=s;o<=a;o=+i.add(o,1,r))l=n[o],l>=0&&(e[l].major=!0);return e}function lf(t,e,n){const r=[],i={},s=e.length;let a,o;for(a=0;a<s;++a)o=e[a],i[o]=a,r.push({value:o,major:!1});return s===0||!n?r:VS(t,r,i,n)}class Ha extends Yr{constructor(e){super(e),this._cache={data:[],labels:[],all:[]},this._unit="day",this._majorUnit=void 0,this._offsets={},this._normalized=!1,this._parseOpts=void 0}init(e,n={}){const r=e.time||(e.time={}),i=this._adapter=new s2._date(e.adapters.date);i.init(n),ji(r.displayFormats,i.formats()),this._parseOpts={parser:r.parser,round:r.round,isoWeekday:r.isoWeekday},super.init(e),this._normalized=n.normalized}parse(e,n){return e===void 0?null:sf(this,e)}beforeLayout(){super.beforeLayout(),this._cache={data:[],labels:[],all:[]}}determineDataLimits(){const e=this.options,n=this._adapter,r=e.time.unit||"day";let{min:i,max:s,minDefined:a,maxDefined:o}=this.getUserBounds();function l(u){!a&&!isNaN(u.min)&&(i=Math.min(i,u.min)),!o&&!isNaN(u.max)&&(s=Math.max(s,u.max))}(!a||!o)&&(l(this._getLabelBounds()),(e.bounds!=="ticks"||e.ticks.source!=="labels")&&l(this.getMinMax(!1))),i=ct(i)&&!isNaN(i)?i:+n.startOf(Date.now(),r),s=ct(s)&&!isNaN(s)?s:+n.endOf(Date.now(),r)+1,this.min=Math.min(i,s-1),this.max=Math.max(i+1,s)}_getLabelBounds(){const e=this.getLabelTimestamps();let n=Number.POSITIVE_INFINITY,r=Number.NEGATIVE_INFINITY;return e.length&&(n=e[0],r=e[e.length-1]),{min:n,max:r}}buildTicks(){const e=this.options,n=e.time,r=e.ticks,i=r.source==="labels"?this.getLabelTimestamps():this._generate();e.bounds==="ticks"&&i.length&&(this.min=this._userMin||i[0],this.max=this._userMax||i[i.length-1]);const s=this.min,a=this.max,o=Y1(i,s,a);return this._unit=n.unit||(r.autoSkip?af(n.minUnit,this.min,this.max,this._getLabelCapacity(s)):HS(this,o.length,n.minUnit,this.min,this.max)),this._majorUnit=!r.major.enabled||this._unit==="year"?void 0:WS(this._unit),this.initOffsets(i),e.reverse&&o.reverse(),lf(this,o,this._majorUnit)}afterAutoSkip(){this.options.offsetAfterAutoskip&&this.initOffsets(this.ticks.map(e=>+e.value))}initOffsets(e=[]){let n=0,r=0,i,s;this.options.offset&&e.length&&(i=this.getDecimalForValue(e[0]),e.length===1?n=1-i:n=(this.getDecimalForValue(e[1])-i)/2,s=this.getDecimalForValue(e[e.length-1]),e.length===1?r=s:r=(s-this.getDecimalForValue(e[e.length-2]))/2);const a=e.length<3?.5:.25;n=We(n,0,a),r=We(r,0,a),this._offsets={start:n,end:r,factor:1/(n+1+r)}}_generate(){const e=this._adapter,n=this.min,r=this.max,i=this.options,s=i.time,a=s.unit||af(s.minUnit,n,r,this._getLabelCapacity(n)),o=X(i.ticks.stepSize,1),l=a==="week"?s.isoWeekday:!1,u=Aa(l)||l===!0,d={};let h=n,f,m;if(u&&(h=+e.startOf(h,"isoWeek",l)),h=+e.startOf(h,u?"day":a),e.diff(r,n,a)>1e5*o)throw new Error(n+" and "+r+" are too far apart with stepSize of "+o+" "+a);const x=i.ticks.source==="data"&&this.getDataTimestamps();for(f=h,m=0;f<r;f=+e.add(f,o,a),m++)of(d,f,x);return(f===r||i.bounds==="ticks"||m===1)&&of(d,f,x),Object.keys(d).sort(rf).map(v=>+v)}getLabelForValue(e){const n=this._adapter,r=this.options.time;return r.tooltipFormat?n.format(e,r.tooltipFormat):n.format(e,r.displayFormats.datetime)}format(e,n){const i=this.options.time.displayFormats,s=this._unit,a=n||i[s];return this._adapter.format(e,a)}_tickFormatFunction(e,n,r,i){const s=this.options,a=s.ticks.callback;if(a)return ee(a,[e,n,r],this);const o=s.time.displayFormats,l=this._unit,u=this._majorUnit,d=l&&o[l],h=u&&o[u],f=r[n],m=u&&h&&f&&f.major;return this._adapter.format(e,i||(m?h:d))}generateTickLabels(e){let n,r,i;for(n=0,r=e.length;n<r;++n)i=e[n],i.label=this._tickFormatFunction(i.value,n,e)}getDecimalForValue(e){return e===null?NaN:(e-this.min)/(this.max-this.min)}getPixelForValue(e){const n=this._offsets,r=this.getDecimalForValue(e);return this.getPixelForDecimal((n.start+r)*n.factor)}getValueForPixel(e){const n=this._offsets,r=this.getDecimalForPixel(e)/n.factor-n.end;return this.min+r*(this.max-this.min)}_getLabelSize(e){const n=this.options.ticks,r=this.ctx.measureText(e).width,i=Bt(this.isHorizontal()?n.maxRotation:n.minRotation),s=Math.cos(i),a=Math.sin(i),o=this._resolveTickFontOptions(0).size;return{w:r*s+o*a,h:r*a+o*s}}_getLabelCapacity(e){const n=this.options.time,r=n.displayFormats,i=r[n.unit]||r.millisecond,s=this._tickFormatFunction(e,0,lf(this,[e],this._majorUnit),i),a=this._getLabelSize(s),o=Math.floor(this.isHorizontal()?this.width/a.w:this.height/a.h)-1;return o>0?o:1}getDataTimestamps(){let e=this._cache.data||[],n,r;if(e.length)return e;const i=this.getMatchingVisibleMetas();if(this._normalized&&i.length)return this._cache.data=i[0].controller.getAllParsedValues(this);for(n=0,r=i.length;n<r;++n)e=e.concat(i[n].controller.getAllParsedValues(this));return this._cache.data=this.normalize(e)}getLabelTimestamps(){const e=this._cache.labels||[];let n,r;if(e.length)return e;const i=this.getLabels();for(n=0,r=i.length;n<r;++n)e.push(sf(this,i[n]));return this._cache.labels=this._normalized?e:this.normalize(e)}normalize(e){return ag(e.sort(rf))}}F(Ha,"id","time"),F(Ha,"defaults",{bounds:"data",adapters:{},time:{parser:!1,unit:!1,round:!1,isoWeekday:!1,minUnit:"millisecond",displayFormats:{}},ticks:{source:"auto",callback:!1,major:{enabled:!1}}});function zs(t,e,n){let r=0,i=t.length-1,s,a,o,l;n?(e>=t[r].pos&&e<=t[i].pos&&({lo:r,hi:i}=nc(t,"pos",e)),{pos:s,time:o}=t[r],{pos:a,time:l}=t[i]):(e>=t[r].time&&e<=t[i].time&&({lo:r,hi:i}=nc(t,"time",e)),{time:s,pos:o}=t[r],{time:a,pos:l}=t[i]);const u=a-s;return u?o+(l-o)*(e-s)/u:o}class cf extends Ha{constructor(e){super(e),this._table=[],this._minPos=void 0,this._tableRange=void 0}initOffsets(){const e=this._getTimestampsForTable(),n=this._table=this.buildLookupTable(e);this._minPos=zs(n,this.min),this._tableRange=zs(n,this.max)-this._minPos,super.initOffsets(e)}buildLookupTable(e){const{min:n,max:r}=this,i=[],s=[];let a,o,l,u,d;for(a=0,o=e.length;a<o;++a)u=e[a],u>=n&&u<=r&&i.push(u);if(i.length<2)return[{time:n,pos:0},{time:r,pos:1}];for(a=0,o=i.length;a<o;++a)d=i[a+1],l=i[a-1],u=i[a],Math.round((d+l)/2)!==u&&s.push({time:u,pos:a/(o-1)});return s}_generate(){const e=this.min,n=this.max;let r=super.getDataTimestamps();return(!r.includes(e)||!r.length)&&r.splice(0,0,e),(!r.includes(n)||r.length===1)&&r.push(n),r.sort((i,s)=>i-s)}_getTimestampsForTable(){let e=this._cache.all||[];if(e.length)return e;const n=this.getDataTimestamps(),r=this.getLabelTimestamps();return n.length&&r.length?e=this.normalize(n.concat(r)):e=n.length?n:r,e=this._cache.all=e,e}getDecimalForValue(e){return(zs(this._table,e)-this._minPos)/this._tableRange}getValueForPixel(e){const n=this._offsets,r=this.getDecimalForPixel(e)/n.factor-n.end;return zs(this._table,r*this._tableRange+this._minPos,!0)}}F(cf,"id","timeseries"),F(cf,"defaults",Ha.defaults);const Fg="label";function uf(t,e){typeof t=="function"?t(e):t&&(t.current=e)}function YS(t,e){const n=t.options;n&&e&&Object.assign(n,e)}function zg(t,e){t.labels=e}function Ig(t,e){let n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:Fg;const r=[];t.datasets=e.map(i=>{const s=t.datasets.find(a=>a[n]===i[n]);return!s||!i.data||r.includes(s)?{...i}:(r.push(s),Object.assign(s,i),s)})}function XS(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:Fg;const n={labels:[],datasets:[]};return zg(n,t.labels),Ig(n,t.datasets,e),n}function KS(t,e){const{height:n=150,width:r=300,redraw:i=!1,datasetIdKey:s,type:a,data:o,options:l,plugins:u=[],fallbackContent:d,updateMode:h,...f}=t,m=_.useRef(null),x=_.useRef(null),v=()=>{m.current&&(x.current=new os(m.current,{type:a,data:XS(o,s),options:l&&{...l},plugins:u}),uf(e,x.current))},w=()=>{uf(e,null),x.current&&(x.current.destroy(),x.current=null)};return _.useEffect(()=>{!i&&x.current&&l&&YS(x.current,l)},[i,l]),_.useEffect(()=>{!i&&x.current&&zg(x.current.config.data,o.labels)},[i,o.labels]),_.useEffect(()=>{!i&&x.current&&o.datasets&&Ig(x.current.config.data,o.datasets,s)},[i,o.datasets]),_.useEffect(()=>{x.current&&(i?(w(),setTimeout(v)):x.current.update(h))},[i,l,o.labels,o.datasets,h]),_.useEffect(()=>{x.current&&(w(),setTimeout(v))},[a]),_.useEffect(()=>(v(),()=>w()),[]),bt.createElement("canvas",{ref:m,role:"img",height:n,width:r,...f},d)}const QS=_.forwardRef(KS);function Ug(t,e){return os.register(e),_.forwardRef((n,r)=>bt.createElement(QS,{...n,ref:r,type:t}))}const GS=Ug("bar",Js),qS=Ug("doughnut",fi);os.register(pi,Ag,Dg);function ZS(){const{state:t,usageSinceLastRecording:e,getDisplayUnitName:n}=Ke(),{theme:r}=ce(),i=_.useRef(null),s=t.currentUnits,a=e,o=s+a,l=o>0?a/o*100:0,u=()=>{const f=i.current;if(f&&f.ctx&&f.canvas){const m=f.ctx,x=f.canvas,v=x.width/2,w=x.height/2,g=Math.min(v,w)*.2,p=Math.min(v,w)*.8,y=m.createRadialGradient(v,w,g,v,w,p);y.addColorStop(0,"#667eea"),y.addColorStop(.3,"#764ba2"),y.addColorStop(.6,"#667eea"),y.addColorStop(1,"#f093fb");const b=m.createRadialGradient(v,w,g,v,w,p);b.addColorStop(0,"#ff9a9e"),b.addColorStop(.3,"#fecfef"),b.addColorStop(.6,"#fecfef"),b.addColorStop(1,"#ffc3a0"),f.data.datasets[0].backgroundColor=[y,b],f.update()}};_.useEffect(()=>{u()},[s,a]),_.useEffect(()=>{const f=setTimeout(()=>{u()},100);return()=>clearTimeout(f)},[]);const d={labels:[`Remaining ${n()}`,`Used ${n()}`],datasets:[{data:[s,a],backgroundColor:["linear-gradient(135deg, #667eea 0%, #764ba2 100%)","linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #ffc3a0 100%)"],borderColor:["rgba(255, 255, 255, 0.9)","rgba(255, 255, 255, 0.9)"],borderWidth:4,cutout:"78%",borderRadius:12,borderJoinStyle:"round",hoverBorderWidth:6,hoverBorderColor:["rgba(255, 255, 255, 1)","rgba(255, 255, 255, 1)"],shadowOffsetX:3,shadowOffsetY:3,shadowBlur:10,shadowColor:"rgba(0, 0, 0, 0.1)"}]},h={responsive:!0,maintainAspectRatio:!1,onResize:f=>{setTimeout(()=>u(),50)},plugins:{legend:{display:!1},tooltip:{backgroundColor:"rgba(0, 0, 0, 0.8)",titleColor:"#ffffff",bodyColor:"#ffffff",borderColor:"rgba(255, 255, 255, 0.2)",borderWidth:1,cornerRadius:8,displayColors:!0,callbacks:{label:function(f){const m=f.label||"",x=f.parsed,v=o>0?(x/o*100).toFixed(1):0;return`${m}: ${x.toFixed(2)} (${v}%)`}}}},animation:{animateRotate:!0,animateScale:!0,duration:2e3,easing:"easeInOutCubic",delay:f=>f.dataIndex*200,onComplete:()=>{setTimeout(()=>u(),100)}},interaction:{intersect:!1,mode:"nearest"},elements:{arc:{borderWidth:4,hoverBorderWidth:6,borderSkipped:!1,borderAlign:"inner"}},layout:{padding:{top:10,bottom:10,left:10,right:10}}};return c.jsxs("div",{className:"relative",children:[c.jsxs("div",{className:"relative h-[16rem] md:h-[20rem] lg:h-[24rem] mb-4 md:mb-6 flex items-center justify-center",children:[c.jsx("div",{className:`absolute top-3 md:top-6 left-3 md:left-6 w-8 md:w-12 h-8 md:h-12 bg-gradient-to-r ${r.gradient} rounded-full opacity-20 blur-xl animate-pulse`}),c.jsx("div",{className:`absolute bottom-3 md:bottom-6 right-3 md:right-6 w-10 md:w-16 h-10 md:h-16 bg-gradient-to-r ${r.gradient} rounded-full opacity-15 blur-2xl animate-pulse`,style:{animationDelay:"1s"}}),c.jsx("div",{className:`absolute top-1/2 left-2 md:left-4 w-10 md:w-16 h-10 md:h-16 bg-gradient-to-r ${r.gradient} rounded-full opacity-10 blur-lg animate-pulse`,style:{animationDelay:"2s"}}),c.jsx("div",{className:`absolute top-1/4 right-4 md:right-8 w-8 md:w-12 h-8 md:h-12 bg-gradient-to-r ${r.gradient} rounded-full opacity-25 blur-md animate-pulse`,style:{animationDelay:"0.5s"}}),c.jsxs("div",{className:"relative h-full w-full max-w-xs md:max-w-md lg:max-w-lg flex items-center justify-center p-4 md:p-6",children:[c.jsx(qS,{ref:i,data:d,options:h}),c.jsx("div",{className:"absolute inset-0 flex items-center justify-center",children:c.jsxs("div",{className:"relative",children:[c.jsx("div",{className:"absolute inset-0 w-40 h-40 md:w-56 lg:w-64 md:h-56 lg:h-64 bg-gradient-to-r from-indigo-400 via-purple-500 to-pink-500 rounded-full blur-2xl opacity-40 animate-pulse"}),c.jsxs("div",{className:`relative w-40 h-40 md:w-56 lg:w-64 md:h-56 lg:h-64 ${r.card}/50 border-2 md:border-4 ${r.border}/40 backdrop-blur-xl rounded-full shadow-2xl flex items-center justify-center`,children:[c.jsx("div",{className:`absolute inset-2 md:inset-3 ${r.secondary}/70 rounded-full`}),c.jsxs("div",{className:"relative text-center z-10",children:[c.jsx("div",{className:"mb-1 flex justify-center",children:c.jsxs("div",{className:"relative",children:[c.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full blur-sm opacity-75 animate-pulse"}),c.jsx(De,{className:"relative h-4 md:h-6 lg:h-8 w-4 md:w-6 lg:w-8 text-transparent bg-gradient-to-r from-yellow-500 to-orange-600 bg-clip-text animate-bounce",style:{animationDuration:"2s"}})]})}),c.jsxs("div",{className:"relative mb-1",children:[c.jsx("div",{className:`text-xl md:text-3xl lg:text-4xl font-black ${r.text} drop-shadow-lg`,children:s.toFixed(2)}),c.jsxs("div",{className:`text-xs md:text-sm lg:text-base font-bold ${r.textSecondary} mt-0.5 tracking-wide`,children:[n()," Left"]})]}),c.jsx("div",{className:"mt-0.5",children:c.jsxs("div",{className:`text-xs md:text-sm lg:text-base font-bold tracking-tight ${r.textSecondary} drop-shadow-lg`,children:[l.toFixed(1),"% Used"]})})]}),c.jsx("div",{className:"absolute top-6 right-6 w-3 h-3 bg-gradient-to-r from-blue-400 to-purple-500 rounded-full opacity-70 animate-pulse"}),c.jsx("div",{className:"absolute bottom-6 left-6 w-2 h-2 bg-gradient-to-r from-pink-400 to-orange-400 rounded-full opacity-60 animate-pulse",style:{animationDelay:"1s"}}),c.jsx("div",{className:"absolute top-1/4 right-8 w-1.5 h-1.5 bg-gradient-to-r from-green-400 to-blue-400 rounded-full opacity-50 animate-pulse",style:{animationDelay:"1.5s"}}),c.jsx("div",{className:"absolute bottom-1/4 left-8 w-1 h-1 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full opacity-40 animate-pulse",style:{animationDelay:"0.5s"}})]})]})})]})]}),c.jsxs("div",{className:"mt-4 space-y-3",children:[c.jsxs("div",{className:`relative overflow-hidden rounded-2xl ${r.card} border ${r.border} p-3 md:p-4 shadow-lg mobile-card-auto`,children:[c.jsx("div",{className:`absolute inset-0 ${r.secondary}`}),c.jsx("div",{className:"relative",children:c.jsxs("div",{className:"flex items-center justify-between",children:[c.jsxs("div",{className:"flex-1",children:[c.jsx("div",{className:`text-xs font-semibold ${r.textSecondary} tracking-wider uppercase mb-1`,children:"CURRENT UNITS"}),c.jsx("p",{className:`text-lg md:text-xl font-bold ${r.text} mb-1`,children:t.currentUnits.toFixed(2)}),c.jsxs("p",{className:`text-xs ${r.textSecondary}`,children:[n()," remaining"]}),c.jsxs("p",{className:`text-xs ${r.textSecondary}`,children:["Value: ",t.currencySymbol,(t.currentUnits*t.unitCost).toFixed(2)]})]}),c.jsx("div",{className:`p-2 md:p-3 rounded-xl bg-gradient-to-br ${r.gradient} shadow-lg ml-3`,children:c.jsx(De,{className:"h-5 md:h-6 w-5 md:w-6 text-white"})})]})}),c.jsx("div",{className:`absolute top-2 right-2 w-3 h-3 bg-gradient-to-r ${r.gradient} rounded-full opacity-15 blur-sm`}),c.jsx("div",{className:`absolute bottom-2 left-2 w-4 h-4 bg-gradient-to-r ${r.gradient} rounded-full opacity-20 blur-sm`})]}),t.usageHistory.length>0&&c.jsxs("div",{className:`relative overflow-hidden rounded-2xl ${r.card} border ${r.border} p-3 md:p-4 shadow-lg mobile-card-auto`,children:[c.jsx("div",{className:`absolute inset-0 ${r.secondary}`}),c.jsx("div",{className:"relative",children:c.jsxs("div",{className:"flex items-center justify-between",children:[c.jsxs("div",{className:"flex-1",children:[c.jsx("div",{className:`text-xs font-semibold ${r.textSecondary} tracking-wider uppercase mb-1`,children:"USAGE SINCE LAST RECORDING"}),c.jsx("p",{className:`text-lg md:text-xl font-bold ${r.text} mb-1`,children:e.toFixed(2)}),c.jsxs("p",{className:`text-xs ${r.textSecondary}`,children:[n()," used"]}),c.jsxs("p",{className:`text-xs ${r.textSecondary}`,children:["Cost: ",t.currencySymbol,(e*t.unitCost).toFixed(2)]})]}),c.jsx("div",{className:`p-2 md:p-3 rounded-xl bg-gradient-to-br ${r.gradient} shadow-lg ml-3`,children:c.jsx(yt,{className:"h-5 md:h-6 w-5 md:w-6 text-white"})})]})}),c.jsx("div",{className:`absolute top-2 right-2 w-4 h-4 bg-gradient-to-r ${r.gradient} rounded-full opacity-20 blur-sm`}),c.jsx("div",{className:`absolute bottom-2 left-2 w-3 h-3 bg-gradient-to-r ${r.gradient} rounded-full opacity-15 blur-sm`})]})]}),c.jsxs("div",{className:"mt-3 space-y-3",children:[t.usageHistory.length>0&&c.jsxs("div",{className:`relative overflow-hidden rounded-2xl ${r.card} border ${r.border} p-3 md:p-4 shadow-lg mobile-card-auto`,children:[c.jsx("div",{className:`absolute inset-0 ${r.secondary}`}),c.jsx("div",{className:"relative",children:c.jsxs("div",{className:"flex items-center justify-between",children:[c.jsxs("div",{className:"flex-1",children:[c.jsx("div",{className:`text-xs font-semibold ${r.textSecondary} tracking-wider uppercase mb-1`,children:"TOTAL COST"}),c.jsxs("div",{className:`text-lg md:text-xl font-black ${r.text} mb-1`,children:[t.currencySymbol||"R",(a*t.unitCost).toFixed(2)]}),c.jsxs("div",{className:`text-xs ${r.textSecondary}`,children:["For ",a.toFixed(2)," ",n()," used"]})]}),c.jsx("div",{className:`p-2 md:p-3 rounded-xl bg-gradient-to-br ${r.gradient} shadow-lg ml-3`,children:c.jsx(su,{className:"h-5 md:h-6 w-5 md:w-6 text-white"})})]})}),c.jsx("div",{className:`absolute top-2 right-2 w-4 h-4 bg-gradient-to-r ${r.gradient} rounded-full opacity-20 blur-sm`}),c.jsx("div",{className:`absolute bottom-2 left-2 w-3 h-3 bg-gradient-to-r ${r.gradient} rounded-full opacity-15 blur-sm`})]}),c.jsxs("div",{className:`relative overflow-hidden rounded-2xl ${r.card} border ${r.border} p-3 md:p-4 shadow-lg mobile-card-auto`,children:[c.jsx("div",{className:`absolute inset-0 ${r.secondary}`}),c.jsx("div",{className:"relative",children:c.jsxs("div",{className:"flex items-center justify-between",children:[c.jsxs("div",{className:"flex-1",children:[c.jsx("div",{className:`text-xs font-semibold ${r.textSecondary} tracking-wider uppercase mb-1`,children:"CURRENT RATE"}),c.jsxs("div",{className:`text-lg md:text-xl font-black ${r.text} mb-1`,children:[t.currencySymbol||"R",t.unitCost.toFixed(2)]}),c.jsxs("div",{className:`text-xs ${r.textSecondary}`,children:["Per ",n()]})]}),c.jsx("div",{className:`p-2 md:p-3 rounded-xl bg-gradient-to-br ${r.gradient} shadow-lg ml-3`,children:c.jsx(n1,{className:"h-5 md:h-6 w-5 md:w-6 text-white"})})]})}),c.jsx("div",{className:`absolute top-2 right-2 w-3 h-3 bg-gradient-to-r ${r.gradient} rounded-full opacity-15 blur-sm`}),c.jsx("div",{className:`absolute bottom-2 left-2 w-4 h-4 bg-gradient-to-r ${r.gradient} rounded-full opacity-20 blur-sm`})]})]}),c.jsxs("div",{className:"mt-6",children:[c.jsxs("div",{className:"flex justify-between items-center mb-3",children:[c.jsx("span",{className:`text-xs font-bold ${r.textSecondary} tracking-wide uppercase`,children:"Usage Progress"}),c.jsxs("span",{className:`text-sm font-black px-2 py-1 rounded-full ${r.secondary} ${r.text}`,children:[l.toFixed(1),"%"]})]}),c.jsx("div",{className:"relative",children:c.jsx("div",{className:`w-full ${r.secondary} rounded-full h-4 border ${r.border}`,children:c.jsx("div",{className:`h-4 rounded-full transition-all duration-1000 ease-out bg-gradient-to-r ${l>70?"from-red-500 to-red-600":l>40?"from-amber-500 to-orange-600":"from-emerald-500 to-green-600"}`,style:{width:`${Math.min(l,100)}%`}})})})]})]})}function JS(){const t=tr(),{state:e,getDisplayUnitName:n}=Ke(),{theme:r}=ce(),i=e.currentUnits,s=e.thresholdLimit;return c.jsx("div",{className:"bg-gradient-to-r from-amber-50 to-orange-50 border border-amber-200 rounded-xl p-6 shadow-lg",children:c.jsxs("div",{className:"flex items-start",children:[c.jsx("div",{className:"flex-shrink-0",children:c.jsx("div",{className:"p-2 bg-gradient-to-r from-amber-400 to-orange-500 rounded-lg",children:c.jsx(kr,{className:"h-6 w-6 text-white animate-pulse"})})}),c.jsxs("div",{className:"ml-4 flex-1",children:[c.jsxs("h3",{className:"text-xl font-bold text-amber-800 mb-2",children:["⚠️ Low ",n()," Warning!"]}),c.jsxs("div",{className:"text-sm text-amber-700 space-y-2",children:[c.jsxs("p",{className:"font-medium",children:["You have ",c.jsxs("strong",{className:"text-amber-900",children:[i.toFixed(2)," ",n()]})," remaining, which is below your threshold of ",c.jsxs("strong",{className:"text-amber-900",children:[s.toFixed(2)," ",n()]}),"."]}),c.jsxs("p",{children:["💡 ",c.jsx("strong",{children:"Time to top up!"})," Consider purchasing more ",n()," to avoid running out of power."]})]}),c.jsxs("div",{className:"mt-4 flex flex-wrap gap-3",children:[c.jsx("button",{onClick:()=>t("/purchases"),className:"bg-gradient-to-r from-emerald-500 to-green-600 text-white px-6 py-3 rounded-lg text-sm font-semibold hover:from-emerald-600 hover:to-green-700 transition-all duration-200 shadow-md hover:shadow-lg transform hover:scale-105",children:"🛒 Top Up Now"}),c.jsx("button",{onClick:()=>t("/settings"),className:"bg-white text-amber-700 border-2 border-amber-300 px-6 py-3 rounded-lg text-sm font-semibold hover:bg-amber-50 hover:border-amber-400 transition-all duration-200",children:"⚙️ Adjust Threshold"})]})]})]})})}function eN({message:t="Swipe to see more",onDismiss:e,position:n="top-right",autoHide:r=!0,autoHideDelay:i=5e3}){const{theme:s}=ce(),[a,o]=_.useState(!0);_.useEffect(()=>{if(r){const u=setTimeout(()=>{o(!1),e&&e()},i);return()=>clearTimeout(u)}},[r,i,e]);const l=()=>{switch(n){case"top-left":return"top-4 left-4";case"top-right":return"top-4 right-4";case"bottom-left":return"bottom-4 left-4";case"bottom-right":return"bottom-4 right-4";case"center":return"top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2";default:return"top-4 right-4"}};return a?c.jsxs("div",{className:`absolute ${l()} z-30 pointer-events-none`,children:[c.jsxs("div",{className:`flex items-center gap-2 ${s.primary} text-white px-4 py-3 rounded-full shadow-lg backdrop-blur-sm animate-pulse swipe-indicator`,children:[c.jsx("div",{className:"flex items-center gap-1",children:c.jsxs("div",{className:"flex space-x-1",children:[c.jsx("div",{className:"w-1 h-1 bg-white rounded-full animate-bounce"}),c.jsx("div",{className:"w-1 h-1 bg-white rounded-full animate-bounce",style:{animationDelay:"0.1s"}}),c.jsx("div",{className:"w-1 h-1 bg-white rounded-full animate-bounce",style:{animationDelay:"0.2s"}})]})}),c.jsx("span",{className:"text-sm font-medium whitespace-nowrap",children:t}),c.jsx("div",{className:"flex items-center",children:c.jsx("div",{className:"animate-pulse",children:c.jsx("svg",{className:"w-4 h-4 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:c.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})})})]}),e&&c.jsx("button",{onClick:()=>{o(!1),e()},className:"absolute -top-1 -right-1 w-5 h-5 bg-gray-600 text-white rounded-full flex items-center justify-center text-xs hover:bg-gray-700 transition-colors pointer-events-auto",children:"×"})]}):null}function Su({leftContent:t,rightContent:e,className:n="",rightContentTitle:r="Additional Info"}){const{theme:i}=ce(),[s,a]=_.useState("left"),[o,l]=_.useState(!0),[u,d]=_.useState(null),[h,f]=_.useState(null),[m,x]=_.useState(null),v=_.useRef(null),w=50;_.useEffect(()=>{const b=setTimeout(()=>{l(!1)},5e3);return()=>clearTimeout(b)},[]);const g=b=>{f(null),d(b.targetTouches[0].clientX),x(b.targetTouches[0].clientY)},p=b=>{const S=b.targetTouches[0];f(S.clientX);const j=Math.abs(S.clientX-u);Math.abs(S.clientY-(m||S.clientY))>j||j>10&&b.preventDefault()},y=()=>{if(!u||!h)return;const b=u-h,S=b>w,j=b<-50;S&&s==="left"?(a("right"),l(!1)):j&&s==="right"&&a("left")};return _.useEffect(()=>{const b=S=>{S.key==="Escape"&&s==="right"&&a("left")};return document.addEventListener("keydown",b),()=>document.removeEventListener("keydown",b)},[s]),c.jsxs("div",{className:`${n}`,children:[c.jsxs("div",{className:"hidden lg:grid lg:grid-cols-2 gap-6 min-h-full",children:[c.jsx("div",{children:t}),c.jsx("div",{children:e})]}),c.jsxs("div",{className:"lg:hidden relative min-h-full",children:[o&&s==="left"&&c.jsx(eN,{onDismiss:()=>l(!1),message:"Swipe left for more info"}),s==="right"&&c.jsx("div",{className:"absolute top-4 left-4 z-20",children:c.jsxs("button",{onClick:()=>a("left"),className:`flex items-center gap-2 ${i.primary} text-white px-3 py-2 rounded-full shadow-lg backdrop-blur-sm`,children:[c.jsx("span",{className:"text-sm",children:"←"}),c.jsx("span",{className:"text-xs font-medium",children:"Back"})]})}),c.jsx("div",{ref:v,className:"relative w-full swipeable-container",onTouchStart:g,onTouchMove:p,onTouchEnd:y,style:{touchAction:"pan-y"},children:c.jsxs("div",{className:`flex w-[200%] swipe-transition swipeable-content ${s==="right"?"-translate-x-1/2":"translate-x-0"}`,children:[c.jsx("div",{className:"w-1/2 pr-2",children:c.jsx("div",{className:"pb-20",children:t})}),c.jsx("div",{className:"w-1/2 pl-2",children:c.jsxs("div",{className:"pb-20",children:[c.jsx("div",{className:`${i.card} rounded-2xl shadow-lg p-4 border ${i.border} mb-4`,children:c.jsx("h3",{className:`text-lg font-semibold ${i.text} text-center`,children:r})}),e]})})]})}),c.jsxs("div",{className:"absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2 z-20",children:[c.jsx("div",{className:`w-2 h-2 rounded-full transition-colors ${s==="left"?i.primary:"bg-gray-400"}`}),c.jsx("div",{className:`w-2 h-2 rounded-full transition-colors ${s==="right"?i.primary:"bg-gray-400"}`})]})]})]})}function df(){const t=tr(),{state:e,isThresholdExceeded:n,getDisplayUnitName:r,weeklyPurchaseTotal:i,monthlyPurchaseTotal:s,weeklyUsageTotal:a,monthlyUsageTotal:o,usageSinceLastRecording:l}=Ke(),{theme:u,currentTheme:d}=ce(),h=c.jsxs("div",{className:"space-y-6",children:[c.jsx("div",{className:`${u.card} rounded-2xl shadow-lg p-4 md:p-6 lg:p-8 border ${u.border} w-full`,children:c.jsx(ZS,{})}),c.jsxs("div",{className:`${u.card} rounded-2xl shadow-lg p-6 lg:p-8 border ${u.border}`,children:[c.jsxs("h2",{className:`text-xl lg:text-2xl font-bold ${u.text} mb-6 flex items-center gap-3`,children:[c.jsx("div",{className:`p-3 lg:p-4 rounded-xl bg-gradient-to-br ${u.gradient} shadow-lg`,children:c.jsx(De,{className:"h-6 lg:h-7 w-6 lg:w-7 text-white"})}),"Quick Actions"]}),c.jsxs("div",{className:"space-y-3",children:[c.jsxs("button",{onClick:()=>t("/purchases"),className:`w-full flex items-center gap-3 p-3 ${u.primary} text-white rounded-xl hover:opacity-90 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98]`,children:[c.jsx(Te,{className:"h-5 w-5"}),c.jsxs("div",{className:"text-left",children:[c.jsx("span",{className:"block text-sm font-semibold",children:"Add Purchase"}),c.jsx("span",{className:"block text-xs opacity-80",children:"Top up your units"})]})]}),c.jsxs("button",{onClick:()=>t("/usage"),className:`w-full flex items-center gap-3 p-3 ${u.primary} text-white rounded-xl hover:opacity-90 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98]`,children:[c.jsx(yt,{className:"h-5 w-5"}),c.jsxs("div",{className:"text-left",children:[c.jsx("span",{className:"block text-sm font-semibold",children:"Record Usage"}),c.jsx("span",{className:"block text-xs opacity-80",children:"Track consumption"})]})]}),c.jsxs("button",{onClick:()=>t("/history"),className:`w-full flex items-center gap-3 p-3 ${u.primary} text-white rounded-xl hover:opacity-90 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98]`,children:[c.jsx(De,{className:"h-5 w-5"}),c.jsxs("div",{className:"text-left",children:[c.jsx("span",{className:"block text-sm font-semibold",children:"View History"}),c.jsx("span",{className:"block text-xs opacity-80",children:"See all records"})]})]})]})]})]}),f=c.jsxs("div",{className:`${u.card} rounded-2xl shadow-lg p-6 lg:p-8 border ${u.border} flex flex-col`,children:[c.jsxs("h2",{className:`text-xl lg:text-2xl font-bold ${u.text} mb-6 flex items-center gap-3`,children:[c.jsx("div",{className:`p-3 lg:p-4 rounded-xl bg-gradient-to-br ${u.gradient} shadow-lg`,children:c.jsx(yt,{className:"h-6 lg:h-7 w-6 lg:w-7 text-white"})}),"Recent Activity"]}),c.jsxs("div",{className:"space-y-3",children:[e.purchases.slice(0,2).map(m=>c.jsx("div",{className:`p-3 ${u.secondary} border ${u.border} rounded-xl shadow-sm hover:shadow-md transition-all duration-200`,children:c.jsxs("div",{className:"flex items-center justify-between",children:[c.jsxs("div",{className:"flex items-center",children:[c.jsx("div",{className:`p-2 rounded-lg ${u.accent} shadow-sm`,children:c.jsx(Te,{className:"h-4 w-4 text-white"})}),c.jsxs("div",{className:"ml-3",children:[c.jsxs("p",{className:`text-sm font-semibold ${u.text}`,children:["Purchase: ",e.currencySymbol||"R",m.currency.toFixed(2)]}),c.jsx("p",{className:`text-xs ${u.textSecondary}`,children:new Date(m.date).toLocaleDateString()})]})]}),c.jsxs("span",{className:`text-sm font-semibold ${u.text}`,children:["+",m.units.toFixed(2)," ",r()]})]})},m.id)),e.usageHistory.slice(0,1).map(m=>c.jsx("div",{className:`p-3 ${u.secondary} border ${u.border} rounded-xl shadow-sm hover:shadow-md transition-all duration-200`,children:c.jsxs("div",{className:"flex items-center justify-between",children:[c.jsxs("div",{className:"flex items-center",children:[c.jsx("div",{className:`p-2 rounded-lg ${u.accent} shadow-sm`,children:c.jsx(yt,{className:"h-4 w-4 text-white"})}),c.jsxs("div",{className:"ml-3",children:[c.jsx("p",{className:`text-sm font-semibold ${u.text}`,children:"Usage recorded"}),c.jsx("p",{className:`text-xs ${u.textSecondary}`,children:new Date(m.date).toLocaleDateString()})]})]}),c.jsxs("span",{className:`text-sm font-semibold ${u.text}`,children:["-",m.usage.toFixed(2)," ",r()]})]})},m.id)),e.purchases.length===0&&e.usageHistory.length===0&&c.jsxs("div",{className:`text-center py-8 ${u.secondary} border ${u.border} rounded-xl`,children:[c.jsx("div",{className:`p-3 rounded-2xl bg-gradient-to-br ${u.gradient} w-fit mx-auto mb-3`,children:c.jsx(De,{className:"h-8 w-8 text-white"})}),c.jsx("p",{className:`text-sm ${u.textSecondary} font-medium`,children:"No recent activity"}),c.jsx("p",{className:`text-xs ${u.textSecondary} mt-1`,children:"Start by making a purchase or recording usage"})]})]})]});return c.jsxs("div",{className:"w-full space-y-6 pb-6",children:[n&&c.jsx(JS,{}),c.jsx(Su,{leftContent:h,rightContent:f,rightContentTitle:"Recent Activity",className:""})]})}const zr=t=>{t.target.type==="number"&&t.preventDefault()};function tN(){var w;const[t,e]=_.useState(""),[n,r]=_.useState(!1),{state:i,addPurchase:s,getDisplayUnitName:a}=Ke(),{theme:o,currentTheme:l}=ce(),u=[{code:"ZAR",name:"South African Rand",symbol:"R"},{code:"USD",name:"US Dollar",symbol:"$"},{code:"EUR",name:"Euro",symbol:"€"},{code:"GBP",name:"British Pound",symbol:"£"},{code:"JPY",name:"Japanese Yen",symbol:"¥"},{code:"CAD",name:"Canadian Dollar",symbol:"C$"},{code:"AUD",name:"Australian Dollar",symbol:"A$"},{code:"CHF",name:"Swiss Franc",symbol:"CHF"},{code:"CNY",name:"Chinese Yuan",symbol:"¥"},{code:"INR",name:"Indian Rupee",symbol:"₹"},{code:"BRL",name:"Brazilian Real",symbol:"R$"},{code:"KRW",name:"South Korean Won",symbol:"₩"},{code:"MXN",name:"Mexican Peso",symbol:"$"},{code:"SGD",name:"Singapore Dollar",symbol:"S$"},{code:"NZD",name:"New Zealand Dollar",symbol:"NZ$"}],d=g=>Math.round((g+Number.EPSILON)*100)/100,h=g=>{const p=d(g);return p%1===0?p.toString():p.toFixed(2)},f=parseFloat(t)||0,m=i.unitCost||0,x=m>0?d(f/m):0,v=async g=>{g.preventDefault(),r(!0);try{const p=d(parseFloat(t)),y=x;if(isNaN(p)||p<=0){alert("Please enter a valid positive amount");return}if(m<=0){alert("Please set a valid unit cost in Settings before making a purchase");return}if(y<=0){alert("The calculated units must be greater than 0");return}s(p,y),e(""),alert(`Purchase added successfully! Added ${h(y)} ${a()} for ${i.currencySymbol||"R"}${h(p)}`)}catch(p){console.error("Error adding purchase:",p),alert("Error adding purchase. Please try again.")}finally{r(!1)}};return c.jsxs("form",{onSubmit:v,className:"space-y-6",children:[c.jsxs("div",{children:[c.jsxs("label",{htmlFor:"currency",className:`block text-sm font-semibold ${o.text} mb-3`,children:["💰 Amount (",((w=u.find(g=>g.code===(i.currency||"ZAR")))==null?void 0:w.name)||"South African Rand",")"]}),c.jsxs("div",{className:"relative",children:[c.jsx("div",{className:"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none",children:c.jsx("div",{className:`p-1 rounded-lg bg-gradient-to-br ${o.gradient}`,children:c.jsx(Te,{className:"h-4 w-4 text-white"})})}),c.jsx("input",{type:"number",id:"currency",value:t,onChange:g=>e(g.target.value),onWheel:zr,step:"0.01",min:"0",placeholder:"Enter amount to spend",className:`w-full pl-12 pr-4 py-4 md:py-4 border-4 ${o.border} rounded-xl focus:ring-4 focus:ring-opacity-50 focus:${o.border} ${o.card} ${o.text} ${o.textSecondary} font-bold shadow-lg hover:shadow-xl transition-all duration-200 min-w-0`,required:!0})]}),c.jsx("p",{className:`mt-2 text-xs ${o.textSecondary} opacity-80 font-medium`,children:"💡 Enter the amount you want to spend"})]}),f>0&&c.jsxs("div",{className:"mt-4 space-y-4",children:[c.jsxs("div",{className:`relative overflow-hidden rounded-2xl ${o.card} border ${o.border} p-4 md:p-5 shadow-lg mobile-card-auto`,children:[c.jsx("div",{className:`absolute inset-0 ${o.secondary}`}),c.jsx("div",{className:"relative",children:c.jsxs("div",{className:"flex items-center justify-between",children:[c.jsxs("div",{className:"flex-1",children:[c.jsx("div",{className:`text-xs font-semibold ${o.textSecondary} tracking-wider uppercase mb-1`,children:"PURCHASE AMOUNT"}),c.jsxs("p",{className:`text-lg md:text-xl font-bold ${o.text} mb-1`,children:[i.currencySymbol||"R",h(f)]}),c.jsxs("p",{className:`text-xs ${o.textSecondary}`,children:["@ ",i.currencySymbol||"R",h(m)," per ",a()]})]}),c.jsx("div",{className:`p-2 md:p-3 rounded-xl bg-gradient-to-br ${o.gradient} shadow-lg ml-3`,children:c.jsx(Te,{className:"h-5 md:h-6 w-5 md:w-6 text-white"})})]})}),c.jsx("div",{className:`absolute top-2 right-2 w-4 h-4 bg-gradient-to-r ${o.gradient} rounded-full opacity-20 blur-sm`}),c.jsx("div",{className:`absolute bottom-2 left-2 w-3 h-3 bg-gradient-to-r ${o.gradient} rounded-full opacity-15 blur-sm`})]}),c.jsxs("div",{className:`relative overflow-hidden rounded-2xl ${o.card} border ${o.border} p-4 md:p-5 shadow-lg mobile-card-auto`,children:[c.jsx("div",{className:`absolute inset-0 ${o.secondary}`}),c.jsx("div",{className:"relative",children:c.jsxs("div",{className:"flex items-center justify-between",children:[c.jsxs("div",{className:"flex-1",children:[c.jsx("div",{className:`text-xs font-semibold ${o.textSecondary} tracking-wider uppercase mb-1`,children:"UNITS TO RECEIVE"}),c.jsx("p",{className:`text-lg md:text-xl font-bold ${o.text} mb-1`,children:h(x)}),c.jsxs("p",{className:`text-xs ${o.textSecondary}`,children:["New Total: ",h(d(i.currentUnits+x))," ",a()]})]}),c.jsx("div",{className:`p-2 md:p-3 rounded-xl bg-gradient-to-br ${o.gradient} shadow-lg ml-3`,children:c.jsx(De,{className:"h-5 md:h-6 w-5 md:w-6 text-white"})})]})}),c.jsx("div",{className:`absolute top-2 right-2 w-4 h-4 bg-gradient-to-r ${o.gradient} rounded-full opacity-20 blur-sm`}),c.jsx("div",{className:`absolute bottom-2 left-2 w-3 h-3 bg-gradient-to-r ${o.gradient} rounded-full opacity-15 blur-sm`})]})]}),c.jsx("button",{type:"submit",disabled:n||f<=0||x<=0||m<=0,className:`w-full ${o.primary} text-white py-4 px-6 rounded-xl font-semibold hover:opacity-90 transition-all duration-300 focus:ring-4 focus:ring-opacity-50 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98]`,children:c.jsx("div",{className:"flex items-center justify-center gap-2 text-white",children:n?c.jsxs(c.Fragment,{children:[c.jsx("div",{className:"animate-spin rounded-full h-5 w-5 border-2 border-white border-t-transparent"}),c.jsx("span",{className:"text-white",children:"Adding Purchase..."})]}):c.jsxs(c.Fragment,{children:[c.jsx(Te,{className:"h-5 w-5 text-white"}),c.jsx("span",{className:"text-white",children:"Add Purchase"})]})})})]})}function nN(){const{state:t,getDisplayUnitName:e}=Ke(),{theme:n,currentTheme:r}=ce(),i=(d,h="bg-gray-800/50")=>r==="dark"?h:d,s=t.purchases.reduce((d,h)=>d+h.currency,0),a=t.purchases.reduce((d,h)=>d+h.units,0),o=d=>{const h=new Date(d),f=h.toLocaleDateString("en-GB"),m=h.toLocaleTimeString("en-GB",{hour:"2-digit",minute:"2-digit",hour12:!1});return{date:f,time:m}},l=c.jsxs("div",{className:"space-y-6",children:[c.jsxs("div",{className:`${n.card} rounded-2xl shadow-lg p-4 md:p-8 border ${n.border} ${i("bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50","bg-gray-800/50")} h-fit w-full`,children:[c.jsxs("h2",{className:`text-2xl font-semibold ${n.text} mb-6 flex items-center gap-3`,children:[c.jsx("div",{className:"p-3 rounded-xl bg-gradient-to-br from-blue-500 to-indigo-600 shadow-md",children:c.jsx(Te,{className:"h-6 w-6 text-white"})}),"Add New Purchase"]}),c.jsx("div",{className:`${i("bg-white/60","bg-gray-700/50")} backdrop-blur-sm rounded-xl p-4 md:p-6 w-full`,children:c.jsx(tN,{})})]}),c.jsxs("div",{className:"space-y-4",children:[c.jsx("div",{className:`${n.card} rounded-xl shadow-lg p-4 border ${n.border} hover:shadow-xl transition-all duration-300`,children:c.jsxs("div",{className:"flex items-center justify-between",children:[c.jsxs("div",{className:"flex items-center space-x-3",children:[c.jsx("div",{className:`p-2 rounded-lg bg-gradient-to-br ${n.gradient} shadow-lg`,children:c.jsx(Te,{className:"h-5 w-5 text-white"})}),c.jsxs("div",{className:"flex-1",children:[c.jsx("p",{className:`text-sm font-medium ${n.textSecondary} opacity-80 mb-2`,children:"Total Spent"}),c.jsxs("p",{className:`text-lg font-bold ${n.text} mb-2`,children:[t.currencySymbol||"R",s.toFixed(2)]})]})]}),c.jsx("div",{className:"text-right",children:c.jsx("p",{className:"text-xs text-emerald-500 font-medium",children:"All Purchases"})})]})}),c.jsx("div",{className:`${n.card} rounded-xl shadow-lg p-4 border ${n.border} hover:shadow-xl transition-all duration-300`,children:c.jsxs("div",{className:"flex items-center justify-between",children:[c.jsxs("div",{className:"flex items-center space-x-3",children:[c.jsx("div",{className:`p-2 rounded-lg bg-gradient-to-br ${n.gradient} shadow-lg`,children:c.jsx(De,{className:"h-5 w-5 text-white"})}),c.jsxs("div",{className:"flex-1",children:[c.jsxs("p",{className:`text-sm font-medium ${n.textSecondary} opacity-80 mb-1`,children:["Total ",e()," Purchased"]}),c.jsx("p",{className:`text-lg font-bold ${n.text} mb-1`,children:a.toFixed(2)})]})]}),c.jsx("div",{className:"text-right",children:c.jsx("p",{className:`text-xs ${n.textSecondary} font-medium`,children:e()})})]})}),c.jsx("div",{className:`${n.card} rounded-xl shadow-lg p-4 border ${n.border} hover:shadow-xl transition-all duration-300`,children:c.jsxs("div",{className:"flex items-center justify-between",children:[c.jsxs("div",{className:"flex items-center space-x-3",children:[c.jsx("div",{className:`p-2 rounded-lg bg-gradient-to-br ${n.gradient} shadow-lg`,children:c.jsx($a,{className:"h-5 w-5 text-white"})}),c.jsxs("div",{className:"flex-1",children:[c.jsx("p",{className:`text-sm font-medium ${n.textSecondary} opacity-80 mb-1`,children:"Total Purchases"}),c.jsx("p",{className:`text-lg font-bold ${n.text} mb-1`,children:t.purchases.length})]})]}),c.jsx("div",{className:"text-right",children:c.jsx("p",{className:"text-xs text-violet-500 font-medium",children:"Transactions"})})]})})]})]}),u=c.jsxs("div",{className:`${n.card} rounded-2xl shadow-lg p-6 border ${n.border} ${i("bg-gradient-to-br from-emerald-50 via-green-50 to-teal-50","bg-gray-800/50")} flex flex-col`,children:[c.jsxs("h2",{className:`text-xl font-semibold ${n.text} mb-4 flex items-center gap-3`,children:[c.jsx("div",{className:`p-2 rounded-xl bg-gradient-to-br ${n.gradient} shadow-md`,children:c.jsx($a,{className:"h-5 w-5 text-white"})}),"Recent Purchases"]}),c.jsxs("div",{className:"space-y-3 flex-1 overflow-y-auto",children:[t.purchases.slice(0,10).map(d=>c.jsx("div",{className:`p-4 ${n.secondary} border ${n.border} rounded-xl shadow-sm hover:shadow-md transition-all duration-200`,children:c.jsxs("div",{className:"flex items-center justify-between",children:[c.jsxs("div",{className:"flex items-center",children:[c.jsx("div",{className:`p-2 rounded-lg bg-gradient-to-br ${n.gradient} shadow-sm`,children:c.jsx(Te,{className:"h-4 w-4 text-white"})}),c.jsxs("div",{className:"ml-3",children:[c.jsxs("p",{className:`text-sm font-semibold ${n.text}`,children:[t.currencySymbol||"R",d.currency.toFixed(2)]}),c.jsxs("div",{className:`text-xs ${n.textSecondary}`,children:[c.jsx("p",{children:o(d.timestamp).date}),c.jsx("p",{children:o(d.timestamp).time})]})]})]}),c.jsxs("span",{className:`text-sm font-semibold ${n.text}`,children:["+",d.units.toFixed(2)," ",e()]})]})},d.id)),t.purchases.length===0&&c.jsxs("div",{className:`text-center py-8 ${n.secondary} border ${n.border} rounded-xl`,children:[c.jsx("div",{className:`p-3 rounded-2xl bg-gradient-to-br ${n.gradient} w-fit mx-auto mb-3`,children:c.jsx(Te,{className:"h-8 w-8 text-white"})}),c.jsx("p",{className:`text-sm ${n.textSecondary} font-medium`,children:"No purchases yet"}),c.jsx("p",{className:`text-xs ${n.textSecondary} mt-1`,children:"Add your first purchase to get started"})]})]})]});return c.jsx("div",{className:"w-full space-y-6 pb-6",children:c.jsx(Su,{leftContent:l,rightContent:u,rightContentTitle:"Recent Purchases",className:""})})}function rN(){const[t,e]=_.useState(""),[n,r]=_.useState(!1),{state:i,updateUsage:s,usageSinceLastRecording:a,getDisplayUnitName:o}=Ke(),{theme:l,currentTheme:u}=ce(),d=parseFloat(t)||0,h=i.currentUnits-d,f=h*i.unitCost,m=async x=>{x.preventDefault(),r(!0);try{const v=parseFloat(t);if(isNaN(v)||v<0){alert("Please enter a valid meter reading (0 or greater)");return}if(v>i.currentUnits){alert("Current reading cannot be higher than your available units");return}s(v),e(""),alert(`Usage recorded successfully! Used ${h.toFixed(2)} ${o()} costing ${i.currencySymbol||"R"}${f.toFixed(2)}`)}catch(v){console.error("Error recording usage:",v),alert("Error recording usage. Please try again.")}finally{r(!1)}};return c.jsxs("form",{onSubmit:m,className:"space-y-6",children:[c.jsxs("div",{children:[c.jsx("label",{htmlFor:"currentReading",className:`block text-sm font-semibold ${l.text} mb-3`,children:"Current Meter Reading"}),c.jsxs("div",{className:"relative",children:[c.jsx("div",{className:"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none",children:c.jsx("div",{className:`p-1 rounded-lg bg-gradient-to-br ${l.gradient}`,children:c.jsx(De,{className:"h-4 w-4 text-white"})})}),c.jsx("input",{type:"number",id:"currentReading",value:t,onChange:x=>e(x.target.value),onWheel:zr,step:"0.01",min:"0",max:i.currentUnits,placeholder:"Enter current meter reading",className:`w-full pl-12 pr-4 py-4 md:py-4 border-4 ${l.border} rounded-xl focus:ring-4 focus:ring-opacity-50 focus:${l.border} ${l.card} ${l.text} ${l.textSecondary} font-bold shadow-lg hover:shadow-xl transition-all duration-200 min-w-0`,required:!0})]}),c.jsx("p",{className:`mt-2 text-xs ${l.textSecondary} opacity-80 font-medium`,children:"📊 Enter the current reading from your electricity meter"})]}),d>0&&c.jsxs("div",{className:"mt-4 space-y-4",children:[c.jsxs("div",{className:`relative overflow-hidden rounded-2xl ${l.card} border ${l.border} p-4 md:p-5 shadow-lg mobile-card-auto`,children:[c.jsx("div",{className:`absolute inset-0 ${l.secondary}`}),c.jsx("div",{className:"relative",children:c.jsxs("div",{className:"flex items-center justify-between",children:[c.jsxs("div",{className:"flex-1",children:[c.jsx("div",{className:`text-xs font-semibold ${l.textSecondary} tracking-wider uppercase mb-1`,children:"READINGS"}),c.jsx("p",{className:`text-lg md:text-xl font-bold ${l.text} mb-1`,children:d.toFixed(2)}),c.jsxs("p",{className:`text-xs ${l.textSecondary}`,children:["Previous: ",i.currentUnits.toFixed(2)," ",o()]})]}),c.jsx("div",{className:`p-2 md:p-3 rounded-xl bg-gradient-to-br ${l.gradient} shadow-lg ml-3`,children:c.jsx(De,{className:"h-5 md:h-6 w-5 md:w-6 text-white"})})]})}),c.jsx("div",{className:`absolute top-2 right-2 w-4 h-4 bg-gradient-to-r ${l.gradient} rounded-full opacity-20 blur-sm`}),c.jsx("div",{className:`absolute bottom-2 left-2 w-3 h-3 bg-gradient-to-r ${l.gradient} rounded-full opacity-15 blur-sm`})]}),c.jsxs("div",{className:`relative overflow-hidden rounded-2xl ${l.card} border ${l.border} p-4 md:p-5 shadow-lg mobile-card-auto`,children:[c.jsx("div",{className:`absolute inset-0 ${l.secondary}`}),c.jsx("div",{className:"relative",children:c.jsxs("div",{className:"flex items-center justify-between",children:[c.jsxs("div",{className:"flex-1",children:[c.jsx("div",{className:`text-xs font-semibold ${l.textSecondary} tracking-wider uppercase mb-1`,children:"USAGE & COST"}),c.jsx("p",{className:`text-lg md:text-xl font-bold ${l.text} mb-1`,children:h.toFixed(2)}),c.jsxs("p",{className:`text-xs ${l.textSecondary}`,children:["Cost: ",i.currencySymbol||"R",f.toFixed(2)]})]}),c.jsx("div",{className:`p-2 md:p-3 rounded-xl bg-gradient-to-br ${l.gradient} shadow-lg ml-3`,children:c.jsx(Qb,{className:"h-5 md:h-6 w-5 md:w-6 text-white"})})]})}),c.jsx("div",{className:`absolute top-2 right-2 w-4 h-4 bg-gradient-to-r ${l.gradient} rounded-full opacity-20 blur-sm`}),c.jsx("div",{className:`absolute bottom-2 left-2 w-3 h-3 bg-gradient-to-r ${l.gradient} rounded-full opacity-15 blur-sm`})]})]}),d>0&&h<0&&c.jsx("div",{className:`mt-4 p-4 ${l.secondary} border ${l.border} rounded-xl`,children:c.jsxs("div",{className:"flex items-center",children:[c.jsx("div",{className:`p-1 rounded-lg ${l.accent} mr-2`,children:c.jsx("span",{className:"text-white text-xs",children:"⚠️"})}),c.jsx("span",{className:`${l.textSecondary} text-sm font-medium`,children:"Warning: New reading cannot be higher than available units"})]})}),c.jsx("button",{type:"submit",disabled:n||d<=0||d>i.currentUnits,className:`w-full ${l.primary} text-white py-4 px-6 rounded-xl font-semibold hover:opacity-90 transition-all duration-300 focus:ring-4 focus:ring-opacity-50 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98]`,children:c.jsx("div",{className:"flex items-center justify-center gap-2 text-white",children:n?c.jsxs(c.Fragment,{children:[c.jsx("div",{className:"animate-spin rounded-full h-5 w-5 border-2 border-white border-t-transparent"}),c.jsx("span",{className:"text-white",children:"Recording Usage..."})]}):c.jsxs(c.Fragment,{children:[c.jsx(De,{className:"h-5 w-5 text-white"}),c.jsx("span",{className:"text-white",children:"Record Usage"})]})})}),c.jsxs("div",{className:`w-full text-center p-4 ${l.card} rounded-xl border ${l.border}`,children:[c.jsxs("div",{className:"w-full flex items-center justify-center mb-2",children:[c.jsx("div",{className:`p-1 rounded-lg bg-gradient-to-br ${l.gradient} mr-2`,children:c.jsx("span",{className:"text-white text-xs",children:"💡"})}),c.jsx("span",{className:`text-sm font-semibold ${l.text}`,children:"How it works"})]}),c.jsx("p",{className:`w-full text-xs ${l.textSecondary} leading-relaxed`,children:"Record your current meter reading to track electricity usage. The system will calculate how many units you've used since the last recording."})]}),c.jsxs("div",{className:`w-full p-6 ${l.card} rounded-xl border ${l.border} shadow-sm`,children:[c.jsxs("div",{className:"w-full flex items-center mb-4",children:[c.jsx("div",{className:`p-2 rounded-lg bg-gradient-to-br ${l.gradient} mr-3`,children:c.jsx(De,{className:"h-5 w-5 text-white"})}),c.jsx("h3",{className:`font-semibold ${l.text} text-lg`,children:"Current Status"})]}),c.jsxs("div",{className:"w-full space-y-3 text-sm",children:[c.jsxs("div",{className:`w-full flex justify-between items-center p-3 ${l.secondary} rounded-lg`,children:[c.jsx("span",{className:`${l.textSecondary} font-medium`,children:"Available Units:"}),c.jsxs("span",{className:`${l.text} font-bold`,children:[i.currentUnits.toFixed(2)," ",o()]})]}),c.jsxs("div",{className:`w-full flex justify-between items-center p-3 ${l.secondary} rounded-lg`,children:[c.jsx("span",{className:`${l.textSecondary} font-medium`,children:"Previous Reading:"}),c.jsxs("span",{className:`${l.text} font-bold`,children:[i.previousUnits.toFixed(2)," ",o()]})]}),c.jsxs("div",{className:`w-full flex justify-between items-center p-3 ${l.secondary} rounded-lg`,children:[c.jsx("span",{className:`${l.textSecondary} font-medium`,children:"Usage Since Last:"}),c.jsxs("span",{className:`${l.text} font-bold`,children:[a.toFixed(2)," ",o()]})]})]})]})]})}function cc({children:t,className:e=""}){const{theme:n}=ce(),[r,i]=_.useState(!0),[s,a]=_.useState(!1),o=_.useRef(null);_.useEffect(()=>{const u=()=>{if(o.current){const{scrollWidth:d,clientWidth:h}=o.current;a(d>h)}};return u(),window.addEventListener("resize",u),()=>window.removeEventListener("resize",u)},[t]),_.useEffect(()=>{if(!s){i(!1);return}const u=setTimeout(()=>{i(!1)},5e3);return()=>clearTimeout(u)},[s]);const l=()=>{i(!1)};return c.jsxs("div",{className:`relative ${e}`,children:[r&&s&&c.jsx("div",{className:"md:hidden absolute top-2 right-2 z-20 pointer-events-none",children:c.jsxs("div",{className:`flex items-center gap-2 ${n.primary} text-white px-3 py-2 rounded-full shadow-lg backdrop-blur-sm animate-bounce`,children:[c.jsx("span",{className:"text-xs font-medium",children:"Swipe to see more"}),c.jsxs("div",{className:"flex space-x-1",children:[c.jsx("div",{className:"w-1 h-1 bg-white rounded-full animate-pulse"}),c.jsx("div",{className:"w-1 h-1 bg-white rounded-full animate-pulse",style:{animationDelay:"0.2s"}}),c.jsx("div",{className:"w-1 h-1 bg-white rounded-full animate-pulse",style:{animationDelay:"0.4s"}})]}),c.jsx("span",{className:"text-sm",children:"→"})]})}),s&&c.jsx("div",{className:`md:hidden absolute top-0 right-0 bottom-0 w-8 bg-gradient-to-l ${n.card}/80 to-transparent z-10 pointer-events-none`}),c.jsx("div",{ref:o,className:"overflow-x-auto overflow-y-hidden",style:{WebkitOverflowScrolling:"touch"},onScroll:l,children:t})]})}os.register(oc,lc,na,TS,Ag,Dg);function iN(){const{state:t,usageSinceLastRecording:e,getDisplayUnitName:n,weeklyUsageTotal:r,monthlyUsageTotal:i,weeklyPurchaseTotal:s,monthlyPurchaseTotal:a}=Ke(),{theme:o,currentTheme:l}=ce(),u=(p,y="bg-gray-800/50")=>l==="dark"?y:p,d=t.usageHistory.reduce((p,y)=>p+y.usage,0),h=t.usageHistory.length>0?d/t.usageHistory.length:0,f=p=>{const y=new Date(p),b=y.toLocaleDateString("en-GB"),S=y.toLocaleTimeString("en-GB",{hour:"2-digit",minute:"2-digit",hour12:!1});return{date:b,time:S}},m=t.usageHistory.slice(-7).reverse(),x={labels:m.length>0?m.map((p,y)=>new Date(p.timestamp).toLocaleDateString("en-US",{month:"short",day:"numeric"})):["Mon","Tue","Wed","Thu","Fri","Sat","Sun"],datasets:[{label:`Daily Usage (${n()})`,data:m.length>0?m.map(p=>p.usage):[12.5,15.2,8.7,22.1,18.9,14.3,16.8],backgroundColor:["rgba(99, 102, 241, 0.8)","rgba(139, 92, 246, 0.8)","rgba(236, 72, 153, 0.8)","rgba(34, 197, 94, 0.8)","rgba(251, 146, 60, 0.8)","rgba(14, 165, 233, 0.8)","rgba(168, 85, 247, 0.8)"],borderColor:["rgba(99, 102, 241, 1)","rgba(139, 92, 246, 1)","rgba(236, 72, 153, 1)","rgba(34, 197, 94, 1)","rgba(251, 146, 60, 1)","rgba(14, 165, 233, 1)","rgba(168, 85, 247, 1)"],borderWidth:2,borderRadius:8,borderSkipped:!1}]},v={responsive:!0,maintainAspectRatio:!1,plugins:{legend:{display:!1},title:{display:!0,text:"Daily Usage Trend",font:{size:16,weight:"bold"},color:o.text==="text-gray-900"?"#1f2937":"#f9fafb",padding:20},tooltip:{backgroundColor:"rgba(0, 0, 0, 0.8)",titleColor:"#ffffff",bodyColor:"#ffffff",borderColor:"rgba(255, 255, 255, 0.2)",borderWidth:1,cornerRadius:8,displayColors:!0,callbacks:{label:function(p){return`Usage: ${p.parsed.y.toFixed(2)} ${n()}`}}}},scales:{y:{beginAtZero:!0,grid:{color:"rgba(156, 163, 175, 0.2)",drawBorder:!1},ticks:{color:o.textSecondary==="text-gray-600"?"#6b7280":"#9ca3af",font:{size:12},callback:function(p){return p+" "+n()}}},x:{grid:{display:!1},ticks:{color:o.textSecondary==="text-gray-600"?"#6b7280":"#9ca3af",font:{size:12}}}},animation:{duration:1500,easing:"easeInOutQuart"}},w=c.jsxs("div",{className:"space-y-6",children:[c.jsxs("div",{className:`${o.card} rounded-2xl shadow-lg p-4 md:p-6 border ${o.border} ${u("bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50","bg-gray-800/50")} w-full`,children:[c.jsxs("h2",{className:`text-xl font-semibold ${o.text} mb-4 flex items-center gap-3`,children:[c.jsx("div",{className:"p-2 rounded-xl bg-gradient-to-br from-blue-500 to-indigo-600 shadow-md",children:c.jsx(De,{className:"h-5 w-5 text-white"})}),"Record New Reading"]}),c.jsx("div",{className:`${u("bg-white/60","bg-gray-700/50")} backdrop-blur-sm rounded-xl p-4 w-full`,children:c.jsx(rN,{})})]}),c.jsx("div",{className:`${o.card} rounded-xl shadow-lg p-4 md:p-5 border ${o.border} min-h-24`,children:c.jsx(cc,{children:c.jsxs("div",{className:"flex items-center justify-between min-h-full min-w-max",children:[c.jsxs("div",{className:"flex items-center space-x-3 flex-shrink-0",children:[c.jsx("h3",{className:`text-base font-semibold ${o.text} whitespace-nowrap`,children:"This Week"}),c.jsxs("div",{className:"flex space-x-1",children:[c.jsx("div",{className:`p-1.5 rounded-lg ${o.secondary}`,children:c.jsx(Te,{className:`h-4 w-4 ${o.textSecondary}`})}),c.jsx("div",{className:`p-1.5 rounded-lg ${o.secondary}`,children:c.jsx(yt,{className:`h-4 w-4 ${o.textSecondary}`})})]})]}),c.jsxs("div",{className:"flex items-center space-x-6 flex-shrink-0",children:[c.jsxs("div",{className:"text-center",children:[c.jsx("p",{className:`text-sm font-medium ${o.textSecondary} whitespace-nowrap`,children:"Purchases"}),c.jsxs("p",{className:`text-lg font-bold ${o.text} whitespace-nowrap`,children:[t.currencySymbol,s.toFixed(2)]})]}),c.jsx("div",{className:`border-l ${o.border} h-10 w-px flex-shrink-0`}),c.jsxs("div",{className:"text-center",children:[c.jsx("p",{className:`text-sm font-medium ${o.textSecondary} whitespace-nowrap`,children:"Usage"}),c.jsxs("p",{className:`text-lg font-bold ${o.text} whitespace-nowrap`,children:[r.toFixed(2)," ",n()]}),c.jsxs("p",{className:`text-sm ${o.textSecondary} whitespace-nowrap`,children:["Cost: ",t.currencySymbol,(r*t.unitCost).toFixed(2)]})]})]})]})})}),c.jsx("div",{className:`${o.card} rounded-xl shadow-lg p-4 md:p-5 border ${o.border} min-h-24`,children:c.jsx(cc,{children:c.jsxs("div",{className:"flex items-center justify-between min-h-full min-w-max",children:[c.jsxs("div",{className:"flex items-center space-x-3 flex-shrink-0",children:[c.jsx("h3",{className:`text-base font-semibold ${o.text} whitespace-nowrap`,children:"This Month"}),c.jsxs("div",{className:"flex space-x-1",children:[c.jsx("div",{className:`p-1.5 rounded-lg ${o.secondary}`,children:c.jsx(Te,{className:`h-4 w-4 ${o.textSecondary}`})}),c.jsx("div",{className:`p-1.5 rounded-lg ${o.secondary}`,children:c.jsx(yt,{className:`h-4 w-4 ${o.textSecondary}`})})]})]}),c.jsxs("div",{className:"flex items-center space-x-6 flex-shrink-0",children:[c.jsxs("div",{className:"text-center",children:[c.jsx("p",{className:`text-sm font-medium ${o.textSecondary} whitespace-nowrap`,children:"Purchases"}),c.jsxs("p",{className:`text-lg font-bold ${o.text} whitespace-nowrap`,children:[t.currencySymbol,a.toFixed(2)]})]}),c.jsx("div",{className:`border-l ${o.border} h-10 w-px flex-shrink-0`}),c.jsxs("div",{className:"text-center",children:[c.jsx("p",{className:`text-sm font-medium ${o.textSecondary} whitespace-nowrap`,children:"Usage"}),c.jsxs("p",{className:`text-lg font-bold ${o.text} whitespace-nowrap`,children:[i.toFixed(2)," ",n()]}),c.jsxs("p",{className:`text-sm ${o.textSecondary} whitespace-nowrap`,children:["Cost: ",t.currencySymbol,(i*t.unitCost).toFixed(2)]})]})]})]})})}),c.jsxs("div",{className:"space-y-4",children:[c.jsxs("div",{className:`relative overflow-hidden rounded-2xl ${o.card} border ${o.border} p-3 md:p-4 shadow-lg mobile-card-auto`,children:[c.jsx("div",{className:`absolute inset-0 ${o.secondary}`}),c.jsx("div",{className:"relative",children:c.jsxs("div",{className:"flex items-center justify-between",children:[c.jsxs("div",{className:"flex-1",children:[c.jsx("div",{className:`text-xs font-semibold ${o.textSecondary} tracking-wider uppercase mb-1`,children:"CURRENT READING"}),c.jsx("p",{className:`text-lg md:text-xl font-bold ${o.text} mb-1`,children:t.currentUnits.toFixed(2)}),c.jsx("p",{className:`text-xs ${o.textSecondary}`,children:n()})]}),c.jsx("div",{className:`p-2 md:p-3 rounded-xl bg-gradient-to-br ${o.gradient} shadow-lg ml-3`,children:c.jsx(De,{className:"h-5 md:h-6 w-5 md:w-6 text-white"})})]})}),c.jsx("div",{className:`absolute top-2 right-2 w-4 h-4 bg-gradient-to-r ${o.gradient} rounded-full opacity-20 blur-sm`}),c.jsx("div",{className:`absolute bottom-2 left-2 w-3 h-3 bg-gradient-to-r ${o.gradient} rounded-full opacity-15 blur-sm`})]}),t.usageHistory.length>0&&c.jsxs("div",{className:`relative overflow-hidden rounded-2xl ${o.card} border ${o.border} p-3 md:p-4 shadow-lg mobile-card-auto`,children:[c.jsx("div",{className:`absolute inset-0 ${o.secondary}`}),c.jsx("div",{className:"relative",children:c.jsxs("div",{className:"flex items-center justify-between",children:[c.jsxs("div",{className:"flex-1",children:[c.jsx("div",{className:`text-xs font-semibold ${o.textSecondary} tracking-wider uppercase mb-1`,children:"USAGE SINCE LAST"}),c.jsx("p",{className:`text-lg md:text-xl font-bold ${o.text} mb-1`,children:e.toFixed(2)}),c.jsxs("p",{className:`text-xs ${o.textSecondary}`,children:[n()," used"]})]}),c.jsx("div",{className:`p-2 md:p-3 rounded-xl bg-gradient-to-br ${o.gradient} shadow-lg ml-3`,children:c.jsx(yt,{className:"h-5 md:h-6 w-5 md:w-6 text-white"})})]})}),c.jsx("div",{className:`absolute top-2 right-2 w-4 h-4 bg-gradient-to-r ${o.gradient} rounded-full opacity-20 blur-sm`}),c.jsx("div",{className:`absolute bottom-2 left-2 w-3 h-3 bg-gradient-to-r ${o.gradient} rounded-full opacity-15 blur-sm`})]}),c.jsxs("div",{className:`relative overflow-hidden rounded-2xl ${o.card} border ${o.border} p-3 md:p-4 shadow-lg mobile-card-auto`,children:[c.jsx("div",{className:`absolute inset-0 ${o.secondary}`}),c.jsx("div",{className:"relative",children:c.jsxs("div",{className:"flex items-center justify-between",children:[c.jsxs("div",{className:"flex-1",children:[c.jsx("div",{className:`text-xs font-semibold ${o.textSecondary} tracking-wider uppercase mb-1`,children:"TOTAL USAGE"}),c.jsx("p",{className:`text-lg md:text-xl font-bold ${o.text} mb-1`,children:d.toFixed(2)}),c.jsx("p",{className:`text-xs ${o.textSecondary}`,children:n()})]}),c.jsx("div",{className:`p-2 md:p-3 rounded-xl bg-gradient-to-br ${o.gradient} shadow-lg ml-3`,children:c.jsx($a,{className:"h-5 md:h-6 w-5 md:w-6 text-white"})})]})}),c.jsx("div",{className:`absolute top-2 right-2 w-4 h-4 bg-gradient-to-r ${o.gradient} rounded-full opacity-20 blur-sm`}),c.jsx("div",{className:`absolute bottom-2 left-2 w-3 h-3 bg-gradient-to-r ${o.gradient} rounded-full opacity-15 blur-sm`})]}),c.jsxs("div",{className:`relative overflow-hidden rounded-2xl ${o.card} border ${o.border} p-3 md:p-4 shadow-lg mobile-card-auto`,children:[c.jsx("div",{className:`absolute inset-0 ${o.secondary}`}),c.jsx("div",{className:"relative",children:c.jsxs("div",{className:"flex items-center justify-between",children:[c.jsxs("div",{className:"flex-1",children:[c.jsx("div",{className:`text-xs font-semibold ${o.textSecondary} tracking-wider uppercase mb-1`,children:"AVERAGE USAGE"}),c.jsx("p",{className:`text-lg md:text-xl font-bold ${o.text} mb-1`,children:h.toFixed(2)}),c.jsx("p",{className:`text-xs ${o.textSecondary}`,children:n()})]}),c.jsx("div",{className:`p-2 md:p-3 rounded-xl bg-gradient-to-br ${o.gradient} shadow-lg ml-3`,children:c.jsx(Gd,{className:"h-5 md:h-6 w-5 md:w-6 text-white"})})]})}),c.jsx("div",{className:`absolute top-2 right-2 w-4 h-4 bg-gradient-to-r ${o.gradient} rounded-full opacity-20 blur-sm`}),c.jsx("div",{className:`absolute bottom-2 left-2 w-3 h-3 bg-gradient-to-r ${o.gradient} rounded-full opacity-15 blur-sm`})]})]}),c.jsxs("div",{className:`${o.card} rounded-2xl shadow-lg p-6 border ${o.border} ${u("bg-gradient-to-br from-slate-50 via-gray-50 to-zinc-50","bg-gray-800/50")}`,children:[c.jsx("div",{className:"flex items-center justify-between mb-6",children:c.jsxs("div",{children:[c.jsxs("h2",{className:`text-xl font-bold ${o.text} flex items-center gap-3`,children:[c.jsx("div",{className:"p-2 rounded-xl bg-gradient-to-br from-indigo-500 to-purple-600 shadow-lg",children:c.jsx(su,{className:"h-5 w-5 text-white"})}),"Usage Analytics"]}),c.jsx("p",{className:`mt-2 ${o.textSecondary} opacity-80 text-sm`,children:"Visual representation of your daily electricity consumption"})]})}),c.jsxs("div",{className:"h-64 relative",children:[c.jsx("div",{className:`absolute inset-0 ${u("bg-gradient-to-br from-white/80 to-white/40","bg-gray-700/40")} rounded-xl backdrop-blur-sm`}),c.jsx("div",{className:"relative h-full p-4",children:c.jsx(GS,{data:x,options:v})})]})]}),c.jsxs("div",{className:`${o.card} rounded-2xl shadow-lg p-6 border ${o.border} ${u("bg-gradient-to-br from-amber-50 via-yellow-50 to-orange-50","bg-gray-800/50")}`,children:[c.jsxs("h2",{className:`text-lg font-semibold ${o.text} mb-4 flex items-center gap-3`,children:[c.jsx("div",{className:"p-2 rounded-xl bg-gradient-to-br from-amber-500 to-orange-600 shadow-md",children:c.jsx(Gd,{className:"h-4 w-4 text-white"})}),"How Usage is Calculated"]}),t.usageHistory.length>0&&c.jsx("div",{className:`p-4 ${o.card} rounded-xl border ${o.border} shadow-sm`,children:c.jsxs("div",{className:"space-y-3 text-sm",children:[c.jsxs("div",{className:`flex justify-between items-center p-3 ${o.secondary} rounded-lg border ${o.border}`,children:[c.jsx("span",{className:`${o.textSecondary} font-medium`,children:"Previous Reading:"}),c.jsxs("span",{className:`${o.text} font-bold`,children:[t.previousUnits.toFixed(2)," ",n()]})]}),c.jsxs("div",{className:`flex justify-between items-center p-3 ${o.secondary} rounded-lg border ${o.border}`,children:[c.jsx("span",{className:`${o.textSecondary} font-medium`,children:"Current Reading:"}),c.jsxs("span",{className:`${o.text} font-bold`,children:[t.currentUnits.toFixed(2)," ",n()]})]}),c.jsx("div",{className:`border-t ${o.border} my-3`}),c.jsxs("div",{className:`flex justify-between items-center p-3 ${o.secondary} rounded-lg border ${o.border}`,children:[c.jsx("span",{className:`${o.text} font-semibold`,children:"Usage Since Last Recording:"}),c.jsxs("div",{className:"text-right",children:[c.jsxs("div",{className:`${o.textSecondary} text-xs mb-1`,children:[t.previousUnits.toFixed(2)," - ",t.currentUnits.toFixed(2)]}),c.jsxs("span",{className:`${o.text} font-bold text-lg`,children:[e.toFixed(2)," ",n()]})]})]}),c.jsxs("div",{className:`flex justify-between items-center p-3 ${o.secondary} rounded-lg border ${o.border}`,children:[c.jsx("span",{className:`${o.textSecondary} font-medium`,children:"Cost of Usage:"}),c.jsxs("span",{className:`${o.text} font-bold`,children:[t.currencySymbol||"R",(e*t.unitCost).toFixed(2)]})]})]})})]})]}),g=c.jsxs("div",{className:`${o.card} rounded-2xl shadow-lg p-6 border ${o.border} ${u("bg-gradient-to-br from-emerald-50 via-green-50 to-teal-50","bg-gray-800/50")} flex flex-col`,children:[c.jsxs("h2",{className:`text-xl font-semibold ${o.text} mb-4 flex items-center gap-3`,children:[c.jsx("div",{className:"p-2 rounded-xl bg-gradient-to-br from-emerald-500 to-green-600 shadow-md",children:c.jsx($a,{className:"h-5 w-5 text-white"})}),"Recent Readings"]}),c.jsxs("div",{className:"space-y-2",children:[t.usageHistory.slice(0,10).map(p=>c.jsx("div",{className:`p-3 ${u("bg-white/60","bg-gray-700/50")} backdrop-blur-sm rounded-xl border ${u("border-white/40","border-gray-600")} shadow-sm hover:shadow-md transition-all duration-200`,children:c.jsxs("div",{className:"flex justify-between items-center",children:[c.jsxs("div",{className:"flex-1",children:[c.jsxs("p",{className:`font-semibold ${o.text} text-base`,children:[p.currentUnits.toFixed(2)," ",n()]}),c.jsxs("p",{className:`text-xs ${o.textSecondary} opacity-80 mt-1`,children:["Previous: ",p.previousUnits.toFixed(2)," ",n()]}),c.jsxs("div",{className:`text-xs ${o.textSecondary} mt-1 opacity-70`,children:[c.jsx("p",{children:f(p.timestamp).date}),c.jsx("p",{children:f(p.timestamp).time})]})]}),c.jsxs("div",{className:"text-right ml-3",children:[c.jsxs("span",{className:`inline-flex items-center px-2 py-1 rounded-full text-xs font-semibold ${p.usage>0?"bg-gradient-to-r from-rose-100 to-pink-100 text-rose-700 border border-rose-200":"bg-gradient-to-r from-gray-100 to-slate-100 text-gray-700 border border-gray-200"}`,children:["-",p.usage.toFixed(2)," ",n()]}),c.jsxs("p",{className:`text-xs ${o.textSecondary} mt-1 font-medium`,children:[t.currencySymbol||"R",(p.usage*t.unitCost).toFixed(2)]})]})]})},p.id)),t.usageHistory.length===0&&c.jsxs("div",{className:`text-center py-12 ${u("bg-white/40","bg-gray-700/40")} backdrop-blur-sm rounded-xl border ${u("border-white/40","border-gray-600")}`,children:[c.jsx("div",{className:`p-4 rounded-2xl ${o.secondary} w-fit mx-auto mb-4`,children:c.jsx(yt,{className:`h-12 w-12 ${o.textSecondary}`})}),c.jsx("p",{className:`text-sm ${o.textSecondary} opacity-80 font-medium`,children:"No usage records yet"}),c.jsx("p",{className:`text-xs ${o.textSecondary} opacity-60 mt-1`,children:"Record your first reading above to get started"})]})]})]});return c.jsx("div",{className:"space-y-6",children:c.jsx(Su,{leftContent:w,rightContent:g,rightContentTitle:"Recent Readings",className:""})})}function sN({history:t}){const{state:e,getDisplayUnitName:n}=Ke(),{theme:r,currentTheme:i}=ce(),s=a=>{const o=new Date(a),l=o.toLocaleDateString("en-GB"),u=o.toLocaleTimeString("en-GB",{hour:"2-digit",minute:"2-digit",hour12:!1});return{date:l,time:u}};return t.length===0?null:c.jsx(cc,{children:c.jsxs("table",{className:`min-w-full divide-y ${i==="dark"?"divide-gray-600":"divide-gray-200"}`,style:{minWidth:"600px"},children:[c.jsx("thead",{className:r.secondary,children:c.jsxs("tr",{children:[c.jsx("th",{className:`px-6 py-3 text-left text-xs font-medium ${r.textSecondary} uppercase tracking-wider`,children:"Type"}),c.jsx("th",{className:`px-6 py-3 text-left text-xs font-medium ${r.textSecondary} uppercase tracking-wider`,children:"Date & Time"}),c.jsx("th",{className:`px-6 py-3 text-left text-xs font-medium ${r.textSecondary} uppercase tracking-wider`,children:"Details"}),c.jsx("th",{className:`px-6 py-3 text-left text-xs font-medium ${r.textSecondary} uppercase tracking-wider`,children:n()}),c.jsx("th",{className:`px-6 py-3 text-left text-xs font-medium ${r.textSecondary} uppercase tracking-wider`,children:"Amount"})]})}),c.jsx("tbody",{className:`${r.card} divide-y ${i==="dark"?"divide-gray-600":"divide-gray-200"}`,children:t.map(a=>c.jsxs("tr",{className:`${i==="dark"?"hover:bg-gray-700":"hover:bg-gray-50"} transition-colors duration-200`,children:[c.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:c.jsx("div",{className:"flex items-center",children:a.type==="purchase"?c.jsxs("div",{className:"flex items-center",children:[c.jsx("div",{className:`p-2 rounded-lg ${r.secondary}`,children:c.jsx(Te,{className:`h-4 w-4 ${r.textSecondary}`})}),c.jsx("div",{className:"ml-3",children:c.jsxs("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${i==="dark"?"bg-green-900/30 text-green-400":"bg-green-100 text-green-800"}`,children:[c.jsx(Kb,{className:"mr-1 h-3 w-3"}),"Purchase"]})})]}):c.jsxs("div",{className:"flex items-center",children:[c.jsx("div",{className:`p-2 rounded-lg ${i==="dark"?"bg-red-900/30":"bg-red-100"}`,children:c.jsx(yt,{className:"h-4 w-4 text-red-600"})}),c.jsx("div",{className:"ml-3",children:c.jsxs("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${i==="dark"?"bg-red-900/30 text-red-400":"bg-red-100 text-red-800"}`,children:[c.jsx(Xb,{className:"mr-1 h-3 w-3"}),"Usage"]})})]})})}),c.jsx("td",{className:`px-6 py-6 whitespace-nowrap text-sm ${r.text}`,children:c.jsxs("div",{children:[c.jsx("p",{children:s(a.timestamp).date}),c.jsx("p",{className:`text-xs ${r.textSecondary}`,children:s(a.timestamp).time})]})}),c.jsx("td",{className:`px-6 py-6 text-sm ${r.text}`,children:a.type==="purchase"?c.jsxs("div",{className:"space-y-2",children:[c.jsx("p",{className:"font-medium",children:"Electricity Purchase"}),c.jsxs("p",{className:`text-xs ${r.textSecondary}`,children:["@ ",e.currencySymbol||"R",a.unitCost.toFixed(2)," per ",n()]})]}):c.jsxs("div",{className:"space-y-2",children:[c.jsx("p",{className:"font-medium",children:"Usage Recording"}),c.jsxs("p",{className:`text-xs ${r.textSecondary}`,children:["From ",a.previousUnits.toFixed(2)," to ",a.currentUnits.toFixed(2)," ",n()]})]})}),c.jsx("td",{className:`px-6 py-6 whitespace-nowrap text-sm ${r.text}`,children:a.type==="purchase"?c.jsxs("span",{className:`${r.text} font-medium`,children:["+",a.units.toFixed(2)]}):c.jsxs("span",{className:`${r.text} font-medium`,children:["-",a.usage.toFixed(2)]})}),c.jsx("td",{className:`px-6 py-6 whitespace-nowrap text-sm ${r.text}`,children:a.type==="purchase"?c.jsxs("span",{className:`${r.text} font-medium`,children:["+",e.currencySymbol||"R",a.currency.toFixed(2)]}):c.jsxs("span",{className:`${r.text} font-medium`,children:["-",e.currencySymbol||"R",(a.usage*e.unitCost).toFixed(2)]})})]},`${a.type}-${a.id}`))})]})})}function aN(){const t=tr(),[e,n]=_.useState("all"),[r,i]=_.useState(""),[s,a]=_.useState(!1),[o,l]=_.useState(""),[u,d]=_.useState(""),[h,f]=_.useState(new Date),m=_.useRef(null),{state:x,getDisplayUnitName:v,weeklyPurchaseTotal:w,monthlyPurchaseTotal:g,weeklyUsageTotal:p,monthlyUsageTotal:y}=Ke(),{theme:b,currentTheme:S}=ce(),N=[...x.purchases.map($=>({...$,type:"purchase"})),...x.usageHistory.map($=>({...$,type:"usage"}))].sort(($,U)=>new Date(U.date)-new Date($.date)).filter($=>{const U=e==="all"||$.type===e;if(r&&!o&&!u)return U&&$.date.includes(r);if(o||u){const z=new Date($.date),B=o?new Date(o):null,Y=u?new Date(u):null;let T=!0;return B&&(T=T&&z>=B),Y&&(T=T&&z<=Y),U&&T}return U});x.purchases.reduce(($,U)=>$+U.currency,0),x.usageHistory.reduce(($,U)=>$+U.usage,0)*x.unitCost;const C=[{id:"all",name:"All Activity",icon:Ra},{id:"purchase",name:"Purchases",icon:Te},{id:"usage",name:"Usage",icon:yt}],P=()=>{i(""),a(!1)},M=()=>{i(""),l(""),d(""),a(!1)},D=r||o||u;return _.useEffect(()=>{const $=setInterval(()=>{f(new Date)},1e3);return()=>clearInterval($)},[]),_.useEffect(()=>{const $=U=>{m.current&&!m.current.contains(U.target)&&a(!1)};if(s)return document.addEventListener("mousedown",$),()=>document.removeEventListener("mousedown",$)},[s]),c.jsxs("div",{className:"space-y-6",children:[c.jsxs("div",{className:`${b.card} rounded-2xl shadow-lg border ${b.border}`,children:[c.jsx("div",{className:`p-4 md:p-8 ${b.secondary} rounded-t-2xl`,children:c.jsxs("div",{className:"space-y-4",children:[c.jsx("div",{className:"flex flex-wrap gap-2",children:C.map($=>c.jsxs("button",{onClick:()=>n($.id),className:`flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-all duration-300 border backdrop-blur-md shadow-lg ${e===$.id?`${b.primary} text-white border-white/30 bg-opacity-90 shadow-xl`:S==="dark"?`${b.text} bg-white/10 border-white/20 hover:bg-white/20 hover:border-white/30 hover:shadow-xl`:`${b.text} bg-white/30 border-white/40 hover:bg-white/50 hover:border-white/60 hover:shadow-xl`}`,children:[c.jsx($.icon,{className:"mr-2 h-4 w-4"}),$.name]},$.id))}),c.jsxs("div",{ref:m,className:"space-y-3",children:[c.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[c.jsx(qb,{className:`h-5 w-5 ${b.textSecondary}`}),c.jsx("span",{className:`text-sm font-medium ${b.text}`,children:"Filter by Date"})]}),c.jsxs("div",{className:"flex flex-col sm:flex-row gap-3",children:[c.jsx("input",{type:"date",value:r||new Date().toISOString().split("T")[0],onChange:$=>{i($.target.value),$.target.value&&(l(""),d(""))},className:`px-3 py-2 border ${b.border} rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${b.card} ${b.text} text-sm w-full sm:w-40`,placeholder:"Filter by date"}),c.jsx("button",{onClick:()=>a(!s),className:`px-3 py-2 border ${b.border} rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${b.card} ${b.text} hover:${b.secondary} transition-colors text-sm ${o||u?"ring-2 ring-blue-500":""}`,children:"📅 Range"}),D&&c.jsx("button",{onClick:M,className:`px-3 py-2 text-sm ${b.textSecondary} hover:${b.text} transition-colors border ${b.border} rounded-lg`,children:"Clear All"})]}),s&&c.jsx("div",{className:`mt-3 p-4 ${b.card} border ${b.border} rounded-xl shadow-xl w-full max-w-md`,children:c.jsxs("div",{className:"space-y-4",children:[c.jsx("h3",{className:`font-semibold ${b.text} text-sm`,children:"Select Date Range"}),c.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4",children:[c.jsxs("div",{children:[c.jsx("label",{className:`block text-xs font-medium ${b.textSecondary} mb-1`,children:"From Date"}),c.jsx("input",{type:"date",value:o,onChange:$=>{l($.target.value),i("")},className:`w-full px-3 py-2 border ${b.border} rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${b.card} ${b.text} text-sm`})]}),c.jsxs("div",{children:[c.jsx("label",{className:`block text-xs font-medium ${b.textSecondary} mb-1`,children:"To Date"}),c.jsx("input",{type:"date",value:u,onChange:$=>{d($.target.value),i("")},className:`w-full px-3 py-2 border ${b.border} rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${b.card} ${b.text} text-sm`})]})]}),c.jsxs("div",{className:"flex justify-between items-center pt-2",children:[c.jsx("button",{onClick:()=>{l(""),d("")},className:`text-xs ${b.textSecondary} hover:${b.text} transition-colors`,children:"Clear Range"}),c.jsx("button",{onClick:P,className:`px-4 py-2 bg-gradient-to-r ${b.gradient} text-white rounded-lg hover:opacity-90 transition-colors text-sm font-medium`,children:"Apply"})]})]})})]})]})}),c.jsx("div",{className:`border-t ${b.border}`,children:c.jsx(sN,{history:N})})]}),N.length===0&&c.jsxs("div",{className:`${b.card} rounded-2xl shadow-lg p-16 text-center border ${b.border}`,children:[c.jsxs("div",{className:"relative",children:[c.jsx("div",{className:`absolute inset-0 ${b.secondary} rounded-full opacity-20 scale-110`}),c.jsx("div",{className:`relative p-6 rounded-2xl ${b.secondary} w-fit mx-auto`,children:c.jsx(Ra,{className:`h-16 w-16 ${b.textSecondary}`})})]}),c.jsx("h3",{className:`mt-6 text-2xl font-bold ${b.text}`,children:"No history found"}),c.jsx("p",{className:`mt-3 ${b.textSecondary} opacity-80 text-lg leading-relaxed max-w-md mx-auto`,children:D?"No records found for the selected date range. Try adjusting your filters or clear them to see all records.":"Start by making purchases or recording usage to see your history here."}),!D&&c.jsxs("div",{className:"mt-8 flex flex-col sm:flex-row justify-center gap-4",children:[c.jsx("button",{onClick:()=>t("/purchases"),className:`bg-gradient-to-r ${b.gradient} text-white px-6 py-3 rounded-xl font-semibold hover:opacity-90 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98]`,children:c.jsxs("div",{className:"flex items-center gap-2",children:[c.jsx("span",{children:"💰"}),"Add Purchase"]})}),c.jsx("button",{onClick:()=>t("/usage"),className:`bg-gradient-to-r ${b.gradient} text-white px-6 py-3 rounded-xl font-semibold hover:opacity-90 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98]`,children:c.jsxs("div",{className:"flex items-center gap-2",children:[c.jsx("span",{children:"⚡"}),"Record Usage"]})})]})]})]})}function oN(){const[t,e]=_.useState(!1),n=_.useRef(null),{currentTheme:r,setCurrentTheme:i,theme:s}=ce(),a=o=>o?`${s.border} ring-2 ring-opacity-50`:r==="dark"?"border-gray-600 hover:border-gray-500":"border-gray-200 hover:border-gray-300";return _.useEffect(()=>{function o(l){n.current&&!n.current.contains(l.target)&&e(!1)}return document.addEventListener("mousedown",o),()=>{document.removeEventListener("mousedown",o)}},[]),c.jsxs("div",{className:"space-y-8 pb-8 md:pb-6 theme-selector-mobile",style:{paddingBottom:"calc(env(safe-area-inset-bottom, 0px) + 2rem)",minHeight:"auto"},children:[c.jsxs("div",{children:[c.jsxs("h3",{className:`text-lg font-semibold ${s.text} mb-4 flex items-center`,children:[c.jsx(Gp,{className:"mr-2 h-5 w-5"}),"Choose Theme"]}),c.jsxs("div",{className:"relative",ref:n,children:[c.jsxs("button",{onClick:()=>e(!t),className:`w-full p-4 ${s.card} border ${s.border} rounded-lg flex items-center justify-between hover:${s.secondary} transition-colors min-w-0`,children:[c.jsxs("div",{className:"flex items-center space-x-4",children:[c.jsxs("div",{className:"space-y-2",children:[c.jsx("div",{className:`h-6 w-16 ${xt[r].gradient} bg-gradient-to-r rounded`}),c.jsxs("div",{className:"flex space-x-1",children:[c.jsx("div",{className:`h-2 w-2 ${xt[r].primary} rounded`}),c.jsx("div",{className:`h-2 w-2 ${xt[r].accent} rounded`}),c.jsx("div",{className:`h-2 w-2 ${xt[r].secondary} rounded`})]})]}),c.jsx("span",{className:`text-lg font-medium ${s.text}`,children:xt[r].name})]}),c.jsx(ec,{className:`h-5 w-5 ${s.textSecondary} transition-transform ${t?"rotate-180":""}`})]}),t&&c.jsx("div",{className:`absolute top-full left-0 right-0 mt-2 ${s.card} border ${s.border} rounded-lg shadow-lg z-50 max-h-80 overflow-y-auto`,children:c.jsx("div",{className:"grid grid-cols-1 gap-2 p-2 w-full",children:Object.entries(xt).map(([o,l])=>c.jsx("button",{onClick:()=>{i(o),e(!1)},className:`relative p-4 rounded-lg border-2 transition-all hover:scale-105 text-left w-full ${a(r===o)}`,children:c.jsxs("div",{className:"flex items-center space-x-4",children:[c.jsxs("div",{className:"space-y-2 flex-shrink-0",children:[c.jsx("div",{className:`h-8 w-20 ${l.gradient} bg-gradient-to-r rounded`}),c.jsxs("div",{className:"flex space-x-1",children:[c.jsx("div",{className:`h-3 w-3 ${l.primary} rounded`}),c.jsx("div",{className:`h-3 w-3 ${l.accent} rounded`}),c.jsx("div",{className:`h-3 w-3 ${l.secondary} rounded`})]})]}),c.jsx("div",{className:"flex-1",children:c.jsx("p",{className:`text-sm font-medium ${s.text}`,children:l.name})}),r===o&&c.jsx("div",{className:"flex-shrink-0",children:c.jsx(Gb,{className:`h-5 w-5 ${s.text}`})})]})},o))})})]})]}),c.jsxs("div",{children:[c.jsx("h3",{className:`text-lg font-semibold ${s.text} mb-4`,children:"Preview"}),c.jsxs("div",{className:`p-4 ${s.card} rounded-lg border ${s.border} space-y-3 theme-preview-card`,children:[c.jsxs("div",{className:"flex items-center justify-between",children:[c.jsx("h4",{className:`text-lg font-bold ${s.text}`,children:"Sample Dashboard"}),c.jsx("div",{className:`px-2 py-1 ${s.primary} text-white rounded-full text-sm`,children:"Active"})]}),c.jsx("div",{className:"grid grid-cols-1 gap-3",children:c.jsxs("div",{className:`p-3 ${s.card} border ${s.border} rounded-lg shadow-sm h-16`,children:[c.jsx("p",{className:`text-xs ${s.textSecondary}`,children:"Current Units"}),c.jsx("p",{className:`text-lg font-bold ${s.text}`,children:"125.50"})]})}),c.jsxs("div",{className:"flex space-x-2",children:[c.jsx("button",{className:`px-3 py-2 ${s.primary} text-white rounded-lg text-sm h-10`,children:"Primary Button"}),c.jsx("button",{className:`px-3 py-2 border ${s.border} ${s.text} rounded-lg text-sm h-10`,children:"Secondary Button"})]})]})]}),c.jsxs("div",{children:[c.jsx("h3",{className:`text-lg font-semibold ${s.text} mb-4`,children:"Reset Appearance"}),c.jsx("div",{className:"space-y-3",children:c.jsx("button",{onClick:()=>{i("electric"),e(!1)},className:`w-full px-6 py-3 bg-gradient-to-r ${s.gradient} text-white rounded-lg hover:opacity-90 transition-all duration-300 shadow-lg hover:shadow-xl`,children:"Reset to Default Theme"})})]})]})}function lN(){const[t,e]=_.useState(!1),[n,r]=_.useState(!1),[i,s]=_.useState(!1),{state:a,factoryReset:o,dashboardReset:l}=Ke(),{theme:u,currentTheme:d}=ce(),h=(x,v="bg-gray-800/50")=>d==="dark"?v:x,f=async()=>{s(!0);try{o(),e(!1),alert("Factory reset completed successfully! The app will now restart.")}catch(x){console.error("Error during factory reset:",x),alert("Error during factory reset. Please try again.")}finally{s(!1)}},m=async()=>{s(!0);try{l(),r(!1),alert("Dashboard data reset successfully! Your history has been preserved.")}catch(x){console.error("Error during dashboard reset:",x),alert("Error during dashboard reset. Please try again.")}finally{s(!1)}};return c.jsxs("div",{className:"space-y-6 max-w-none",children:[c.jsx("div",{className:`p-4 md:p-6 border ${u.border} rounded-lg ${h("bg-white","bg-gray-800/50")} w-full max-w-none`,children:c.jsxs("div",{className:"w-full",children:[c.jsx("h3",{className:`text-lg font-semibold ${u.text}`,children:"Dashboard Data Reset"}),c.jsx("p",{className:`mt-2 text-sm ${u.textSecondary}`,children:"Reset current units and previous readings to zero. This will clear your dashboard data but preserve your purchase and usage history for reference."}),c.jsxs("div",{className:`mt-4 p-4 ${h("bg-orange-50 border-orange-200","bg-orange-900/20 border-orange-700")} border rounded-lg`,children:[c.jsx("h4",{className:`text-sm font-medium mb-2 ${d==="dark"?"text-orange-300":"text-orange-800"}`,children:"What will be reset:"}),c.jsxs("ul",{className:`text-sm ${d==="dark"?"text-orange-200":"text-orange-700"}`,children:[c.jsxs("li",{className:"flex items-start",children:[c.jsx("span",{className:"mr-3 flex-shrink-0",children:"•"}),c.jsx("span",{children:"Current units will be set to 0"})]}),c.jsxs("li",{className:"flex items-start",children:[c.jsx("span",{className:"mr-3 flex-shrink-0",children:"•"}),c.jsx("span",{children:"Previous units will be set to 0"})]}),c.jsxs("li",{className:"flex items-start",children:[c.jsx("span",{className:"mr-3 flex-shrink-0",children:"•"}),c.jsx("span",{children:"Usage since last recording will be reset"})]})]}),c.jsx("h4",{className:`text-sm font-medium mt-3 mb-2 ${d==="dark"?"text-orange-300":"text-orange-800"}`,children:"What will be preserved:"}),c.jsxs("ul",{className:`text-sm ${d==="dark"?"text-orange-200":"text-orange-700"}`,children:[c.jsxs("li",{className:"flex items-start",children:[c.jsx("span",{className:"mr-3 flex-shrink-0",children:"•"}),c.jsx("span",{children:"All purchase history"})]}),c.jsxs("li",{className:"flex items-start",children:[c.jsx("span",{className:"mr-3 flex-shrink-0",children:"•"}),c.jsx("span",{children:"All usage history"})]}),c.jsxs("li",{className:"flex items-start",children:[c.jsx("span",{className:"mr-3 flex-shrink-0",children:"•"}),c.jsx("span",{children:"Settings and preferences"})]}),c.jsxs("li",{className:"flex items-start",children:[c.jsx("span",{className:"mr-3 flex-shrink-0",children:"•"}),c.jsx("span",{children:"Theme and appearance settings"})]})]})]}),n?c.jsxs("div",{className:"mt-4 space-y-3",children:[c.jsxs("div",{className:`flex items-center p-4 ${h("bg-red-50 border-red-200","bg-red-900/20 border-red-700")} border rounded-lg`,children:[c.jsx(kr,{className:"h-6 w-6 text-red-600 mr-3"}),c.jsx("span",{className:`text-sm ${d==="dark"?"text-red-200":"text-red-800"}`,children:"Are you sure? This action cannot be undone."})]}),c.jsxs("div",{className:"flex space-x-3",children:[c.jsx("button",{onClick:m,disabled:i,className:"px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors disabled:opacity-50",children:i?"Resetting...":"Yes, Reset Dashboard"}),c.jsx("button",{onClick:()=>r(!1),className:`px-4 py-2 border ${u.border} ${u.text} rounded-lg hover:${u.secondary} transition-colors`,children:"Cancel"})]})]}):c.jsx("button",{onClick:()=>r(!0),className:"mt-4 px-6 py-3 text-sm bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors",children:"Reset Dashboard Data"})]})}),c.jsx("div",{className:`p-4 md:p-6 border ${h("border-red-200 bg-red-50","border-red-700 bg-red-900/20")} rounded-lg w-full max-w-none`,children:c.jsxs("div",{className:"w-full",children:[c.jsx("h3",{className:`text-lg font-semibold ${d==="dark"?"text-red-300":"text-red-800"}`,children:"Factory Reset"}),c.jsx("p",{className:`mt-2 text-sm ${d==="dark"?"text-red-200":"text-red-700"}`,children:"Completely reset the app to its initial state. This will delete ALL data including purchases, usage history, and settings. You will need to set up the app again from scratch."}),c.jsxs("div",{className:`mt-4 p-4 ${h("bg-red-100 border-red-300","bg-red-900/30 border-red-600")} border rounded-lg`,children:[c.jsx("h4",{className:`text-sm font-medium mb-2 ${d==="dark"?"text-red-300":"text-red-800"}`,children:"What will be deleted:"}),c.jsxs("ul",{className:`text-sm ${d==="dark"?"text-red-200":"text-red-700"}`,children:[c.jsxs("li",{className:"flex items-start",children:[c.jsx("span",{className:"mr-3 flex-shrink-0",children:"•"}),c.jsx("span",{children:"All purchase records"})]}),c.jsxs("li",{className:"flex items-start",children:[c.jsx("span",{className:"mr-3 flex-shrink-0",children:"•"}),c.jsx("span",{children:"All usage history"})]}),c.jsxs("li",{className:"flex items-start",children:[c.jsx("span",{className:"mr-3 flex-shrink-0",children:"•"}),c.jsx("span",{children:"Current and previous unit readings"})]}),c.jsxs("li",{className:"flex items-start",children:[c.jsx("span",{className:"mr-3 flex-shrink-0",children:"•"}),c.jsx("span",{children:"All settings and preferences"})]}),c.jsxs("li",{className:"flex items-start",children:[c.jsx("span",{className:"mr-3 flex-shrink-0",children:"•"}),c.jsx("span",{children:"Theme and appearance settings"})]})]}),c.jsxs("div",{className:`mt-3 p-3 ${h("bg-red-200 border-red-400","bg-red-900/40 border-red-500")} border rounded text-sm ${d==="dark"?"text-red-200":"text-red-800"}`,children:[c.jsx("strong",{children:"Warning:"})," This action is irreversible. Make sure you have backed up any important data."]})]}),t?c.jsxs("div",{className:"mt-4 space-y-3",children:[c.jsxs("div",{className:`flex items-center p-4 ${h("bg-red-100 border-red-300","bg-red-900/30 border-red-600")} border rounded-lg`,children:[c.jsx(kr,{className:"h-6 w-6 text-red-700 mr-3"}),c.jsx("span",{className:`text-sm font-medium ${d==="dark"?"text-red-200":"text-red-800"}`,children:"This will permanently delete ALL your data. Are you absolutely sure?"})]}),c.jsxs("div",{className:"flex space-x-3",children:[c.jsx("button",{onClick:f,disabled:i,className:"px-4 py-2 bg-red-700 text-white rounded-lg hover:bg-red-800 transition-colors disabled:opacity-50",children:i?"Resetting...":"Yes, Delete Everything"}),c.jsx("button",{onClick:()=>e(!1),className:`px-4 py-2 border ${u.border} ${u.text} rounded-lg hover:${u.secondary} transition-colors`,children:"Cancel"})]})]}):c.jsx("button",{onClick:()=>e(!0),className:"mt-4 px-6 py-3 text-sm bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors",children:"Factory Reset"})]})}),c.jsxs("div",{className:`p-4 md:p-6 ${u.card} border ${u.border} rounded-lg w-full max-w-none`,children:[c.jsx("h3",{className:`text-lg font-semibold ${u.text} mb-4`,children:"Current Data Summary"}),c.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm",children:[c.jsxs("div",{children:[c.jsx("h4",{className:`font-medium ${u.text} mb-2`,children:"App Data"}),c.jsxs("ul",{className:`space-y-3 ${u.textSecondary}`,children:[c.jsxs("li",{className:"flex items-start",children:[c.jsx("span",{className:"mr-3 flex-shrink-0 leading-relaxed",children:"•"}),c.jsxs("span",{className:"leading-relaxed",children:["Current Units: ",a.currentUnits.toFixed(2)]})]}),c.jsxs("li",{className:"flex items-start",children:[c.jsx("span",{className:"mr-3 flex-shrink-0 leading-relaxed",children:"•"}),c.jsxs("span",{className:"leading-relaxed",children:["Previous Units: ",a.previousUnits.toFixed(2)]})]}),c.jsxs("li",{className:"flex items-start",children:[c.jsx("span",{className:"mr-3 flex-shrink-0 leading-relaxed",children:"•"}),c.jsxs("span",{className:"leading-relaxed",children:["Unit Cost: R",a.unitCost.toFixed(2)]})]}),c.jsxs("li",{className:"flex items-start",children:[c.jsx("span",{className:"mr-3 flex-shrink-0 leading-relaxed",children:"•"}),c.jsxs("span",{className:"leading-relaxed",children:["Threshold: ",a.thresholdLimit.toFixed(2)," units"]})]})]})]}),c.jsxs("div",{children:[c.jsx("h4",{className:`font-medium ${u.text} mb-2`,children:"History"}),c.jsxs("ul",{className:`space-y-3 ${u.textSecondary}`,children:[c.jsxs("li",{className:"flex items-start",children:[c.jsx("span",{className:"mr-3 flex-shrink-0 leading-relaxed",children:"•"}),c.jsxs("span",{className:"leading-relaxed",children:["Purchases: ",a.purchases.length," records"]})]}),c.jsxs("li",{className:"flex items-start",children:[c.jsx("span",{className:"mr-3 flex-shrink-0 leading-relaxed",children:"•"}),c.jsxs("span",{className:"leading-relaxed",children:["Usage Records: ",a.usageHistory.length," records"]})]}),c.jsxs("li",{className:"flex items-start",children:[c.jsx("span",{className:"mr-3 flex-shrink-0 leading-relaxed",children:"•"}),c.jsxs("span",{className:"leading-relaxed",children:["Last Reset: ",a.lastResetDate?new Date(a.lastResetDate).toLocaleDateString():"Never"]})]}),c.jsxs("li",{className:"flex items-start",children:[c.jsx("span",{className:"mr-3 flex-shrink-0 leading-relaxed",children:"•"}),c.jsxs("span",{className:"leading-relaxed",children:["App Initialized: ",a.isInitialized?"Yes":"No"]})]})]})]})]})]})]})}function cN(){var L,A;const{state:t,updateSettings:e,testNotification:n}=Ke(),{theme:r,currentTheme:i}=ce(),[s]=Sy(),a=(R,H="bg-gray-800/50")=>i==="dark"?H:R,[o,l]=_.useState(t.unitCost.toString()),[u,d]=_.useState(t.thresholdLimit.toString()),[h,f]=_.useState(t.currency||"ZAR"),[m,x]=_.useState(t.customCurrencyName||""),[v,w]=_.useState(t.customCurrencySymbol||""),[g,p]=_.useState(t.unitName||"kWh"),[y,b]=_.useState(t.customUnitName||""),[S,j]=_.useState(t.notificationsEnabled||!1),[N,k]=_.useState(t.notificationTime||"18:00"),[C,P]=_.useState(!1),[M,D]=_.useState(!1),$=[{code:"ZAR",name:"South African Rand",symbol:"R"},{code:"USD",name:"US Dollar",symbol:"$"},{code:"EUR",name:"Euro",symbol:"€"},{code:"GBP",name:"British Pound",symbol:"£"},{code:"JPY",name:"Japanese Yen",symbol:"¥"},{code:"CUSTOM",name:"Custom Currency",symbol:"C"}],U=[{value:"kWh",label:"kWh (Kilowatt Hours)"},{value:"Units",label:"Units"},{value:"custom",label:"Custom"}],z=async R=>{R.preventDefault(),P(!0);try{const H=parseFloat(o),ze=parseFloat(u);if(isNaN(H)||H<=0){alert("Please enter a valid unit cost (greater than 0)");return}if(isNaN(ze)||ze<0){alert("Please enter a valid threshold limit (0 or greater)");return}if(g==="custom"&&!y.trim()){alert("Please enter a custom unit name");return}if(h==="CUSTOM"&&(!m.trim()||!v.trim())){alert("Please enter both custom currency name and symbol");return}const pe=$.find(Se=>Se.code===h),dt=h==="CUSTOM"?v:(pe==null?void 0:pe.symbol)||"R";e({unitCost:H,thresholdLimit:ze,currency:h,currencySymbol:dt,customCurrencyName:h==="CUSTOM"?m.trim():"",customCurrencySymbol:h==="CUSTOM"?v.trim():"",unitName:g,customUnitName:g==="custom"?y.trim():"",notificationsEnabled:S,notificationTime:N}),alert("Settings saved successfully!")}catch(H){console.error("Error saving settings:",H),alert("Error saving settings. Please try again.")}finally{P(!1)}},B=async()=>{D(!0);try{const R=await n();alert(R?"Test notification sent! Check your notification panel.":"Unable to send test notification. Please check your notification permissions in device settings.")}catch(R){console.error("Test notification error:",R),alert("Error sending test notification. Please try again.")}finally{D(!1)}},[Y,T]=_.useState("general");return _.useEffect(()=>{const R=s.get("section");R&&["general","appearance","reset"].includes(R)&&T(R)},[s]),c.jsx("div",{className:"space-y-6 pb-8 md:pb-6 settings-page-android",children:c.jsx("div",{className:`${r.card} rounded-2xl shadow-lg border ${r.border} w-full settings-content`,children:c.jsxs("div",{className:"p-4 md:p-6",children:[Y==="general"&&c.jsx("div",{className:"space-y-6",children:c.jsxs("form",{onSubmit:z,className:"space-y-6",children:[c.jsxs("div",{className:`p-4 md:p-6 ${r.card} rounded-xl border ${r.border} shadow-sm w-full`,children:[c.jsxs("div",{className:"flex items-center mb-4",children:[c.jsx("div",{className:`p-2 rounded-lg bg-gradient-to-br ${r.gradient} mr-3`,children:c.jsx(Zb,{className:"h-5 w-5 text-white"})}),c.jsx("h3",{className:`font-semibold ${r.text} text-lg`,children:"Currency Settings"})]}),c.jsxs("div",{children:[c.jsx("label",{htmlFor:"currency",className:`block text-sm font-semibold ${r.text} mb-3`,children:"💰 Currency"}),c.jsx("select",{id:"currency",value:h,onChange:R=>f(R.target.value),className:`w-full px-4 py-4 border-4 ${r.border} rounded-xl focus:ring-4 focus:ring-opacity-50 focus:${r.border} ${r.card} ${r.text} font-bold shadow-lg hover:shadow-xl transition-all duration-200 min-w-0`,children:$.map(R=>c.jsxs("option",{value:R.code,children:[R.symbol," - ",R.name," (",R.code,")"]},R.code))}),c.jsx("p",{className:`mt-3 text-xs ${r.textSecondary} opacity-80 font-medium`,children:"💡 Select your preferred currency for cost calculations"}),h==="CUSTOM"&&c.jsxs("div",{className:"mt-4 space-y-4",children:[c.jsxs("div",{children:[c.jsx("label",{htmlFor:"customCurrencyName",className:`block text-sm font-semibold ${r.text} mb-2`,children:"🏷️ Custom Currency Name"}),c.jsx("input",{type:"text",id:"customCurrencyName",value:m,onChange:R=>x(R.target.value),placeholder:"Enter currency name (e.g., Bitcoin, Credits, Points)",className:`w-full px-4 py-3 border-4 ${r.border} rounded-xl focus:ring-4 focus:ring-opacity-50 focus:${r.border} ${r.card} ${r.text} ${r.textSecondary} font-bold shadow-lg hover:shadow-xl transition-all duration-200 min-w-0`,required:h==="CUSTOM"})]}),c.jsxs("div",{children:[c.jsx("label",{htmlFor:"customCurrencySymbol",className:`block text-sm font-semibold ${r.text} mb-2`,children:"💰 Custom Currency Symbol"}),c.jsx("input",{type:"text",id:"customCurrencySymbol",value:v,onChange:R=>w(R.target.value),placeholder:"Enter symbol (e.g., ₿, Cr, Pts)",maxLength:"5",className:`w-full px-4 py-3 border-4 ${r.border} rounded-xl focus:ring-4 focus:ring-opacity-50 focus:${r.border} ${r.card} ${r.text} ${r.textSecondary} font-bold shadow-lg hover:shadow-xl transition-all duration-200 min-w-0`,required:h==="CUSTOM"}),c.jsxs("p",{className:`mt-3 text-xs ${r.textSecondary} opacity-80 font-medium`,children:['💰 This symbol will be displayed with all amounts (e.g., "',v||"Cr",'100.00")']})]})]})]})]}),c.jsxs("div",{className:`p-4 md:p-6 ${a("bg-gradient-to-br from-blue-50 to-indigo-50","bg-gray-800/50")} rounded-xl border ${r.border} shadow-sm w-full`,children:[c.jsxs("div",{className:"flex items-center mb-4",children:[c.jsx("div",{className:"p-2 rounded-lg bg-gradient-to-br from-blue-400 to-indigo-500 mr-3",children:c.jsx(De,{className:"h-5 w-5 text-white"})}),c.jsx("h3",{className:`font-semibold ${r.text} text-lg`,children:"Unit Settings"})]}),c.jsxs("div",{className:"space-y-4",children:[c.jsxs("div",{children:[c.jsx("label",{htmlFor:"unitName",className:`block text-sm font-semibold ${r.text} mb-3`,children:"⚡ Unit Name"}),c.jsx("select",{id:"unitName",value:g,onChange:R=>p(R.target.value),className:`w-full px-4 py-4 border-4 border-blue-300 rounded-xl focus:ring-4 focus:ring-blue-500 focus:border-blue-600 ${r.card} ${r.text} font-bold shadow-lg hover:shadow-xl transition-all duration-200 hover:border-blue-400 min-w-0`,children:U.map(R=>c.jsx("option",{value:R.value,children:R.label},R.value))}),c.jsx("p",{className:`mt-3 text-xs ${r.textSecondary} opacity-80 font-medium`,children:"⚡ Choose how your units are displayed throughout the app"})]}),g==="custom"&&c.jsxs("div",{children:[c.jsx("label",{htmlFor:"customUnitName",className:`block text-sm font-semibold ${r.text} mb-3`,children:"🎯 Custom Unit Name"}),c.jsx("input",{type:"text",id:"customUnitName",value:y,onChange:R=>b(R.target.value),placeholder:"Enter custom unit name (e.g., Donkey, Credits, Points)",className:`w-full px-4 py-4 border-4 border-purple-300 rounded-xl focus:ring-4 focus:ring-purple-500 focus:border-purple-600 ${r.card} ${r.text} placeholder-gray-400 font-bold shadow-lg hover:shadow-xl transition-all duration-200 hover:border-purple-400 min-w-0`,required:g==="custom"}),c.jsxs("p",{className:`mt-3 text-xs ${r.textSecondary} opacity-80 font-medium`,children:['🎯 This name will be used everywhere (e.g., "Cost per ',y||"YourUnit",'")']})]})]})]}),c.jsxs("div",{className:`p-4 md:p-6 ${a("bg-gradient-to-br from-violet-50 to-purple-50","bg-gray-800/50")} rounded-xl border ${r.border} shadow-sm w-full`,children:[c.jsxs("div",{className:"flex items-center mb-4",children:[c.jsx("div",{className:"p-2 rounded-lg bg-gradient-to-br from-violet-400 to-purple-500 mr-3",children:c.jsx(Te,{className:"h-5 w-5 text-white"})}),c.jsx("h3",{className:`font-semibold ${r.text} text-lg`,children:"Cost Settings"})]}),c.jsxs("div",{children:[c.jsxs("label",{htmlFor:"unitCost",className:`block text-sm font-semibold ${r.text} mb-3`,children:["💵 Cost per ",g==="custom"?y||"Unit":g]}),c.jsxs("div",{className:"relative",children:[c.jsx("div",{className:"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none",children:c.jsx("div",{className:"p-1 rounded-lg bg-gradient-to-br from-violet-400 to-purple-500",children:c.jsx(Te,{className:"h-4 w-4 text-white"})})}),c.jsx("input",{type:"number",id:"unitCost",value:o,onChange:R=>l(R.target.value),onWheel:zr,step:"0.01",min:"0.01",placeholder:"Enter cost per unit",className:`w-full pl-12 pr-4 py-4 border-4 border-violet-300 rounded-xl focus:ring-4 focus:ring-violet-500 focus:border-violet-600 ${r.card} ${r.text} placeholder-gray-400 font-bold shadow-lg hover:shadow-xl transition-all duration-200 hover:border-violet-400 min-w-0`,required:!0})]}),c.jsx("p",{className:`mt-3 text-xs ${r.textSecondary} opacity-80 font-medium`,children:"💡 This is used to calculate the cost of your electricity usage"})]})]}),c.jsxs("div",{className:`p-4 md:p-6 ${a("bg-gradient-to-br from-amber-50 to-yellow-50","bg-gray-800/50")} rounded-xl border ${r.border} shadow-sm w-full`,children:[c.jsxs("div",{className:"flex items-center mb-4",children:[c.jsx("div",{className:"p-2 rounded-lg bg-gradient-to-br from-amber-400 to-yellow-500 mr-3",children:c.jsx(kr,{className:"h-5 w-5 text-white"})}),c.jsx("h3",{className:`font-semibold ${r.text} text-lg`,children:"Alert Settings"})]}),c.jsxs("div",{children:[c.jsxs("label",{htmlFor:"thresholdLimit",className:`block text-sm font-semibold ${r.text} mb-3`,children:["⚠️ Low ",g==="custom"?y||"Units":g," Warning Threshold"]}),c.jsxs("div",{className:"relative",children:[c.jsx("div",{className:"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none",children:c.jsx("div",{className:"p-1 rounded-lg bg-gradient-to-br from-amber-400 to-yellow-500",children:c.jsx(kr,{className:"h-4 w-4 text-white"})})}),c.jsx("input",{type:"number",id:"thresholdLimit",value:u,onChange:R=>d(R.target.value),onWheel:zr,step:"0.01",min:"0",placeholder:"Enter low units warning threshold",className:`w-full pl-12 pr-4 py-4 border-4 border-amber-300 rounded-xl focus:ring-4 focus:ring-amber-500 focus:border-amber-600 ${r.card} ${r.text} placeholder-gray-400 font-bold shadow-lg hover:shadow-xl transition-all duration-200 hover:border-amber-400 min-w-0`,required:!0})]}),c.jsx("p",{className:`mt-3 text-xs ${r.textSecondary} opacity-80 font-medium`,children:"⚠️ You'll receive a warning when your remaining units drop below this threshold"})]})]}),c.jsxs("div",{className:`p-4 md:p-6 ${a("bg-gradient-to-br from-slate-50 to-gray-50","bg-gray-800/50")} rounded-xl border ${r.border} shadow-sm w-full`,children:[c.jsxs("div",{className:"flex items-center mb-4",children:[c.jsx("div",{className:"p-2 rounded-lg bg-gradient-to-br from-gray-400 to-slate-500 mr-3",children:c.jsx(Ki,{className:"h-5 w-5 text-white"})}),c.jsx("h3",{className:`font-semibold ${r.text} text-lg`,children:"Current Settings Preview"})]}),c.jsxs("div",{className:"space-y-3 text-sm",children:[c.jsxs("div",{className:`flex justify-between items-center p-3 ${a("bg-white/60","bg-gray-700/50")} rounded-lg`,children:[c.jsx("span",{className:`${r.textSecondary} font-medium`,children:"Currency:"}),c.jsx("span",{className:`${r.text} font-bold`,children:t.currency==="CUSTOM"?`${t.customCurrencySymbol||"C"} - ${t.customCurrencyName||"Custom Currency"}`:`${((L=$.find(R=>R.code===(t.currency||"ZAR")))==null?void 0:L.symbol)||"R"} - ${((A=$.find(R=>R.code===(t.currency||"ZAR")))==null?void 0:A.name)||"South African Rand"}`})]}),c.jsxs("div",{className:`flex justify-between items-center p-3 ${a("bg-white/60","bg-gray-700/50")} rounded-lg`,children:[c.jsx("span",{className:`${r.textSecondary} font-medium`,children:"Unit Name:"}),c.jsx("span",{className:`${r.text} font-bold`,children:t.unitName==="custom"?t.customUnitName||"Units":t.unitName||"kWh"})]}),c.jsxs("div",{className:`flex justify-between items-center p-3 ${a("bg-white/60","bg-gray-700/50")} rounded-lg`,children:[c.jsx("span",{className:`${r.textSecondary} font-medium`,children:"Unit Cost:"}),c.jsxs("span",{className:`${r.text} font-bold`,children:[t.currencySymbol||"R",t.unitCost.toFixed(2)," per ",t.unitName==="custom"?t.customUnitName||"Unit":t.unitName||"kWh"]})]}),c.jsxs("div",{className:`flex justify-between items-center p-3 ${a("bg-white/60","bg-gray-700/50")} rounded-lg`,children:[c.jsx("span",{className:`${r.textSecondary} font-medium`,children:"Low Units Warning:"}),c.jsxs("span",{className:`${r.text} font-bold`,children:[t.thresholdLimit.toFixed(2)," ",t.unitName==="custom"?t.customUnitName||"Units":t.unitName||"kWh"]})]}),c.jsxs("div",{className:`flex justify-between items-center p-3 ${a("bg-white/60","bg-gray-700/50")} rounded-lg`,children:[c.jsx("span",{className:`${r.textSecondary} font-medium`,children:"Last Reset:"}),c.jsx("span",{className:`${r.text} font-bold`,children:t.lastResetDate?new Date(t.lastResetDate).toLocaleDateString():"Never"})]})]})]}),c.jsxs("div",{className:`p-4 md:p-6 ${a("bg-gradient-to-br from-indigo-50 to-purple-50","bg-gray-800/50")} rounded-xl border ${r.border} shadow-sm w-full`,children:[c.jsxs("div",{className:"flex items-center mb-4",children:[c.jsx("div",{className:"p-2 rounded-lg bg-gradient-to-br from-indigo-400 to-purple-500 mr-3",children:c.jsx(kr,{className:"h-5 w-5 text-white"})}),c.jsx("h3",{className:`font-semibold ${r.text} text-lg`,children:"Notification Settings"})]}),c.jsxs("div",{className:"space-y-4",children:[c.jsxs("div",{className:"flex items-center justify-between",children:[c.jsxs("div",{children:[c.jsx("label",{className:`text-sm font-semibold ${r.text}`,children:"🔔 Daily Usage Reminders"}),c.jsx("p",{className:`text-xs ${r.textSecondary} opacity-80 mt-3`,children:"Get reminded to record your electricity usage every day"})]}),c.jsxs("label",{className:"relative inline-flex items-center cursor-pointer",children:[c.jsx("input",{type:"checkbox",checked:S,onChange:R=>j(R.target.checked),className:"sr-only peer","aria-label":"Enable daily usage reminder notifications"}),c.jsx("div",{className:`w-11 h-6 ${i==="dark"?"bg-gray-600":"bg-gray-200"} peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-indigo-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600`})]})]}),S&&c.jsxs("div",{children:[c.jsx("label",{htmlFor:"notificationTime",className:`block text-sm font-semibold ${r.text} mb-3`,children:"⏰ Reminder Time"}),c.jsx("input",{type:"time",id:"notificationTime",value:N,onChange:R=>k(R.target.value),className:`w-full px-4 py-4 border-4 border-indigo-300 rounded-xl focus:ring-4 focus:ring-indigo-500 focus:border-indigo-600 ${r.card} ${r.text} font-bold shadow-lg hover:shadow-xl transition-all duration-200 hover:border-indigo-400 min-w-0`}),c.jsx("p",{className:`mt-3 text-xs ${r.textSecondary} opacity-80 font-medium`,children:"⏰ You'll receive a notification at this time every day"})]}),S&&c.jsxs("div",{className:"pt-4 border-t border-gray-200 dark:border-gray-600",children:[c.jsx("button",{type:"button",onClick:B,disabled:M,className:"w-full px-4 py-3 bg-gradient-to-r from-indigo-500 to-purple-600 text-white rounded-lg font-semibold hover:opacity-90 transition-all duration-300 focus:ring-4 focus:ring-indigo-300 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-xl",children:c.jsx("div",{className:"flex items-center justify-center gap-2",children:M?c.jsxs(c.Fragment,{children:[c.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"}),"Sending Test..."]}):c.jsx(c.Fragment,{children:"🔔 Test Notification"})})}),c.jsx("p",{className:`mt-2 text-xs ${r.textSecondary} opacity-80 text-center`,children:"Send a test notification to verify everything is working"})]})]})]}),c.jsx("button",{type:"submit",disabled:C,className:`w-full bg-gradient-to-r ${r.gradient} text-white py-4 px-6 rounded-xl font-semibold hover:opacity-90 transition-all duration-300 focus:ring-4 focus:ring-opacity-50 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98]`,children:c.jsx("div",{className:"flex items-center justify-center gap-2",children:C?c.jsxs(c.Fragment,{children:[c.jsx("div",{className:"animate-spin rounded-full h-5 w-5 border-2 border-white border-t-transparent"}),"Saving Settings..."]}):c.jsxs(c.Fragment,{children:[c.jsx(Ki,{className:"h-5 w-5"}),"Save Settings"]})})})]})}),Y==="appearance"&&c.jsx("div",{className:"space-y-6 pb-8 md:pb-6",children:c.jsx(oN,{})}),Y==="reset"&&c.jsx("div",{className:"space-y-6",children:c.jsx(lN,{})})]})})})}function uN(){const[t,e]=_.useState(""),[n,r]=_.useState(""),[i,s]=_.useState(""),{initializeApp:a,state:o}=Ke(),{theme:l}=ce(),u=d=>{d.preventDefault(),s("");const h=parseFloat(t),f=parseFloat(n);if(isNaN(h)||h<0){s("Please enter a valid number of units (0 or greater)");return}if(isNaN(f)||f<=0){s("Please enter a valid cost per unit (greater than 0)");return}a(h,f)};return c.jsx("div",{className:`min-h-screen ${l.background}`,children:c.jsx("div",{className:"h-screen overflow-y-auto",children:c.jsx("div",{className:"container mx-auto px-4 py-4",children:c.jsxs("div",{className:`max-w-lg mx-auto ${l.card} rounded-2xl shadow-2xl p-6 border ${l.border}`,children:[c.jsxs("div",{className:"text-center mb-6",children:[c.jsx("div",{className:"flex justify-center mb-4",children:c.jsxs("div",{className:"h-16 w-16 flex items-center justify-center",children:[c.jsx("img",{src:"/Logo Prepaid User.png",alt:"Prepaid User Electricity Logo",className:"h-16 w-16 object-contain",onError:d=>{d.target.style.display="none",d.target.nextElementSibling.style.display="flex"}}),c.jsx("div",{className:"h-16 w-16 rounded-full bg-blue-600 flex items-center justify-center shadow-2xl border-4 border-white",style:{display:"none"},children:c.jsx("span",{className:"text-white text-2xl font-bold",children:"⚡"})})]})}),c.jsx("h1",{className:`text-2xl font-bold ${l.text} mb-3`,children:"Welcome to Prepaid Meter App"}),c.jsx("p",{className:`${l.textSecondary} text-base mb-2`,children:"Let's get started by setting up your initial meter reading and cost settings"})]}),c.jsxs("form",{onSubmit:u,className:"space-y-4",children:[c.jsxs("div",{children:[c.jsx("label",{htmlFor:"initialUnits",className:`block text-sm font-medium ${l.text} mb-2`,children:"Initial Unit Value"}),c.jsx("input",{type:"number",id:"initialUnits",value:t,onChange:d=>e(d.target.value),onWheel:zr,step:"0.01",min:"0",placeholder:"Enter your current meter reading",className:`w-full px-3 py-3 border-2 ${l.border} rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${l.card} ${l.text} transition-all duration-200 hover:border-blue-300`,required:!0})]}),c.jsxs("div",{children:[c.jsxs("label",{htmlFor:"unitCost",className:`block text-sm font-medium ${l.text} mb-2`,children:["Cost per Unit (",o.currencySymbol||"R",")"]}),c.jsx("input",{type:"number",id:"unitCost",value:n,onChange:d=>r(d.target.value),onWheel:zr,step:"0.01",min:"0.01",placeholder:"Enter cost per unit (e.g., 2.50)",className:`w-full px-3 py-3 border-2 ${l.border} rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${l.card} ${l.text} transition-all duration-200 hover:border-blue-300`,required:!0})]}),i&&c.jsx("div",{className:"p-3 bg-red-50 border border-red-200 rounded-lg",children:c.jsx("p",{className:"text-sm text-red-600",children:i})}),(t||n)&&c.jsxs("div",{className:"mt-4 space-y-3",children:[c.jsxs("div",{className:`relative overflow-hidden rounded-xl ${l.card} border ${l.border} p-4 shadow-lg`,children:[c.jsx("div",{className:`absolute inset-0 ${l.secondary}`}),c.jsx("div",{className:"relative",children:c.jsxs("div",{className:"flex items-center justify-between",children:[c.jsxs("div",{className:"flex-1",children:[c.jsx("div",{className:`text-xs font-semibold ${l.textSecondary} tracking-wider uppercase mb-2`,children:"STARTING UNITS"}),c.jsx("p",{className:`text-xl font-bold ${l.text} mb-1`,children:parseFloat(t||0).toFixed(2)}),c.jsx("p",{className:`text-xs font-medium ${l.textSecondary}`,children:"Initial meter reading"})]}),c.jsx("div",{className:`p-2 rounded-lg bg-gradient-to-br ${l.gradient} shadow-lg ml-3`,children:c.jsx("span",{className:"text-white text-lg",children:"⚡"})})]})})]}),c.jsxs("div",{className:`relative overflow-hidden rounded-xl ${l.card} border ${l.border} p-4 shadow-lg`,children:[c.jsx("div",{className:`absolute inset-0 ${l.secondary}`}),c.jsx("div",{className:"relative",children:c.jsxs("div",{className:"flex items-center justify-between",children:[c.jsxs("div",{className:"flex-1",children:[c.jsx("div",{className:`text-xs font-semibold ${l.textSecondary} tracking-wider uppercase mb-2`,children:"UNIT COST"}),c.jsxs("p",{className:`text-xl font-bold ${l.text} mb-1`,children:[o.currencySymbol||"R",parseFloat(n||0).toFixed(2)]}),c.jsx("p",{className:`text-xs font-medium ${l.textSecondary}`,children:"Per unit rate"})]}),c.jsx("div",{className:`p-2 rounded-lg bg-gradient-to-br ${l.gradient} shadow-lg ml-3`,children:c.jsx("span",{className:"text-white text-lg",children:"💰"})})]})})]})]}),c.jsx("button",{type:"submit",className:`w-full bg-gradient-to-r ${l.gradient} text-white py-3 px-6 rounded-xl font-semibold hover:opacity-90 transition-all duration-200 focus:ring-2 focus:ring-opacity-50 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5`,children:"🚀 Initialize App"})]}),c.jsx("div",{className:"mt-4 text-center",children:c.jsx("p",{className:`text-xs ${l.textSecondary}`,children:"You can always change these values later in Settings"})})]})})})})}function dN(){const[t,e]=_.useState(!1),{state:n}=Ke(),{theme:r,currentTheme:i}=ce(),s=$t(),a=()=>({electric:"safe-area-electric",green:"safe-area-green",teal:"safe-area-teal",pink:"safe-area-pink",dark:"safe-area-dark"})[i]||"safe-area-electric";return _.useEffect(()=>{const o=l=>{l.target.type==="number"&&document.activeElement===l.target&&l.preventDefault()};return document.addEventListener("wheel",o,{passive:!1}),()=>{document.removeEventListener("wheel",o)}},[]),_.useEffect(()=>{const o=l=>{t&&!l.target.closest(".sidebar-container")&&!l.target.closest(".hamburger-menu")&&e(!1)};return document.addEventListener("mousedown",o),()=>{document.removeEventListener("mousedown",o)}},[t]),_.useEffect(()=>{e(!1)},[s.pathname]),_.useEffect(()=>{e(!1);const o=()=>{window.innerWidth<1024&&e(!1)};return window.addEventListener("resize",o),()=>window.removeEventListener("resize",o)},[]),n.isInitialized?c.jsxs("div",{className:`flex flex-col h-screen ${r.background}`,style:{height:"100vh"},children:[c.jsx("div",{className:`${a()} safe-top mobile-safe-top flex-shrink-0`}),c.jsxs("div",{className:"flex flex-1 overflow-hidden",children:[t&&c.jsx("div",{className:"fixed inset-0 bg-black/50 z-40 lg:hidden",onClick:()=>e(!1),style:{zIndex:40}}),c.jsx(a1,{isOpen:t,onClose:()=>e(!1)}),c.jsxs("div",{className:"flex flex-col flex-1 overflow-hidden",children:[c.jsx("div",{className:"flex-shrink-0",children:c.jsx(s1,{onMenuClick:()=>e(o=>!o)})}),c.jsx("main",{className:"flex-1 overflow-y-auto overflow-x-hidden px-4 lg:px-8 pt-2 safe-bottom-nav mobile-safe-bottom-nav mobile-content",style:{WebkitOverflowScrolling:"touch",touchAction:"pan-y",overscrollBehavior:"contain"},children:c.jsx("div",{className:"w-full pb-20",children:c.jsxs(oy,{children:[c.jsx(Dn,{path:"/",element:c.jsx(df,{})}),c.jsx(Dn,{path:"/dashboard",element:c.jsx(df,{})}),c.jsx(Dn,{path:"/purchases",element:c.jsx(nN,{})}),c.jsx(Dn,{path:"/usage",element:c.jsx(iN,{})}),c.jsx(Dn,{path:"/history",element:c.jsx(aN,{})}),c.jsx(Dn,{path:"/settings",element:c.jsx(cN,{})})]})})}),c.jsx("div",{className:"flex-shrink-0 lg:hidden",children:c.jsx(o1,{})})]})]}),c.jsx("div",{className:`${a()} safe-bottom mobile-safe-bottom flex-shrink-0`})]}):c.jsx(uN,{})}const hf=Wr("App",{web:()=>iu(()=>import("./web-0m_Y08Ay.js"),[]).then(t=>new t.AppWeb)}),hN=()=>{const t=tr(),e=$t();_.useEffect(()=>{if(!vt.isNativePlatform()||vt.getPlatform()!=="android")return;const n=()=>{const i=e.pathname,s={"/purchases":"/","/usage":"/","/history":"/","/settings":"/"};if(s[i]){t(s[i]);return}if(i==="/"){hf.exitApp();return}t("/")},r=hf.addListener("backButton",n);return()=>{r.remove()}},[t,e.pathname])};var ff;(function(t){t.Dark="DARK",t.Light="LIGHT",t.Default="DEFAULT"})(ff||(ff={}));var mf;(function(t){t.None="NONE",t.Slide="SLIDE",t.Fade="FADE"})(mf||(mf={}));const ar=Wr("StatusBar");var pf;(function(t){t.WHITE="#FFFFFF",t.BLACK="#000000",t.TRANSPARENT="transparent"})(pf||(pf={}));const gf=Wr("NavigationBar",{web:()=>iu(()=>import("./web-Gg-mp-Zu.js"),[]).then(t=>new t.NavigationBarWeb)});function fN(){const{currentTheme:t}=ce();_.useEffect(()=>{if(!vt.isNativePlatform())return;(async()=>{try{if(await ar.setOverlaysWebView({overlay:!1}),t==="dark")await ar.setStyle({style:"LIGHT"}),await ar.setBackgroundColor({color:"#111827"});else{await ar.setStyle({style:"LIGHT"});const r={electric:"#3B82F6",green:"#10B981",teal:"#14B8A6",pink:"#EC4899"}[t]||"#3B82F6";await ar.setBackgroundColor({color:r})}if(await ar.show(),t==="dark")await gf.setColor({color:"#111827",darkButtons:!1});else{const r={electric:"#3B82F6",green:"#10B981",teal:"#14B8A6",pink:"#EC4899"}[t]||"#3B82F6";await gf.setColor({color:r,darkButtons:!1})}}catch(n){console.log("StatusBar or NavigationBar plugin not available:",n)}})()},[t])}function mN(){return hN(),fN(),c.jsx(dN,{})}function pN(){return c.jsx(gy,{children:c.jsx(Wb,{children:c.jsx(Hb,{children:c.jsx(mN,{})})})})}tl.createRoot(document.getElementById("root")).render(c.jsx(bt.StrictMode,{children:c.jsx(pN,{})}));export{Bp as W};
