import { useEffect, useRef } from 'react';
import { LocalNotifications } from '@capacitor/local-notifications';
import { Capacitor } from '@capacitor/core';

export const useNotifications = (notificationsEnabled, notificationTime, lastNotificationDate, dispatch) => {
  const intervalRef = useRef(null);
  const listenersSetupRef = useRef(false);

  // Add immediate logging to verify hook is being called
  console.log('🔔 useNotifications hook called with:', {
    enabled: notificationsEnabled,
    time: notificationTime,
    lastDate: lastNotificationDate,
    platform: typeof window !== 'undefined' ? 'web' : 'unknown'
  });

  useEffect(() => {
    console.log('🔔 useNotifications useEffect triggered with:', {
      enabled: notificationsEnabled,
      time: notificationTime,
      lastDate: lastNotificationDate
    });

    // Clear any existing intervals
    if (intervalRef.current) {
      console.log('🔔 Clearing existing interval:', intervalRef.current);
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }

    if (!notificationsEnabled) {
      console.log('🔔 Notifications disabled, cleaning up and exiting');
      return;
    }

    const setupNotifications = async () => {
      try {
        console.log('Notifications: Setting up for platform:', Capacitor.getPlatform());

        // Setup for native platforms (Android/iOS)
        if (Capacitor.isNativePlatform()) {
          console.log('Notifications: Native platform detected');
          await setupNativeNotifications();
        } else {
          console.log('Notifications: Web platform detected, using fallback');
          setupWebNotifications();
        }

      } catch (error) {
        console.error('Notifications: Setup error:', error);
        // Always fallback to web notifications on error
        setupWebNotifications();
      }
    };

    const setupNativeNotifications = async () => {
      try {
        // Request permissions for native platforms
        console.log('Notifications: Requesting permissions...');
        const permission = await LocalNotifications.requestPermissions();

        console.log('Notifications: Permission result:', permission);

        if (permission.display !== 'granted') {
          console.log('Notifications: Permission denied');
          return;
        }

        console.log('Notifications: Permission granted, setting up daily reminder');
        await scheduleDailyNotification();

        // Setup listeners only once
        if (!listenersSetupRef.current) {
          await setupNotificationListeners();
          listenersSetupRef.current = true;
        }

      } catch (error) {
        console.error('Notifications: Native setup error:', error);
        throw error;
      }
    };

    const scheduleDailyNotification = async () => {
      try {
        console.log('Notifications: Scheduling daily notification...');

        // Cancel any existing notifications first
        try {
          await LocalNotifications.cancel({ notifications: [{ id: 1 }] });
          console.log('Notifications: Cancelled existing notifications');
        } catch (cancelError) {
          console.log('Notifications: No existing notifications to cancel');
        }

        // Parse notification time
        const [hours, minutes] = notificationTime.split(':').map(Number);
        console.log('Notifications: Parsed time:', hours, ':', minutes);

        // Create notification schedule for today
        const now = new Date();
        const scheduledTime = new Date();
        scheduledTime.setHours(hours, minutes, 0, 0);

        // If the time has passed today, schedule for tomorrow
        if (scheduledTime <= now) {
          scheduledTime.setDate(scheduledTime.getDate() + 1);
          console.log('Notifications: Time has passed today, scheduling for tomorrow');
        }

        console.log('Notifications: Scheduling for:', scheduledTime.toLocaleString());

        const notificationConfig = {
          notifications: [
            {
              title: '⚡ Prepaid Meter Reminder',
              body: "Don't forget to record your electricity usage today!",
              id: 1,
              schedule: {
                at: scheduledTime,
                repeats: true,
                every: 'day'
              },
              sound: 'default',
              attachments: null,
              actionTypeId: '',
              extra: {
                type: 'daily_reminder'
              }
            }
          ]
        };

        await LocalNotifications.schedule(notificationConfig);
        console.log('Notifications: Daily reminder scheduled successfully for', scheduledTime.toLocaleString());

        // Verify the notification was scheduled
        const pending = await LocalNotifications.getPending();
        console.log('Notifications: Pending notifications:', pending.notifications.length);

      } catch (error) {
        console.error('Notifications: Scheduling error:', error);
        throw error;
      }
    };

    const setupWebNotifications = () => {
      console.log('Notifications: Setting up web notifications');

      // Request permission immediately if not already granted
      const requestPermission = async () => {
        if ('Notification' in window) {
          console.log('🔔 Current permission status:', Notification.permission);

          if (Notification.permission === 'default') {
            console.log('🔔 Requesting notification permission...');
            try {
              const permission = await Notification.requestPermission();
              console.log('🔔 Permission request result:', permission);
              return permission === 'granted';
            } catch (error) {
              console.error('🔔 Permission request failed:', error);
              return false;
            }
          }

          const isGranted = Notification.permission === 'granted';
          console.log('🔔 Permission already set, granted:', isGranted);
          return isGranted;
        }
        console.log('🔔 Notifications not supported in this browser');
        return false;
      };

      // Request permission on setup and show result
      requestPermission().then(granted => {
        console.log('🔔 Final permission status:', granted ? 'GRANTED' : 'DENIED');
        if (!granted) {
          console.warn('🔔 ⚠️ Notifications will not work - permission denied or not supported');
        }
      });

      // Fallback for web browsers
      const checkNotification = () => {
        const now = new Date();
        const [hours, minutes] = notificationTime.split(':').map(Number);
        const notificationTimeToday = new Date();
        notificationTimeToday.setHours(hours, minutes, 0, 0);

        // Create a 5-minute window around the notification time
        const notificationStart = new Date(notificationTimeToday);
        notificationStart.setMinutes(notificationStart.getMinutes() - 2);
        const notificationEnd = new Date(notificationTimeToday);
        notificationEnd.setMinutes(notificationEnd.getMinutes() + 3);

        const today = now.toDateString();
        const lastNotificationDateStr = lastNotificationDate ? new Date(lastNotificationDate).toDateString() : null;

        console.log('🔔 WEB NOTIFICATION CHECK:', {
          currentTime: now.toLocaleTimeString(),
          targetTime: notificationTimeToday.toLocaleTimeString(),
          windowStart: notificationStart.toLocaleTimeString(),
          windowEnd: notificationEnd.toLocaleTimeString(),
          today: today,
          lastNotificationDate: lastNotificationDateStr,
          inWindow: now >= notificationStart && now <= notificationEnd,
          notSentToday: lastNotificationDateStr !== today,
          permission: typeof Notification !== 'undefined' ? Notification.permission : 'not-supported'
        });

        // Check if we're in the notification time window and haven't sent one today
        if (now >= notificationStart && now <= notificationEnd && lastNotificationDateStr !== today) {
          console.log('Notifications: Conditions met, attempting to send notification');

          // Send notification if permission is granted
          if ('Notification' in window && Notification.permission === 'granted') {
            console.log('Notifications: Sending web notification');

            const notification = new Notification('⚡ Prepaid Meter Reminder', {
              body: "Don't forget to record your electricity usage today!",
              icon: '/favicon.ico',
              badge: '/favicon.ico',
              tag: 'daily-reminder', // Prevent duplicate notifications
              requireInteraction: false,
              silent: false
            });

            // Auto-close notification after 10 seconds
            setTimeout(() => {
              notification.close();
            }, 10000);

            // Update last notification date
            dispatch({
              type: 'UPDATE_SETTINGS',
              payload: { lastNotificationDate: now.toISOString() }
            });

            console.log('Notifications: Web notification sent successfully');
          } else {
            console.log('Notifications: Web notification permission not granted');
          }
        }
      };

      // Check immediately and then every 30 seconds for more responsive timing
      console.log('🔔 Starting web notification checker...');
      checkNotification();

      // Create interval with more debugging
      console.log('🔔 Creating interval...');
      intervalRef.current = setInterval(() => {
        console.log('🔔 *** INTERVAL TICK *** Running scheduled notification check at:', new Date().toLocaleTimeString());
        checkNotification();
      }, 30000);

      console.log('🔔 Web notification checker started with 30-second interval, intervalId:', intervalRef.current);

      // Test the interval is working with a shorter test
      setTimeout(() => {
        console.log('🔔 5-second test: Interval should be running...');
      }, 5000);
    };

    // Setup notification listeners for native platforms
    const setupNotificationListeners = async () => {
      if (!Capacitor.isNativePlatform()) return;

      try {
        console.log('Notifications: Setting up listeners...');

        // Remove any existing listeners first
        await LocalNotifications.removeAllListeners();

        // Listen for notification actions (when user taps notification)
        await LocalNotifications.addListener('localNotificationActionPerformed', (notification) => {
          console.log('Notification action performed:', notification);

          // Update last notification date when user interacts with notification
          dispatch({
            type: 'UPDATE_SETTINGS',
            payload: { lastNotificationDate: new Date().toISOString() }
          });
        });

        // Listen for notification received (when app is in foreground)
        await LocalNotifications.addListener('localNotificationReceived', (notification) => {
          console.log('Notification received in foreground:', notification);

          // Update last notification date
          dispatch({
            type: 'UPDATE_SETTINGS',
            payload: { lastNotificationDate: new Date().toISOString() }
          });
        });

        console.log('Notifications: Listeners setup complete');

      } catch (error) {
        console.error('Notifications: Listener setup error:', error);
      }
    };

    // Initialize notifications
    setupNotifications();

    // Cleanup function
    return () => {
      console.log('Notifications: Cleaning up...');

      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }

      if (Capacitor.isNativePlatform() && listenersSetupRef.current) {
        LocalNotifications.removeAllListeners();
        listenersSetupRef.current = false;
      }
    };

  }, [notificationsEnabled, notificationTime, lastNotificationDate, dispatch]); // Include all dependencies

  // Function to test notifications immediately
  const testNotification = async () => {
    try {
      console.log('🔔 TEST NOTIFICATION FUNCTION CALLED');
      alert('Test notification function called! Check console for details.');

      if (!Capacitor.isNativePlatform()) {
        console.log('🌐 Testing web notification on browser');

        // Check if notifications are supported
        if (!('Notification' in window)) {
          console.error('❌ Web notifications not supported in this browser');
          alert('Web notifications not supported in this browser');
          return false;
        }

        console.log('🔔 Current notification permission:', Notification.permission);

        // Request permission if needed
        if (Notification.permission === 'default') {
          console.log('🔔 Requesting notification permission...');
          const permission = await Notification.requestPermission();
          console.log('🔔 Permission request result:', permission);
        }

        if (Notification.permission === 'granted') {
          console.log('✅ Permission granted, creating notification...');

          try {
            const notification = new Notification('⚡ Test Notification', {
              body: 'This is a test notification from your Prepaid Meter app!',
              icon: '/favicon.ico',
              tag: 'test-notification',
              requireInteraction: false
            });

            console.log('✅ Notification created successfully');

            // Auto-close after 5 seconds
            setTimeout(() => {
              notification.close();
              console.log('🔔 Notification auto-closed');
            }, 5000);

            return true;
          } catch (notifError) {
            console.error('❌ Error creating notification:', notifError);
            alert('Error creating notification: ' + notifError.message);
            return false;
          }
        } else {
          console.log('❌ Notification permission denied or blocked');
          alert('Notification permission denied. Please enable notifications in your browser settings.');
          return false;
        }
      }

      console.log('Notifications: Testing native notification');

      // Native notification test
      const permission = await LocalNotifications.requestPermissions();
      console.log('Notifications: Native permission result:', permission);

      if (permission.display !== 'granted') {
        console.log('Test notification: Native permission denied');
        return false;
      }

      // Cancel any existing test notifications
      try {
        await LocalNotifications.cancel({ notifications: [{ id: 999 }] });
      } catch (e) {
        // Ignore if no notifications to cancel
      }

      const testConfig = {
        notifications: [
          {
            title: '⚡ Test Notification',
            body: 'This is a test notification from your Prepaid Meter app!',
            id: 999,
            schedule: {
              at: new Date(Date.now() + 2000) // 2 seconds from now
            },
            sound: 'default',
            attachments: null,
            actionTypeId: '',
            extra: {
              type: 'test_notification'
            }
          }
        ]
      };

      await LocalNotifications.schedule(testConfig);
      console.log('Notifications: Test notification scheduled for 2 seconds from now');

      return true;

    } catch (error) {
      console.error('Test notification error:', error);
      return false;
    }
  };

  return { testNotification };
};
