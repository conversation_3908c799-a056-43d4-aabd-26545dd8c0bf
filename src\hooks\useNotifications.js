import { useEffect } from 'react';
import { LocalNotifications } from '@capacitor/local-notifications';
import { Capacitor } from '@capacitor/core';

export const useNotifications = (notificationsEnabled, notificationTime, lastNotificationDate, dispatch) => {
  useEffect(() => {
    if (!notificationsEnabled) return;

    const setupNotifications = async () => {
      try {
        // Only setup on native platforms
        if (!Capacitor.isNativePlatform()) {
          console.log('Notifications: Web platform detected, using fallback');
          setupWebNotifications();
          return;
        }

        // Request permissions for native platforms
        const permission = await LocalNotifications.requestPermissions();
        
        if (permission.display !== 'granted') {
          console.log('Notifications: Permission denied');
          return;
        }

        console.log('Notifications: Permission granted, setting up daily reminder');
        await scheduleDailyNotification();

      } catch (error) {
        console.error('Notifications: Setup error:', error);
        // Fallback to web notifications
        setupWebNotifications();
      }
    };

    const scheduleDailyNotification = async () => {
      try {
        // Cancel any existing notifications
        await LocalNotifications.cancel({ notifications: [{ id: 1 }] });

        // Parse notification time
        const [hours, minutes] = notificationTime.split(':').map(Number);
        
        // Create notification schedule for today
        const now = new Date();
        const scheduledTime = new Date();
        scheduledTime.setHours(hours, minutes, 0, 0);

        // If the time has passed today, schedule for tomorrow
        if (scheduledTime <= now) {
          scheduledTime.setDate(scheduledTime.getDate() + 1);
        }

        await LocalNotifications.schedule({
          notifications: [
            {
              title: '⚡ Prepaid Meter Reminder',
              body: "Don't forget to record your electricity usage today!",
              id: 1,
              schedule: {
                at: scheduledTime,
                repeats: true,
                every: 'day'
              },
              sound: 'default',
              attachments: null,
              actionTypeId: '',
              extra: {
                type: 'daily_reminder'
              }
            }
          ]
        });

        console.log('Notifications: Daily reminder scheduled for', scheduledTime.toLocaleString());

      } catch (error) {
        console.error('Notifications: Scheduling error:', error);
      }
    };

    const setupWebNotifications = () => {
      // Fallback for web browsers
      const checkNotification = () => {
        const now = new Date();
        const [hours, minutes] = notificationTime.split(':').map(Number);
        const notificationTimeToday = new Date();
        notificationTimeToday.setHours(hours, minutes, 0, 0);

        const today = now.toDateString();
        const lastNotificationDateStr = lastNotificationDate ? new Date(lastNotificationDate).toDateString() : null;

        // Check if it's time for notification and we haven't sent one today
        if (now >= notificationTimeToday && lastNotificationDateStr !== today) {
          // Request notification permission if not granted
          if ('Notification' in window && Notification.permission === 'default') {
            Notification.requestPermission();
          }

          // Send notification if permission is granted
          if ('Notification' in window && Notification.permission === 'granted') {
            new Notification('⚡ Prepaid Meter Reminder', {
              body: "Don't forget to record your electricity usage today!",
              icon: '/favicon.ico',
              badge: '/favicon.ico'
            });

            // Update last notification date
            dispatch({
              type: 'UPDATE_SETTINGS',
              payload: { lastNotificationDate: now.toISOString() }
            });
          }
        }
      };

      // Check immediately and then every minute
      checkNotification();
      const interval = setInterval(checkNotification, 60000);

      return () => clearInterval(interval);
    };

    // Setup notification listeners for native platforms
    const setupNotificationListeners = async () => {
      if (!Capacitor.isNativePlatform()) return;

      try {
        // Listen for notification actions
        await LocalNotifications.addListener('localNotificationActionPerformed', (notification) => {
          console.log('Notification action performed:', notification);
          
          // Update last notification date when user interacts with notification
          dispatch({
            type: 'UPDATE_SETTINGS',
            payload: { lastNotificationDate: new Date().toISOString() }
          });
        });

        // Listen for notification received (when app is in foreground)
        await LocalNotifications.addListener('localNotificationReceived', (notification) => {
          console.log('Notification received:', notification);
          
          // Update last notification date
          dispatch({
            type: 'UPDATE_SETTINGS',
            payload: { lastNotificationDate: new Date().toISOString() }
          });
        });

      } catch (error) {
        console.error('Notifications: Listener setup error:', error);
      }
    };

    // Initialize notifications
    setupNotifications();
    setupNotificationListeners();

    // Cleanup function
    return () => {
      if (Capacitor.isNativePlatform()) {
        LocalNotifications.removeAllListeners();
      }
    };

  }, [notificationsEnabled, notificationTime, lastNotificationDate, dispatch]);

  // Function to test notifications immediately
  const testNotification = async () => {
    try {
      if (!Capacitor.isNativePlatform()) {
        // Web notification test
        if ('Notification' in window) {
          if (Notification.permission === 'default') {
            await Notification.requestPermission();
          }
          
          if (Notification.permission === 'granted') {
            new Notification('⚡ Test Notification', {
              body: 'This is a test notification from your Prepaid Meter app!',
              icon: '/favicon.ico'
            });
            return true;
          }
        }
        return false;
      }

      // Native notification test
      const permission = await LocalNotifications.requestPermissions();
      
      if (permission.display !== 'granted') {
        console.log('Test notification: Permission denied');
        return false;
      }

      await LocalNotifications.schedule({
        notifications: [
          {
            title: '⚡ Test Notification',
            body: 'This is a test notification from your Prepaid Meter app!',
            id: 999,
            schedule: {
              at: new Date(Date.now() + 1000) // 1 second from now
            },
            sound: 'default'
          }
        ]
      });

      console.log('Test notification scheduled');
      return true;

    } catch (error) {
      console.error('Test notification error:', error);
      return false;
    }
  };

  return { testNotification };
};
