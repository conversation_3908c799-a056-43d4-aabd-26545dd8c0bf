import React, { useState } from 'react'
import { useApp } from '../../context/AppContext'
import { useTheme } from '../../context/ThemeContext'
import { HiLightningBolt, HiCalculator } from 'react-icons/hi'
import { preventNumberInputScroll } from '../../hooks/usePreventNumberInputScroll'

function UsageForm() {
  const [currentReading, setCurrentReading] = useState('')
  const [isSubmitting, setIsSubmitting] = useState(false)

  const { state, updateUsage, usageSinceLastRecording, getDisplayUnitName } = useApp()
  const { theme, currentTheme } = useTheme()

  // Helper function to get appropriate background for cards in dark mode
  const getCardBackground = (lightBg, darkBg = 'bg-gray-700/50') => {
    return currentTheme === 'dark' ? darkBg : lightBg
  }

  const newReading = parseFloat(currentReading) || 0
  const calculatedUsage = state.currentUnits - newReading
  const usageCost = calculatedUsage * state.unitCost

  const handleSubmit = async (e) => {
    e.preventDefault()
    setIsSubmitting(true)

    try {
      const reading = parseFloat(currentReading)

      if (isNaN(reading) || reading < 0) {
        alert('Please enter a valid meter reading (0 or greater)')
        return
      }

      if (reading > state.currentUnits) {
        alert('Current reading cannot be higher than your available units')
        return
      }

      updateUsage(reading)
      
      // Reset form
      setCurrentReading('')
      
      // Show success message
      alert(`Usage recorded successfully! Used ${calculatedUsage.toFixed(2)} ${getDisplayUnitName()} costing ${state.currencySymbol || 'R'}${usageCost.toFixed(2)}`)
      
    } catch (error) {
      console.error('Error recording usage:', error)
      alert('Error recording usage. Please try again.')
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Current reading input - Moved to top */}
      <div>
        <label htmlFor="currentReading" className={`block text-sm font-semibold ${theme.text} mb-3`}>
          Current Meter Reading
        </label>
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
            <div className={`p-1 rounded-lg bg-gradient-to-br ${theme.gradient}`}>
              <HiLightningBolt className="h-4 w-4 text-white" />
            </div>
          </div>
          <input
            type="number"
            id="currentReading"
            value={currentReading}
            onChange={(e) => setCurrentReading(e.target.value)}
            onWheel={preventNumberInputScroll}
            step="0.01"
            min="0"
            max={state.currentUnits}
            placeholder="Enter current meter reading"
            className={`w-full pl-12 pr-4 py-4 md:py-4 border-4 ${theme.border} rounded-xl focus:ring-4 focus:ring-opacity-50 focus:${theme.border} ${theme.card} ${theme.text} ${theme.textSecondary} font-bold shadow-lg hover:shadow-xl transition-all duration-200 min-w-0`}
            required
          />
        </div>
        <p className={`mt-2 text-xs ${theme.textSecondary} opacity-80 font-medium`}>
          📊 Enter the current reading from your electricity meter
        </p>
      </div>

      {/* Integrated Usage Calculation Summary - Stacked vertically */}
      {newReading > 0 && (
        <div className="mt-4 space-y-4">
          {/* Previous & Current Reading Card */}
          <div className={`relative overflow-hidden rounded-2xl ${theme.card} border ${theme.border} p-4 md:p-5 shadow-lg mobile-card-auto`}>
            <div className={`absolute inset-0 ${theme.secondary}`}></div>
            <div className="relative">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <div className={`text-xs font-semibold ${theme.textSecondary} tracking-wider uppercase mb-1`}>READINGS</div>
                  <p className={`text-lg md:text-xl font-bold ${theme.text} mb-1`}>
                    {newReading.toFixed(2)}
                  </p>
                  <p className={`text-xs ${theme.textSecondary}`}>Previous: {state.currentUnits.toFixed(2)} {getDisplayUnitName()}</p>
                </div>
                <div className={`p-2 md:p-3 rounded-xl bg-gradient-to-br ${theme.gradient} shadow-lg ml-3`}>
                  <HiLightningBolt className="h-5 md:h-6 w-5 md:w-6 text-white" />
                </div>
              </div>
            </div>
            {/* Decorative elements */}
            <div className={`absolute top-2 right-2 w-4 h-4 bg-gradient-to-r ${theme.gradient} rounded-full opacity-20 blur-sm`}></div>
            <div className={`absolute bottom-2 left-2 w-3 h-3 bg-gradient-to-r ${theme.gradient} rounded-full opacity-15 blur-sm`}></div>
          </div>

          {/* Usage & Cost Card */}
          <div className={`relative overflow-hidden rounded-2xl ${theme.card} border ${theme.border} p-4 md:p-5 shadow-lg mobile-card-auto`}>
            <div className={`absolute inset-0 ${theme.secondary}`}></div>
            <div className="relative">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <div className={`text-xs font-semibold ${theme.textSecondary} tracking-wider uppercase mb-1`}>USAGE & COST</div>
                  <p className={`text-lg md:text-xl font-bold ${theme.text} mb-1`}>
                    {calculatedUsage.toFixed(2)}
                  </p>
                  <p className={`text-xs ${theme.textSecondary}`}>
                    Cost: {state.currencySymbol || 'R'}{usageCost.toFixed(2)}
                  </p>
                </div>
                <div className={`p-2 md:p-3 rounded-xl bg-gradient-to-br ${theme.gradient} shadow-lg ml-3`}>
                  <HiCalculator className="h-5 md:h-6 w-5 md:w-6 text-white" />
                </div>
              </div>
            </div>
            {/* Decorative elements */}
            <div className={`absolute top-2 right-2 w-4 h-4 bg-gradient-to-r ${theme.gradient} rounded-full opacity-20 blur-sm`}></div>
            <div className={`absolute bottom-2 left-2 w-3 h-3 bg-gradient-to-r ${theme.gradient} rounded-full opacity-15 blur-sm`}></div>
          </div>
        </div>
      )}

      {/* Warning for invalid readings */}
      {newReading > 0 && calculatedUsage < 0 && (
        <div className={`mt-4 p-4 ${theme.secondary} border ${theme.border} rounded-xl`}>
          <div className="flex items-center">
            <div className={`p-1 rounded-lg ${theme.accent} mr-2`}>
              <span className="text-white text-xs">⚠️</span>
            </div>
            <span className={`${theme.textSecondary} text-sm font-medium`}>
              Warning: New reading cannot be higher than available units
            </span>
          </div>
        </div>
      )}

      {/* Submit button */}
      <button
        type="submit"
        disabled={isSubmitting || newReading <= 0 || newReading > state.currentUnits}
        className={`w-full ${theme.primary} text-white py-4 px-6 rounded-xl font-semibold hover:opacity-90 transition-all duration-300 focus:ring-4 focus:ring-opacity-50 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98]`}
      >
        <div className="flex items-center justify-center gap-2 text-white">
          {isSubmitting ? (
            <>
              <div className="animate-spin rounded-full h-5 w-5 border-2 border-white border-t-transparent"></div>
              <span className="text-white">Recording Usage...</span>
            </>
          ) : (
            <>
              <HiLightningBolt className="h-5 w-5 text-white" />
              <span className="text-white">Record Usage</span>
            </>
          )}
        </div>
      </button>

      {/* Help text */}
      <div className={`w-full text-center p-4 ${theme.card} rounded-xl border ${theme.border}`}>
        <div className="w-full flex items-center justify-center mb-2">
          <div className={`p-1 rounded-lg bg-gradient-to-br ${theme.gradient} mr-2`}>
            <span className="text-white text-xs">💡</span>
          </div>
          <span className={`text-sm font-semibold ${theme.text}`}>How it works</span>
        </div>
        <p className={`w-full text-xs ${theme.textSecondary} leading-relaxed`}>
          Record your current meter reading to track electricity usage.
          The system will calculate how many units you've used since the last recording.
        </p>
      </div>

      {/* Current status - Moved to bottom */}
      <div className={`w-full p-6 ${theme.card} rounded-xl border ${theme.border} shadow-sm`}>
        <div className="w-full flex items-center mb-4">
          <div className={`p-2 rounded-lg bg-gradient-to-br ${theme.gradient} mr-3`}>
            <HiLightningBolt className="h-5 w-5 text-white" />
          </div>
          <h3 className={`font-semibold ${theme.text} text-lg`}>Current Status</h3>
        </div>
        <div className="w-full space-y-3 text-sm">
          <div className={`w-full flex justify-between items-center p-3 ${theme.secondary} rounded-lg`}>
            <span className={`${theme.textSecondary} font-medium`}>Available Units:</span>
            <span className={`${theme.text} font-bold`}>{state.currentUnits.toFixed(2)} {getDisplayUnitName()}</span>
          </div>
          <div className={`w-full flex justify-between items-center p-3 ${theme.secondary} rounded-lg`}>
            <span className={`${theme.textSecondary} font-medium`}>Previous Reading:</span>
            <span className={`${theme.text} font-bold`}>{state.previousUnits.toFixed(2)} {getDisplayUnitName()}</span>
          </div>
          <div className={`w-full flex justify-between items-center p-3 ${theme.secondary} rounded-lg`}>
            <span className={`${theme.textSecondary} font-medium`}>Usage Since Last:</span>
            <span className={`${theme.text} font-bold`}>{usageSinceLastRecording.toFixed(2)} {getDisplayUnitName()}</span>
          </div>
        </div>
      </div>
    </form>
  )
}

export default UsageForm
