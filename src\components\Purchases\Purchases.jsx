import React, { useState } from 'react'
import { useApp } from '../../context/AppContext'
import { useTheme } from '../../context/ThemeContext'
import PurchaseForm from './PurchaseForm'
import HelpTooltip from '../Common/HelpTooltip'
import SwipeableLayout from '../Common/SwipeableLayout'
import { HiCurrencyDollar, HiLightningBolt, HiCalendar } from 'react-icons/hi'

function Purchases() {
  const { state, getDisplayUnitName } = useApp()
  const { theme, currentTheme } = useTheme()

  // Helper function to get appropriate background for cards in dark mode
  const getCardBackground = (lightBg, darkBg = 'bg-gray-800/50') => {
    return currentTheme === 'dark' ? darkBg : lightBg
  }

  const totalSpent = state.purchases.reduce((total, purchase) => total + purchase.currency, 0)
  const totalUnits = state.purchases.reduce((total, purchase) => total + purchase.units, 0)

  // Format timestamp to show date and time separately without seconds
  const formatTimestamp = (timestamp) => {
    const date = new Date(timestamp)
    const dateStr = date.toLocaleDateString('en-GB') // dd/mm/yyyy format
    const timeStr = date.toLocaleTimeString('en-GB', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    }) // HH:mm format
    return { date: dateStr, time: timeStr }
  }

  // Left content for swipeable layout
  const leftContent = (
    <div className="space-y-6">
          {/* Purchase form */}
          <div className={`${theme.card} rounded-2xl shadow-lg p-4 md:p-8 border ${theme.border} ${getCardBackground('bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50', 'bg-gray-800/50')} h-fit w-full`}>
            <h2 className={`text-2xl font-semibold ${theme.text} mb-6 flex items-center gap-3`}>
              <div className="p-3 rounded-xl bg-gradient-to-br from-blue-500 to-indigo-600 shadow-md">
                <HiCurrencyDollar className="h-6 w-6 text-white" />
              </div>
              Add New Purchase
            </h2>
            <div className={`${getCardBackground('bg-white/60', 'bg-gray-700/50')} backdrop-blur-sm rounded-xl p-4 md:p-6 w-full`}>
              <PurchaseForm />
            </div>
          </div>

          {/* Summary cards under Add New Purchase (stacked vertically) */}
          <div className="space-y-4">
            {/* Total Spent Card */}
            <div className={`${theme.card} rounded-xl shadow-lg p-4 border ${theme.border} hover:shadow-xl transition-all duration-300`}>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className={`p-2 rounded-lg bg-gradient-to-br ${theme.gradient} shadow-lg`}>
                    <HiCurrencyDollar className="h-5 w-5 text-white" />
                  </div>
                  <div className="flex-1">
                    <p className={`text-sm font-medium ${theme.textSecondary} opacity-80 mb-2`}>
                      Total Spent
                    </p>
                    <p className={`text-lg font-bold ${theme.text} mb-2`}>
                      {state.currencySymbol || 'R'}{totalSpent.toFixed(2)}
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-xs text-emerald-500 font-medium">All Purchases</p>
                </div>
              </div>
            </div>

            {/* Total Units Card */}
            <div className={`${theme.card} rounded-xl shadow-lg p-4 border ${theme.border} hover:shadow-xl transition-all duration-300`}>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className={`p-2 rounded-lg bg-gradient-to-br ${theme.gradient} shadow-lg`}>
                    <HiLightningBolt className="h-5 w-5 text-white" />
                  </div>
                  <div className="flex-1">
                    <p className={`text-sm font-medium ${theme.textSecondary} opacity-80 mb-1`}>
                      Total {getDisplayUnitName()} Purchased
                    </p>
                    <p className={`text-lg font-bold ${theme.text} mb-1`}>
                      {totalUnits.toFixed(2)}
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <p className={`text-xs ${theme.textSecondary} font-medium`}>{getDisplayUnitName()}</p>
                </div>
              </div>
            </div>

            {/* Total Purchases Card */}
            <div className={`${theme.card} rounded-xl shadow-lg p-4 border ${theme.border} hover:shadow-xl transition-all duration-300`}>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className={`p-2 rounded-lg bg-gradient-to-br ${theme.gradient} shadow-lg`}>
                    <HiCalendar className="h-5 w-5 text-white" />
                  </div>
                  <div className="flex-1">
                    <p className={`text-sm font-medium ${theme.textSecondary} opacity-80 mb-1`}>
                      Total Purchases
                    </p>
                    <p className={`text-lg font-bold ${theme.text} mb-1`}>
                      {state.purchases.length}
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-xs text-violet-500 font-medium">Transactions</p>
                </div>
              </div>
            </div>
          </div>
    </div>
  )

  // Right content for swipeable layout
  const rightContent = (
    <div className={`${theme.card} rounded-2xl shadow-lg p-6 border ${theme.border} ${getCardBackground('bg-gradient-to-br from-emerald-50 via-green-50 to-teal-50', 'bg-gray-800/50')} flex flex-col`}>
      <h2 className={`text-xl font-semibold ${theme.text} mb-4 flex items-center gap-3`}>
        <div className={`p-2 rounded-xl bg-gradient-to-br ${theme.gradient} shadow-md`}>
          <HiCalendar className="h-5 w-5 text-white" />
        </div>
        Recent Purchases
      </h2>
      <div className="space-y-3 flex-1 overflow-y-auto">
              {state.purchases.slice(0, 10).map((purchase) => (
                <div
                  key={purchase.id}
                  className={`p-4 ${theme.secondary} border ${theme.border} rounded-xl shadow-sm hover:shadow-md transition-all duration-200`}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className={`p-2 rounded-lg bg-gradient-to-br ${theme.gradient} shadow-sm`}>
                        <HiCurrencyDollar className="h-4 w-4 text-white" />
                      </div>
                      <div className="ml-3">
                        <p className={`text-sm font-semibold ${theme.text}`}>
                          {state.currencySymbol || 'R'}{purchase.currency.toFixed(2)}
                        </p>
                        <div className={`text-xs ${theme.textSecondary}`}>
                          <p>{formatTimestamp(purchase.timestamp).date}</p>
                          <p>{formatTimestamp(purchase.timestamp).time}</p>
                        </div>
                      </div>
                    </div>
                    <span className={`text-sm font-semibold ${theme.text}`}>
                      +{purchase.units.toFixed(2)} {getDisplayUnitName()}
                    </span>
                  </div>
                </div>
              ))}

              {/* Empty state */}
              {state.purchases.length === 0 && (
                <div className={`text-center py-8 ${theme.secondary} border ${theme.border} rounded-xl`}>
                  <div className={`p-3 rounded-2xl bg-gradient-to-br ${theme.gradient} w-fit mx-auto mb-3`}>
                    <HiCurrencyDollar className={`h-8 w-8 text-white`} />
                  </div>
                  <p className={`text-sm ${theme.textSecondary} font-medium`}>
                    No purchases yet
                  </p>
                  <p className={`text-xs ${theme.textSecondary} mt-1`}>
                    Add your first purchase to get started
                  </p>
                </div>
              )}
      </div>
    </div>
  )

  return (
    <div className="w-full space-y-6 pb-6">
      {/* Main content with swipeable layout */}
      <SwipeableLayout
        leftContent={leftContent}
        rightContent={rightContent}
        rightContentTitle="Recent Purchases"
        className=""
      />
    </div>
  )
}

export default Purchases
