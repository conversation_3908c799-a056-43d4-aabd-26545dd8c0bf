import React, { useState } from 'react'
import { useApp } from '../../context/AppContext'
import { useTheme } from '../../context/ThemeContext'
import { HiCalculator, HiCurrencyDollar, HiLightningBolt } from 'react-icons/hi'
import { preventNumberInputScroll } from '../../hooks/usePreventNumberInputScroll'

function PurchaseForm() {
  const [currency, setCurrency] = useState('')
  const [isSubmitting, setIsSubmitting] = useState(false)

  const { state, addPurchase, getDisplayUnitName } = useApp()
  const { theme, currentTheme } = useTheme()

  // Helper function to get appropriate background for cards in dark mode
  const getCardBackground = (lightBg, darkBg = 'bg-gray-700/50') => {
    return currentTheme === 'dark' ? darkBg : lightBg
  }

  // Available currencies (same as in Settings)
  const currencies = [
    { code: '<PERSON>AR', name: 'South African Rand', symbol: 'R' },
    { code: 'USD', name: 'US Dollar', symbol: '$' },
    { code: 'EUR', name: 'Euro', symbol: '€' },
    { code: 'GBP', name: 'British Pound', symbol: '£' },
    { code: 'JPY', name: 'Japanese Yen', symbol: '¥' },
    { code: 'CAD', name: 'Canadian Dollar', symbol: 'C$' },
    { code: 'AUD', name: 'Australian Dollar', symbol: 'A$' },
    { code: 'CHF', name: 'Swiss Franc', symbol: 'CHF' },
    { code: 'CNY', name: 'Chinese Yuan', symbol: '¥' },
    { code: 'INR', name: 'Indian Rupee', symbol: '₹' },
    { code: 'BRL', name: 'Brazilian Real', symbol: 'R$' },
    { code: 'KRW', name: 'South Korean Won', symbol: '₩' },
    { code: 'MXN', name: 'Mexican Peso', symbol: '$' },
    { code: 'SGD', name: 'Singapore Dollar', symbol: 'S$' },
    { code: 'NZD', name: 'New Zealand Dollar', symbol: 'NZ$' }
  ]

  // Helper function to round to 2 decimal places without floating point errors
  const roundToTwo = (num) => {
    return Math.round((num + Number.EPSILON) * 100) / 100
  }

  // Helper function to format number for display without trailing zeros
  const formatNumber = (num) => {
    const rounded = roundToTwo(num)
    return rounded % 1 === 0 ? rounded.toString() : rounded.toFixed(2)
  }

  // Real-time calculations
  const currencyValue = parseFloat(currency) || 0
  const unitCost = state.unitCost || 0

  // Calculate units based on currency input
  const calculatedUnits = unitCost > 0 ? roundToTwo(currencyValue / unitCost) : 0

  const handleSubmit = async (e) => {
    e.preventDefault()
    setIsSubmitting(true)

    try {
      const finalCurrency = roundToTwo(parseFloat(currency))
      const finalUnits = calculatedUnits

      if (isNaN(finalCurrency) || finalCurrency <= 0) {
        alert('Please enter a valid positive amount')
        return
      }

      if (unitCost <= 0) {
        alert('Please set a valid unit cost in Settings before making a purchase')
        return
      }

      if (finalUnits <= 0) {
        alert('The calculated units must be greater than 0')
        return
      }

      addPurchase(finalCurrency, finalUnits)

      // Reset form
      setCurrency('')

      // Show success message
      alert(`Purchase added successfully! Added ${formatNumber(finalUnits)} ${getDisplayUnitName()} for ${state.currencySymbol || 'R'}${formatNumber(finalCurrency)}`)

    } catch (error) {
      console.error('Error adding purchase:', error)
      alert('Error adding purchase. Please try again.')
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">

      {/* Currency input */}
      <div>
        <label htmlFor="currency" className={`block text-sm font-semibold ${theme.text} mb-3`}>
          💰 Amount ({currencies.find(c => c.code === (state.currency || 'ZAR'))?.name || 'South African Rand'})
        </label>
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
            <div className={`p-1 rounded-lg bg-gradient-to-br ${theme.gradient}`}>
              <HiCurrencyDollar className="h-4 w-4 text-white" />
            </div>
          </div>
          <input
            type="number"
            id="currency"
            value={currency}
            onChange={(e) => setCurrency(e.target.value)}
            onWheel={preventNumberInputScroll}
            step="0.01"
            min="0"
            placeholder="Enter amount to spend"
            className={`w-full pl-12 pr-4 py-4 md:py-4 border-4 ${theme.border} rounded-xl focus:ring-4 focus:ring-opacity-50 focus:${theme.border} ${theme.card} ${theme.text} ${theme.textSecondary} font-bold shadow-lg hover:shadow-xl transition-all duration-200 min-w-0`}
            required
          />
        </div>
        <p className={`mt-2 text-xs ${theme.textSecondary} opacity-80 font-medium`}>
          💡 Enter the amount you want to spend
        </p>
      </div>

      {/* Integrated Calculation Summary - Stacked vertically */}
      {currencyValue > 0 && (
        <div className="mt-4 space-y-4">
          {/* Purchase Amount Card */}
          <div className={`relative overflow-hidden rounded-2xl ${theme.card} border ${theme.border} p-4 md:p-5 shadow-lg mobile-card-auto`}>
            <div className={`absolute inset-0 ${theme.secondary}`}></div>
            <div className="relative">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <div className={`text-xs font-semibold ${theme.textSecondary} tracking-wider uppercase mb-1`}>PURCHASE AMOUNT</div>
                  <p className={`text-lg md:text-xl font-bold ${theme.text} mb-1`}>
                    {state.currencySymbol || 'R'}{formatNumber(currencyValue)}
                  </p>
                  <p className={`text-xs ${theme.textSecondary}`}>
                    @ {state.currencySymbol || 'R'}{formatNumber(unitCost)} per {getDisplayUnitName()}
                  </p>
                </div>
                <div className={`p-2 md:p-3 rounded-xl bg-gradient-to-br ${theme.gradient} shadow-lg ml-3`}>
                  <HiCurrencyDollar className="h-5 md:h-6 w-5 md:w-6 text-white" />
                </div>
              </div>
            </div>
            {/* Decorative elements */}
            <div className={`absolute top-2 right-2 w-4 h-4 bg-gradient-to-r ${theme.gradient} rounded-full opacity-20 blur-sm`}></div>
            <div className={`absolute bottom-2 left-2 w-3 h-3 bg-gradient-to-r ${theme.gradient} rounded-full opacity-15 blur-sm`}></div>
          </div>

          {/* Units to Receive Card */}
          <div className={`relative overflow-hidden rounded-2xl ${theme.card} border ${theme.border} p-4 md:p-5 shadow-lg mobile-card-auto`}>
            <div className={`absolute inset-0 ${theme.secondary}`}></div>
            <div className="relative">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <div className={`text-xs font-semibold ${theme.textSecondary} tracking-wider uppercase mb-1`}>UNITS TO RECEIVE</div>
                  <p className={`text-lg md:text-xl font-bold ${theme.text} mb-1`}>
                    {formatNumber(calculatedUnits)}
                  </p>
                  <p className={`text-xs ${theme.textSecondary}`}>
                    New Total: {formatNumber(roundToTwo(state.currentUnits + calculatedUnits))} {getDisplayUnitName()}
                  </p>
                </div>
                <div className={`p-2 md:p-3 rounded-xl bg-gradient-to-br ${theme.gradient} shadow-lg ml-3`}>
                  <HiLightningBolt className="h-5 md:h-6 w-5 md:w-6 text-white" />
                </div>
              </div>
            </div>
            {/* Decorative elements */}
            <div className={`absolute top-2 right-2 w-4 h-4 bg-gradient-to-r ${theme.gradient} rounded-full opacity-20 blur-sm`}></div>
            <div className={`absolute bottom-2 left-2 w-3 h-3 bg-gradient-to-r ${theme.gradient} rounded-full opacity-15 blur-sm`}></div>
          </div>
        </div>
      )}

      {/* Submit button */}
      <button
        type="submit"
        disabled={isSubmitting || currencyValue <= 0 || calculatedUnits <= 0 || unitCost <= 0}
        className={`w-full ${theme.primary} text-white py-4 px-6 rounded-xl font-semibold hover:opacity-90 transition-all duration-300 focus:ring-4 focus:ring-opacity-50 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98]`}
      >
        <div className="flex items-center justify-center gap-2 text-white">
          {isSubmitting ? (
            <>
              <div className="animate-spin rounded-full h-5 w-5 border-2 border-white border-t-transparent"></div>
              <span className="text-white">Adding Purchase...</span>
            </>
          ) : (
            <>
              <HiCurrencyDollar className="h-5 w-5 text-white" />
              <span className="text-white">Add Purchase</span>
            </>
          )}
        </div>
      </button>
    </form>
  )
}

export default PurchaseForm
