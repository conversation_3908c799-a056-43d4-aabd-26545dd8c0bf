import React, { useEffect, useState } from 'react'
import { useTheme } from '../../context/ThemeContext'

function SwipeIndicator({ 
  message = "Swipe to see more", 
  onDismiss,
  position = "top-right", // "top-right", "top-left", "bottom-right", "bottom-left", "center"
  autoHide = true,
  autoHideDelay = 5000
}) {
  const { theme } = useTheme()
  const [isVisible, setIsVisible] = useState(true)

  // Auto-hide functionality
  useEffect(() => {
    if (autoHide) {
      const timer = setTimeout(() => {
        setIsVisible(false)
        if (onDismiss) onDismiss()
      }, autoHideDelay)

      return () => clearTimeout(timer)
    }
  }, [autoHide, autoHideDelay, onDismiss])

  // Position classes
  const getPositionClasses = () => {
    switch (position) {
      case 'top-left':
        return 'top-4 left-4'
      case 'top-right':
        return 'top-4 right-4'
      case 'bottom-left':
        return 'bottom-4 left-4'
      case 'bottom-right':
        return 'bottom-4 right-4'
      case 'center':
        return 'top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2'
      default:
        return 'top-4 right-4'
    }
  }

  if (!isVisible) return null

  return (
    <div className={`absolute ${getPositionClasses()} z-30 pointer-events-none`}>
      <div className={`flex items-center gap-2 ${theme.primary} text-white px-4 py-3 rounded-full shadow-lg backdrop-blur-sm animate-pulse swipe-indicator`}>
        {/* Swipe animation */}
        <div className="flex items-center gap-1">
          <div className="flex space-x-1">
            <div className="w-1 h-1 bg-white rounded-full animate-bounce"></div>
            <div className="w-1 h-1 bg-white rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
            <div className="w-1 h-1 bg-white rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
          </div>
        </div>
        
        {/* Message */}
        <span className="text-sm font-medium whitespace-nowrap">{message}</span>
        
        {/* Swipe arrow animation */}
        <div className="flex items-center">
          <div className="animate-pulse">
            <svg 
              className="w-4 h-4 text-white" 
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24"
            >
              <path 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                strokeWidth={2} 
                d="M9 5l7 7-7 7" 
              />
            </svg>
          </div>
        </div>
      </div>
      
      {/* Dismiss button (if needed) */}
      {onDismiss && (
        <button
          onClick={() => {
            setIsVisible(false)
            onDismiss()
          }}
          className="absolute -top-1 -right-1 w-5 h-5 bg-gray-600 text-white rounded-full flex items-center justify-center text-xs hover:bg-gray-700 transition-colors pointer-events-auto"
        >
          ×
        </button>
      )}
    </div>
  )
}

export default SwipeIndicator
