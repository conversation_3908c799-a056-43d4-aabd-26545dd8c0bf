import React, { useState } from 'react'
import { useApp } from '../../context/AppContext'
import { useTheme } from '../../context/ThemeContext'
import { HiExclamation } from 'react-icons/hi'

function ResetOptions() {
  const [showFactoryConfirm, setShowFactoryConfirm] = useState(false)
  const [showDashboardConfirm, setShowDashboardConfirm] = useState(false)
  const [isResetting, setIsResetting] = useState(false)

  const { state, factoryReset, dashboardReset } = useApp()
  const { theme, currentTheme } = useTheme()

  // Helper function to get appropriate background for cards in dark mode
  const getCardBackground = (lightBg, darkBg = 'bg-gray-800/50') => {
    return currentTheme === 'dark' ? darkBg : lightBg
  }

  const handleFactoryReset = async () => {
    setIsResetting(true)
    try {
      factoryReset()
      setShowFactoryConfirm(false)
      alert('Factory reset completed successfully! The app will now restart.')
      // The app will automatically show the initial setup screen
    } catch (error) {
      console.error('Error during factory reset:', error)
      alert('Error during factory reset. Please try again.')
    } finally {
      setIsResetting(false)
    }
  }

  const handleDashboardReset = async () => {
    setIsResetting(true)
    try {
      dashboardReset()
      setShowDashboardConfirm(false)
      alert('Dashboard data reset successfully! Your history has been preserved.')
    } catch (error) {
      console.error('Error during dashboard reset:', error)
      alert('Error during dashboard reset. Please try again.')
    } finally {
      setIsResetting(false)
    }
  }

  return (
    <div className="space-y-6 max-w-none">
      {/* Dashboard Reset - Full width */}
      <div className={`p-4 md:p-6 border ${theme.border} rounded-lg ${getCardBackground('bg-white', 'bg-gray-800/50')} w-full max-w-none`}>
        <div className="w-full">
            <h3 className={`text-2xl font-semibold ${theme.text}`}>
              Dashboard Data Reset
            </h3>
            <p className={`mt-2 text-lg ${theme.textSecondary}`}>
              Reset current units and previous readings to zero. This will clear your dashboard data
              but preserve your purchase and usage history for reference.
            </p>

            <div className={`mt-4 p-4 ${getCardBackground('bg-orange-50 border-orange-200', 'bg-orange-900/20 border-orange-700')} border rounded-lg`}>
              <h4 className={`text-lg font-medium mb-2 ${currentTheme === 'dark' ? 'text-orange-300' : 'text-orange-800'}`}>What will be reset:</h4>
              <ul className={`text-base space-y-1 ${currentTheme === 'dark' ? 'text-orange-200' : 'text-orange-700'}`}>
                <li className="flex items-start">
                  <span className="mr-3 flex-shrink-0">•</span>
                  <span>Current units will be set to 0</span>
                </li>
                <li className="flex items-start">
                  <span className="mr-3 flex-shrink-0">•</span>
                  <span>Previous units will be set to 0</span>
                </li>
                <li className="flex items-start">
                  <span className="mr-3 flex-shrink-0">•</span>
                  <span>Usage since last recording will be reset</span>
                </li>
              </ul>

              <h4 className={`text-lg font-medium mt-3 mb-2 ${currentTheme === 'dark' ? 'text-orange-300' : 'text-orange-800'}`}>What will be preserved:</h4>
              <ul className={`text-base space-y-1 ${currentTheme === 'dark' ? 'text-orange-200' : 'text-orange-700'}`}>
                <li className="flex items-start">
                  <span className="mr-3 flex-shrink-0">•</span>
                  <span>All purchase history</span>
                </li>
                <li className="flex items-start">
                  <span className="mr-3 flex-shrink-0">•</span>
                  <span>All usage history</span>
                </li>
                <li className="flex items-start">
                  <span className="mr-3 flex-shrink-0">•</span>
                  <span>Settings and preferences</span>
                </li>
                <li className="flex items-start">
                  <span className="mr-3 flex-shrink-0">•</span>
                  <span>Theme and appearance settings</span>
                </li>
              </ul>
            </div>

            {!showDashboardConfirm ? (
              <button
                onClick={() => setShowDashboardConfirm(true)}
                className="mt-4 px-6 py-3 text-lg bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors"
              >
                Reset Dashboard Data
              </button>
            ) : (
              <div className="mt-4 space-y-3">
                <div className={`flex items-center p-4 ${getCardBackground('bg-red-50 border-red-200', 'bg-red-900/20 border-red-700')} border rounded-lg`}>
                  <HiExclamation className="h-6 w-6 text-red-600 mr-3" />
                  <span className={`text-lg ${currentTheme === 'dark' ? 'text-red-200' : 'text-red-800'}`}>
                    Are you sure? This action cannot be undone.
                  </span>
                </div>
                <div className="flex space-x-3">
                  <button
                    onClick={handleDashboardReset}
                    disabled={isResetting}
                    className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors disabled:opacity-50"
                  >
                    {isResetting ? 'Resetting...' : 'Yes, Reset Dashboard'}
                  </button>
                  <button
                    onClick={() => setShowDashboardConfirm(false)}
                    className={`px-4 py-2 border ${theme.border} ${theme.text} rounded-lg hover:${theme.secondary} transition-colors`}
                  >
                    Cancel
                  </button>
                </div>
              </div>
            )}
        </div>
      </div>

      {/* Factory Reset - Full width */}
      <div className={`p-4 md:p-6 border ${getCardBackground('border-red-200 bg-red-50', 'border-red-700 bg-red-900/20')} rounded-lg w-full max-w-none`}>
        <div className="w-full">
            <h3 className={`text-2xl font-semibold ${currentTheme === 'dark' ? 'text-red-300' : 'text-red-800'}`}>
              Factory Reset
            </h3>
            <p className={`mt-2 text-lg ${currentTheme === 'dark' ? 'text-red-200' : 'text-red-700'}`}>
              Completely reset the app to its initial state. This will delete ALL data including
              purchases, usage history, and settings. You will need to set up the app again from scratch.
            </p>

            <div className={`mt-4 p-4 ${getCardBackground('bg-red-100 border-red-300', 'bg-red-900/30 border-red-600')} border rounded-lg`}>
              <h4 className={`text-lg font-medium mb-2 ${currentTheme === 'dark' ? 'text-red-300' : 'text-red-800'}`}>What will be deleted:</h4>
              <ul className={`text-base space-y-1 ${currentTheme === 'dark' ? 'text-red-200' : 'text-red-700'}`}>
                <li className="flex items-start">
                  <span className="mr-3 flex-shrink-0">•</span>
                  <span>All purchase records</span>
                </li>
                <li className="flex items-start">
                  <span className="mr-3 flex-shrink-0">•</span>
                  <span>All usage history</span>
                </li>
                <li className="flex items-start">
                  <span className="mr-3 flex-shrink-0">•</span>
                  <span>Current and previous unit readings</span>
                </li>
                <li className="flex items-start">
                  <span className="mr-3 flex-shrink-0">•</span>
                  <span>All settings and preferences</span>
                </li>
                <li className="flex items-start">
                  <span className="mr-3 flex-shrink-0">•</span>
                  <span>Theme and appearance settings</span>
                </li>
              </ul>

              <div className={`mt-3 p-3 ${getCardBackground('bg-red-200 border-red-400', 'bg-red-900/40 border-red-500')} border rounded text-base ${currentTheme === 'dark' ? 'text-red-200' : 'text-red-800'}`}>
                <strong>Warning:</strong> This action is irreversible. Make sure you have backed up any important data.
              </div>
            </div>

            {!showFactoryConfirm ? (
              <button
                onClick={() => setShowFactoryConfirm(true)}
                className="mt-4 px-6 py-3 text-lg bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
              >
                Factory Reset
              </button>
            ) : (
              <div className="mt-4 space-y-3">
                <div className={`flex items-center p-4 ${getCardBackground('bg-red-100 border-red-300', 'bg-red-900/30 border-red-600')} border rounded-lg`}>
                  <HiExclamation className="h-6 w-6 text-red-700 mr-3" />
                  <span className={`text-lg font-medium ${currentTheme === 'dark' ? 'text-red-200' : 'text-red-800'}`}>
                    This will permanently delete ALL your data. Are you absolutely sure?
                  </span>
                </div>
                <div className="flex space-x-3">
                  <button
                    onClick={handleFactoryReset}
                    disabled={isResetting}
                    className="px-4 py-2 bg-red-700 text-white rounded-lg hover:bg-red-800 transition-colors disabled:opacity-50"
                  >
                    {isResetting ? 'Resetting...' : 'Yes, Delete Everything'}
                  </button>
                  <button
                    onClick={() => setShowFactoryConfirm(false)}
                    className={`px-4 py-2 border ${theme.border} ${theme.text} rounded-lg hover:${theme.secondary} transition-colors`}
                  >
                    Cancel
                  </button>
                </div>
              </div>
            )}
        </div>
      </div>

      {/* Current Data Summary */}
      <div className={`p-4 md:p-6 ${theme.card} border ${theme.border} rounded-lg w-full max-w-none`}>
        <h3 className={`text-lg font-semibold ${theme.text} mb-4`}>
          Current Data Summary
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div>
            <h4 className={`font-medium ${theme.text} mb-2`}>App Data</h4>
            <ul className={`space-y-3 ${theme.textSecondary}`}>
              <li className="flex items-start">
                <span className="mr-3 flex-shrink-0 leading-relaxed">•</span>
                <span className="leading-relaxed">Current Units: {state.currentUnits.toFixed(2)}</span>
              </li>
              <li className="flex items-start">
                <span className="mr-3 flex-shrink-0 leading-relaxed">•</span>
                <span className="leading-relaxed">Previous Units: {state.previousUnits.toFixed(2)}</span>
              </li>
              <li className="flex items-start">
                <span className="mr-3 flex-shrink-0 leading-relaxed">•</span>
                <span className="leading-relaxed">Unit Cost: R{state.unitCost.toFixed(2)}</span>
              </li>
              <li className="flex items-start">
                <span className="mr-3 flex-shrink-0 leading-relaxed">•</span>
                <span className="leading-relaxed">Threshold: {state.thresholdLimit.toFixed(2)} units</span>
              </li>
            </ul>
          </div>
          <div>
            <h4 className={`font-medium ${theme.text} mb-2`}>History</h4>
            <ul className={`space-y-3 ${theme.textSecondary}`}>
              <li className="flex items-start">
                <span className="mr-3 flex-shrink-0 leading-relaxed">•</span>
                <span className="leading-relaxed">Purchases: {state.purchases.length} records</span>
              </li>
              <li className="flex items-start">
                <span className="mr-3 flex-shrink-0 leading-relaxed">•</span>
                <span className="leading-relaxed">Usage Records: {state.usageHistory.length} records</span>
              </li>
              <li className="flex items-start">
                <span className="mr-3 flex-shrink-0 leading-relaxed">•</span>
                <span className="leading-relaxed">Last Reset: {state.lastResetDate ? new Date(state.lastResetDate).toLocaleDateString() : 'Never'}</span>
              </li>
              <li className="flex items-start">
                <span className="mr-3 flex-shrink-0 leading-relaxed">•</span>
                <span className="leading-relaxed">App Initialized: {state.isInitialized ? 'Yes' : 'No'}</span>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ResetOptions
