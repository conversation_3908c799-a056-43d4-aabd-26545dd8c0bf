import React from 'react'
import { <PERSON><PERSON><PERSON><PERSON>outer as Router } from 'react-router-dom'
import { AppProvider } from './context/AppContext'
import { ThemeProvider } from './context/ThemeContext'
import Layout from './components/Layout/Layout'
import { useAndroidBackButton } from './hooks/useAndroidBackButton'
import { useStatusBar } from './hooks/useStatusBar'

// Component that uses the back button hook and status bar
function AppContent() {
  useAndroidBackButton();
  useStatusBar();
  return <Layout />;
}

function App() {
  return (
    <Router>
      <ThemeProvider>
        <AppProvider>
          <AppContent />
        </AppProvider>
      </ThemeProvider>
    </Router>
  )
}

export default App
