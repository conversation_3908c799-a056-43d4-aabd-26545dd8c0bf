import React, { useState, useEffect, useRef } from 'react'
import { useTheme } from '../../context/ThemeContext'

function ScrollHint({ children, className = "" }) {
  const { theme } = useTheme()
  const [showHint, setShowHint] = useState(true)
  const [canScroll, setCanScroll] = useState(false)
  const scrollRef = useRef(null)

  // Check if content is scrollable
  useEffect(() => {
    const checkScrollable = () => {
      if (scrollRef.current) {
        const { scrollWidth, clientWidth } = scrollRef.current
        setCanScroll(scrollWidth > clientWidth)
      }
    }

    checkScrollable()
    window.addEventListener('resize', checkScrollable)
    return () => window.removeEventListener('resize', checkScrollable)
  }, [children])

  // Hide hint after user scrolls or after 5 seconds
  useEffect(() => {
    if (!canScroll) {
      setShowHint(false)
      return
    }

    const timer = setTimeout(() => {
      setShowHint(false)
    }, 5000)

    return () => clearTimeout(timer)
  }, [canScroll])

  const handleScroll = () => {
    setShowHint(false)
  }

  return (
    <div className={`relative ${className}`}>
      {/* Enhanced Mobile scroll hint */}
      {showHint && canScroll && (
        <div className="md:hidden absolute top-2 right-2 z-20 pointer-events-none">
          <div className={`flex items-center gap-2 ${theme.primary} text-white px-3 py-2 rounded-full shadow-lg backdrop-blur-sm animate-bounce`}>
            <span className="text-xs font-medium">Swipe to see more</span>
            <div className="flex space-x-1">
              <div className="w-1 h-1 bg-white rounded-full animate-pulse"></div>
              <div className="w-1 h-1 bg-white rounded-full animate-pulse" style={{animationDelay: '0.2s'}}></div>
              <div className="w-1 h-1 bg-white rounded-full animate-pulse" style={{animationDelay: '0.4s'}}></div>
            </div>
            <span className="text-sm">→</span>
          </div>
        </div>
      )}

      {/* Gradient fade indicator on the right */}
      {canScroll && (
        <div className={`md:hidden absolute top-0 right-0 bottom-0 w-8 bg-gradient-to-l ${theme.card}/80 to-transparent z-10 pointer-events-none`}></div>
      )}

      <div
        ref={scrollRef}
        className="overflow-x-auto overflow-y-hidden"
        style={{ WebkitOverflowScrolling: 'touch' }}
        onScroll={handleScroll}
      >
        {children}
      </div>
    </div>
  )
}

export default ScrollHint
