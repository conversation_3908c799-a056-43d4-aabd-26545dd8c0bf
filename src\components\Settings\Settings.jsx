import React, { useState, useEffect } from 'react'
import { useApp } from '../../context/AppContext'
import { useTheme } from '../../context/ThemeContext'
import { useSearchParams } from 'react-router-dom'
import ThemeSelector from './ThemeSelector'
import ResetOptions from './ResetOptions'
import HelpTooltip from '../Common/HelpTooltip'
import { HiCog, HiCurrencyDollar, HiExclamation, HiColorSwatch, HiRefresh, HiLightningBolt, HiGlobe } from 'react-icons/hi'
import { preventNumberInputScroll } from '../../hooks/usePreventNumberInputScroll'

function Settings() {
  const { state, updateSettings, testNotification } = useApp()
  const { theme, currentTheme } = useTheme()
  const [searchParams] = useSearchParams()

  // Debug logging
  console.log('🔧 Settings component loaded');
  console.log('🔔 testNotification function available:', typeof testNotification);
  console.log('🔔 Notification settings:', {
    enabled: state.notificationsEnabled,
    time: state.notificationTime,
    lastDate: state.lastNotificationDate
  });

  // Check browser notification support
  useEffect(() => {
    console.log('🌐 Browser notification support check:');
    console.log('- Notification in window:', 'Notification' in window);
    if ('Notification' in window) {
      console.log('- Current permission:', Notification.permission);
    }
    console.log('- User agent:', navigator.userAgent);
  }, []);

  // Helper function to get appropriate background for cards in dark mode
  const getCardBackground = (lightBg, darkBg = 'bg-gray-800/50') => {
    return currentTheme === 'dark' ? darkBg : lightBg
  }

  const [unitCost, setUnitCost] = useState(state.unitCost.toString())
  const [thresholdLimit, setThresholdLimit] = useState(state.thresholdLimit.toString())
  const [currency, setCurrency] = useState(state.currency || 'ZAR')
  const [customCurrencyName, setCustomCurrencyName] = useState(state.customCurrencyName || '')
  const [customCurrencySymbol, setCustomCurrencySymbol] = useState(state.customCurrencySymbol || '')
  const [unitName, setUnitName] = useState(state.unitName || 'kWh')
  const [customUnitName, setCustomUnitName] = useState(state.customUnitName || '')
  const [notificationsEnabled, setNotificationsEnabled] = useState(state.notificationsEnabled || false)
  const [notificationTime, setNotificationTime] = useState(state.notificationTime || '18:00')
  const [isSaving, setIsSaving] = useState(false)
  const [isTestingNotification, setIsTestingNotification] = useState(false)

  // Available currencies (5 well-known + custom option)
  const currencies = [
    { code: 'ZAR', name: 'South African Rand', symbol: 'R' },
    { code: 'USD', name: 'US Dollar', symbol: '$' },
    { code: 'EUR', name: 'Euro', symbol: '€' },
    { code: 'GBP', name: 'British Pound', symbol: '£' },
    { code: 'JPY', name: 'Japanese Yen', symbol: '¥' },
    { code: 'CUSTOM', name: 'Custom Currency', symbol: 'C' }
  ]

  // Unit name options
  const unitOptions = [
    { value: 'kWh', label: 'kWh (Kilowatt Hours)' },
    { value: 'Units', label: 'Units' },
    { value: 'custom', label: 'Custom' }
  ]

  const handleSaveSettings = async (e) => {
    e.preventDefault()
    setIsSaving(true)

    try {
      const newUnitCost = parseFloat(unitCost)
      const newThresholdLimit = parseFloat(thresholdLimit)

      if (isNaN(newUnitCost) || newUnitCost <= 0) {
        alert('Please enter a valid unit cost (greater than 0)')
        return
      }

      if (isNaN(newThresholdLimit) || newThresholdLimit < 0) {
        alert('Please enter a valid threshold limit (0 or greater)')
        return
      }

      if (unitName === 'custom' && !customUnitName.trim()) {
        alert('Please enter a custom unit name')
        return
      }

      if (currency === 'CUSTOM' && (!customCurrencyName.trim() || !customCurrencySymbol.trim())) {
        alert('Please enter both custom currency name and symbol')
        return
      }

      const selectedCurrency = currencies.find(c => c.code === currency)
      const finalCurrencySymbol = currency === 'CUSTOM' ? customCurrencySymbol : selectedCurrency?.symbol || 'R'

      updateSettings({
        unitCost: newUnitCost,
        thresholdLimit: newThresholdLimit,
        currency: currency,
        currencySymbol: finalCurrencySymbol,
        customCurrencyName: currency === 'CUSTOM' ? customCurrencyName.trim() : '',
        customCurrencySymbol: currency === 'CUSTOM' ? customCurrencySymbol.trim() : '',
        unitName: unitName,
        customUnitName: unitName === 'custom' ? customUnitName.trim() : '',
        notificationsEnabled: notificationsEnabled,
        notificationTime: notificationTime
      })

      alert('Settings saved successfully!')
    } catch (error) {
      console.error('Error saving settings:', error)
      alert('Error saving settings. Please try again.')
    } finally {
      setIsSaving(false)
    }
  }

  const handleTestNotification = async () => {
    setIsTestingNotification(true)
    try {
      console.log('🔔 Settings: Testing notification...')
      console.log('Current notification settings:', {
        enabled: notificationsEnabled,
        time: notificationTime,
        lastNotification: state.lastNotificationDate
      })

      const success = await testNotification()
      if (success) {
        alert('Test notification sent! Check your notification panel.')
      } else {
        alert('Unable to send test notification. Please check your notification permissions in device settings.')
      }
    } catch (error) {
      console.error('Test notification error:', error)
      alert('Error sending test notification. Please try again.')
    } finally {
      setIsTestingNotification(false)
    }
  }

  // Simple manual test function
  const handleManualTest = async () => {
    console.log('🔔 Manual notification test started');

    if (!('Notification' in window)) {
      alert('This browser does not support notifications');
      return;
    }

    if (Notification.permission === 'default') {
      console.log('🔔 Requesting permission...');
      const permission = await Notification.requestPermission();
      console.log('🔔 Permission result:', permission);
    }

    if (Notification.permission === 'granted') {
      console.log('🔔 Creating manual test notification...');
      const notification = new Notification('Manual Test', {
        body: 'This is a manual test notification!',
        icon: '/favicon.ico'
      });

      setTimeout(() => notification.close(), 5000);
      alert('Manual test notification sent!');
    } else {
      alert('Notification permission denied');
    }
  }

  // Test the notification timing logic
  const handleTimingTest = () => {
    const now = new Date();
    const [hours, minutes] = notificationTime.split(':').map(Number);
    const notificationTimeToday = new Date();
    notificationTimeToday.setHours(hours, minutes, 0, 0);

    // Create a 5-minute window around the notification time
    const notificationStart = new Date(notificationTimeToday);
    notificationStart.setMinutes(notificationStart.getMinutes() - 2);
    const notificationEnd = new Date(notificationTimeToday);
    notificationEnd.setMinutes(notificationEnd.getMinutes() + 3);

    const today = now.toDateString();
    const lastNotificationDateStr = state.lastNotificationDate ? new Date(state.lastNotificationDate).toDateString() : null;

    const timingInfo = {
      currentTime: now.toLocaleTimeString(),
      targetTime: notificationTimeToday.toLocaleTimeString(),
      windowStart: notificationStart.toLocaleTimeString(),
      windowEnd: notificationEnd.toLocaleTimeString(),
      today: today,
      lastNotificationDate: lastNotificationDateStr,
      inWindow: now >= notificationStart && now <= notificationEnd,
      notSentToday: lastNotificationDateStr !== today,
      shouldTrigger: (now >= notificationStart && now <= notificationEnd && lastNotificationDateStr !== today)
    };

    console.log('🕐 TIMING TEST RESULTS:', timingInfo);
    alert(`Timing Test Results:\n\nCurrent: ${timingInfo.currentTime}\nTarget: ${timingInfo.targetTime}\nWindow: ${timingInfo.windowStart} - ${timingInfo.windowEnd}\nIn Window: ${timingInfo.inWindow}\nNot Sent Today: ${timingInfo.notSentToday}\nShould Trigger: ${timingInfo.shouldTrigger}`);
  }

  // Set notification time to 2 minutes from now for quick testing
  const handleSetTestTime = () => {
    const now = new Date();
    now.setMinutes(now.getMinutes() + 2); // Changed to 2 minutes to ensure it's in the future
    const hours = now.getHours().toString().padStart(2, '0');
    const minutes = now.getMinutes().toString().padStart(2, '0');
    const testTime = `${hours}:${minutes}`;
    setNotificationTime(testTime);
    console.log('🕐 Set test time to:', testTime);
    alert(`Set notification time to ${testTime} (2 minutes from now). Don't forget to save!`);
  }

  // Set notification time to NOW for immediate testing
  const handleSetNowTime = () => {
    const now = new Date();
    const hours = now.getHours().toString().padStart(2, '0');
    const minutes = now.getMinutes().toString().padStart(2, '0');
    const testTime = `${hours}:${minutes}`;
    setNotificationTime(testTime);
    console.log('🕐 Set test time to NOW:', testTime);
    alert(`Set notification time to ${testTime} (RIGHT NOW). Save and it should trigger within 30 seconds!`);
  }

  // Manual trigger of notification check (for debugging)
  const handleManualCheck = () => {
    console.log('🔔 MANUAL CHECK TRIGGERED');

    // Simulate the same logic as in the hook
    const now = new Date();
    const [hours, minutes] = notificationTime.split(':').map(Number);
    const notificationTimeToday = new Date();
    notificationTimeToday.setHours(hours, minutes, 0, 0);

    // Create a 5-minute window around the notification time
    const notificationStart = new Date(notificationTimeToday);
    notificationStart.setMinutes(notificationStart.getMinutes() - 2);
    const notificationEnd = new Date(notificationTimeToday);
    notificationEnd.setMinutes(notificationEnd.getMinutes() + 3);

    const today = now.toDateString();
    const lastNotificationDateStr = state.lastNotificationDate ? new Date(state.lastNotificationDate).toDateString() : null;

    const shouldTrigger = now >= notificationStart && now <= notificationEnd && lastNotificationDateStr !== today;

    console.log('🔔 MANUAL CHECK RESULTS:', {
      currentTime: now.toLocaleTimeString(),
      targetTime: notificationTimeToday.toLocaleTimeString(),
      windowStart: notificationStart.toLocaleTimeString(),
      windowEnd: notificationEnd.toLocaleTimeString(),
      inWindow: now >= notificationStart && now <= notificationEnd,
      notSentToday: lastNotificationDateStr !== today,
      shouldTrigger: shouldTrigger
    });

    if (shouldTrigger) {
      console.log('🔔 CONDITIONS MET - SENDING MANUAL NOTIFICATION');
      if ('Notification' in window && Notification.permission === 'granted') {
        const notification = new Notification('⚡ Manual Check Notification', {
          body: "Manual check triggered a notification!",
          icon: '/favicon.ico'
        });
        setTimeout(() => notification.close(), 5000);
        alert('Manual check triggered notification!');
      } else {
        alert('Manual check: Conditions met but notification permission not granted');
      }
    } else {
      alert('Manual check: Conditions not met for notification');
    }
  }

  const settingsSections = [
    {
      id: 'general',
      title: 'General Settings',
      icon: HiCog,
      description: 'Configure unit costs and usage thresholds'
    },
    {
      id: 'appearance',
      title: 'Appearance',
      icon: HiColorSwatch,
      description: 'Customize themes and colors'
    },
    {
      id: 'reset',
      title: 'Reset Options',
      icon: HiRefresh,
      description: 'Factory reset and data management'
    }
  ]

  const [activeSection, setActiveSection] = useState('general')

  // Update active section based on URL parameter
  useEffect(() => {
    const section = searchParams.get('section')
    if (section && ['general', 'appearance', 'reset'].includes(section)) {
      setActiveSection(section)
    }
  }, [searchParams])

  return (
    <div className="space-y-6 pb-8 md:pb-6 settings-page-android">


      {/* Settings content */}
      <div className={`${theme.card} rounded-2xl shadow-lg border ${theme.border} w-full settings-content`}>
        <div className="p-4 md:p-6">
          {/* General Settings */}
          {activeSection === 'general' && (
            <div className="space-y-6">
              <form onSubmit={handleSaveSettings} className="space-y-6">
                {/* Currency Selection */}
                <div className={`p-4 md:p-6 ${theme.card} rounded-xl border ${theme.border} shadow-sm w-full`}>
                  <div className="flex items-center mb-4">
                    <div className={`p-2 rounded-lg bg-gradient-to-br ${theme.gradient} mr-3`}>
                      <HiGlobe className="h-5 w-5 text-white" />
                    </div>
                    <h3 className={`font-semibold ${theme.text} text-lg`}>Currency Settings</h3>
                  </div>

                  <div>
                    <label htmlFor="currency" className={`block text-sm font-semibold ${theme.text} mb-3`}>
                      💰 Currency
                    </label>
                    <select
                      id="currency"
                      value={currency}
                      onChange={(e) => setCurrency(e.target.value)}
                      className={`w-full px-4 py-4 border-4 ${theme.border} rounded-xl focus:ring-4 focus:ring-opacity-50 focus:${theme.border} ${theme.card} ${theme.text} font-bold shadow-lg hover:shadow-xl transition-all duration-200 min-w-0`}
                    >
                      {currencies.map((curr) => (
                        <option key={curr.code} value={curr.code}>
                          {curr.symbol} - {curr.name} ({curr.code})
                        </option>
                      ))}
                    </select>
                    <p className={`mt-3 text-xs ${theme.textSecondary} opacity-80 font-medium`}>
                      💡 Select your preferred currency for cost calculations
                    </p>

                    {/* Custom Currency Fields */}
                    {currency === 'CUSTOM' && (
                      <div className="mt-4 space-y-4">
                        <div>
                          <label htmlFor="customCurrencyName" className={`block text-sm font-semibold ${theme.text} mb-2`}>
                            🏷️ Custom Currency Name
                          </label>
                          <input
                            type="text"
                            id="customCurrencyName"
                            value={customCurrencyName}
                            onChange={(e) => setCustomCurrencyName(e.target.value)}
                            placeholder="Enter currency name (e.g., Bitcoin, Credits, Points)"
                            className={`w-full px-4 py-3 border-4 ${theme.border} rounded-xl focus:ring-4 focus:ring-opacity-50 focus:${theme.border} ${theme.card} ${theme.text} ${theme.textSecondary} font-bold shadow-lg hover:shadow-xl transition-all duration-200 min-w-0`}
                            required={currency === 'CUSTOM'}
                          />
                        </div>
                        <div>
                          <label htmlFor="customCurrencySymbol" className={`block text-sm font-semibold ${theme.text} mb-2`}>
                            💰 Custom Currency Symbol
                          </label>
                          <input
                            type="text"
                            id="customCurrencySymbol"
                            value={customCurrencySymbol}
                            onChange={(e) => setCustomCurrencySymbol(e.target.value)}
                            placeholder="Enter symbol (e.g., ₿, Cr, Pts)"
                            maxLength="5"
                            className={`w-full px-4 py-3 border-4 ${theme.border} rounded-xl focus:ring-4 focus:ring-opacity-50 focus:${theme.border} ${theme.card} ${theme.text} ${theme.textSecondary} font-bold shadow-lg hover:shadow-xl transition-all duration-200 min-w-0`}
                            required={currency === 'CUSTOM'}
                          />
                          <p className={`mt-3 text-xs ${theme.textSecondary} opacity-80 font-medium`}>
                            💰 This symbol will be displayed with all amounts (e.g., "{customCurrencySymbol || 'Cr'}100.00")
                          </p>
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                {/* Unit Configuration */}
                <div className={`p-4 md:p-6 ${getCardBackground('bg-gradient-to-br from-blue-50 to-indigo-50', 'bg-gray-800/50')} rounded-xl border ${theme.border} shadow-sm w-full`}>
                  <div className="flex items-center mb-4">
                    <div className="p-2 rounded-lg bg-gradient-to-br from-blue-400 to-indigo-500 mr-3">
                      <HiLightningBolt className="h-5 w-5 text-white" />
                    </div>
                    <h3 className={`font-semibold ${theme.text} text-lg`}>Unit Settings</h3>
                  </div>

                  <div className="space-y-4">
                    <div>
                      <label htmlFor="unitName" className={`block text-sm font-semibold ${theme.text} mb-3`}>
                        ⚡ Unit Name
                      </label>
                      <select
                        id="unitName"
                        value={unitName}
                        onChange={(e) => setUnitName(e.target.value)}
                        className={`w-full px-4 py-4 border-4 border-blue-300 rounded-xl focus:ring-4 focus:ring-blue-500 focus:border-blue-600 ${theme.card} ${theme.text} font-bold shadow-lg hover:shadow-xl transition-all duration-200 hover:border-blue-400 min-w-0`}
                      >
                        {unitOptions.map((option) => (
                          <option key={option.value} value={option.value}>
                            {option.label}
                          </option>
                        ))}
                      </select>
                      <p className={`mt-3 text-xs ${theme.textSecondary} opacity-80 font-medium`}>
                        ⚡ Choose how your units are displayed throughout the app
                      </p>
                    </div>

                    {/* Custom Unit Name Input */}
                    {unitName === 'custom' && (
                      <div>
                        <label htmlFor="customUnitName" className={`block text-sm font-semibold ${theme.text} mb-3`}>
                          🎯 Custom Unit Name
                        </label>
                        <input
                          type="text"
                          id="customUnitName"
                          value={customUnitName}
                          onChange={(e) => setCustomUnitName(e.target.value)}
                          placeholder="Enter custom unit name (e.g., Donkey, Credits, Points)"
                          className={`w-full px-4 py-4 border-4 border-purple-300 rounded-xl focus:ring-4 focus:ring-purple-500 focus:border-purple-600 ${theme.card} ${theme.text} placeholder-gray-400 font-bold shadow-lg hover:shadow-xl transition-all duration-200 hover:border-purple-400 min-w-0`}
                          required={unitName === 'custom'}
                        />
                        <p className={`mt-3 text-xs ${theme.textSecondary} opacity-80 font-medium`}>
                          🎯 This name will be used everywhere (e.g., "Cost per {customUnitName || 'YourUnit'}")
                        </p>
                      </div>
                    )}
                  </div>
                </div>

                {/* Unit Cost Setting */}
                <div className={`p-4 md:p-6 ${getCardBackground('bg-gradient-to-br from-violet-50 to-purple-50', 'bg-gray-800/50')} rounded-xl border ${theme.border} shadow-sm w-full`}>
                  <div className="flex items-center mb-4">
                    <div className="p-2 rounded-lg bg-gradient-to-br from-violet-400 to-purple-500 mr-3">
                      <HiCurrencyDollar className="h-5 w-5 text-white" />
                    </div>
                    <h3 className={`font-semibold ${theme.text} text-lg`}>Cost Settings</h3>
                  </div>

                  <div>
                    <label htmlFor="unitCost" className={`block text-sm font-semibold ${theme.text} mb-3`}>
                      💵 Cost per {unitName === 'custom' ? (customUnitName || 'Unit') : unitName}
                    </label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                        <div className="p-1 rounded-lg bg-gradient-to-br from-violet-400 to-purple-500">
                          <HiCurrencyDollar className="h-4 w-4 text-white" />
                        </div>
                      </div>
                      <input
                        type="number"
                        id="unitCost"
                        value={unitCost}
                        onChange={(e) => setUnitCost(e.target.value)}
                        onWheel={preventNumberInputScroll}
                        step="0.01"
                        min="0.01"
                        placeholder="Enter cost per unit"
                        className={`w-full pl-12 pr-4 py-4 border-4 border-violet-300 rounded-xl focus:ring-4 focus:ring-violet-500 focus:border-violet-600 ${theme.card} ${theme.text} placeholder-gray-400 font-bold shadow-lg hover:shadow-xl transition-all duration-200 hover:border-violet-400 min-w-0`}
                        required
                      />
                    </div>
                    <p className={`mt-3 text-xs ${theme.textSecondary} opacity-80 font-medium`}>
                      💡 This is used to calculate the cost of your electricity usage
                    </p>
                  </div>
                </div>

                {/* Threshold Limit Setting */}
                <div className={`p-4 md:p-6 ${getCardBackground('bg-gradient-to-br from-amber-50 to-yellow-50', 'bg-gray-800/50')} rounded-xl border ${theme.border} shadow-sm w-full`}>
                  <div className="flex items-center mb-4">
                    <div className="p-2 rounded-lg bg-gradient-to-br from-amber-400 to-yellow-500 mr-3">
                      <HiExclamation className="h-5 w-5 text-white" />
                    </div>
                    <h3 className={`font-semibold ${theme.text} text-lg`}>Alert Settings</h3>
                  </div>

                  <div>
                    <label htmlFor="thresholdLimit" className={`block text-sm font-semibold ${theme.text} mb-3`}>
                      ⚠️ Low {unitName === 'custom' ? (customUnitName || 'Units') : unitName} Warning Threshold
                    </label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                        <div className="p-1 rounded-lg bg-gradient-to-br from-amber-400 to-yellow-500">
                          <HiExclamation className="h-4 w-4 text-white" />
                        </div>
                      </div>
                      <input
                        type="number"
                        id="thresholdLimit"
                        value={thresholdLimit}
                        onChange={(e) => setThresholdLimit(e.target.value)}
                        onWheel={preventNumberInputScroll}
                        step="0.01"
                        min="0"
                        placeholder="Enter low units warning threshold"
                        className={`w-full pl-12 pr-4 py-4 border-4 border-amber-300 rounded-xl focus:ring-4 focus:ring-amber-500 focus:border-amber-600 ${theme.card} ${theme.text} placeholder-gray-400 font-bold shadow-lg hover:shadow-xl transition-all duration-200 hover:border-amber-400 min-w-0`}
                        required
                      />
                    </div>
                    <p className={`mt-3 text-xs ${theme.textSecondary} opacity-80 font-medium`}>
                      ⚠️ You'll receive a warning when your remaining units drop below this threshold
                    </p>
                  </div>
                </div>

                {/* Current Settings Preview */}
                <div className={`p-4 md:p-6 ${getCardBackground('bg-gradient-to-br from-slate-50 to-gray-50', 'bg-gray-800/50')} rounded-xl border ${theme.border} shadow-sm w-full`}>
                  <div className="flex items-center mb-4">
                    <div className="p-2 rounded-lg bg-gradient-to-br from-gray-400 to-slate-500 mr-3">
                      <HiCog className="h-5 w-5 text-white" />
                    </div>
                    <h3 className={`font-semibold ${theme.text} text-lg`}>Current Settings Preview</h3>
                  </div>
                  <div className="space-y-3 text-sm">
                    <div className={`flex justify-between items-center p-3 ${getCardBackground('bg-white/60', 'bg-gray-700/50')} rounded-lg`}>
                      <span className={`${theme.textSecondary} font-medium`}>Currency:</span>
                      <span className={`${theme.text} font-bold`}>
                        {state.currency === 'CUSTOM'
                          ? `${state.customCurrencySymbol || 'C'} - ${state.customCurrencyName || 'Custom Currency'}`
                          : `${currencies.find(c => c.code === (state.currency || 'ZAR'))?.symbol || 'R'} - ${currencies.find(c => c.code === (state.currency || 'ZAR'))?.name || 'South African Rand'}`
                        }
                      </span>
                    </div>
                    <div className={`flex justify-between items-center p-3 ${getCardBackground('bg-white/60', 'bg-gray-700/50')} rounded-lg`}>
                      <span className={`${theme.textSecondary} font-medium`}>Unit Name:</span>
                      <span className={`${theme.text} font-bold`}>
                        {state.unitName === 'custom' ? (state.customUnitName || 'Units') : (state.unitName || 'kWh')}
                      </span>
                    </div>
                    <div className={`flex justify-between items-center p-3 ${getCardBackground('bg-white/60', 'bg-gray-700/50')} rounded-lg`}>
                      <span className={`${theme.textSecondary} font-medium`}>Unit Cost:</span>
                      <span className={`${theme.text} font-bold`}>
                        {state.currencySymbol || 'R'}{state.unitCost.toFixed(2)} per {state.unitName === 'custom' ? (state.customUnitName || 'Unit') : (state.unitName || 'kWh')}
                      </span>
                    </div>
                    <div className={`flex justify-between items-center p-3 ${getCardBackground('bg-white/60', 'bg-gray-700/50')} rounded-lg`}>
                      <span className={`${theme.textSecondary} font-medium`}>Low Units Warning:</span>
                      <span className={`${theme.text} font-bold`}>
                        {state.thresholdLimit.toFixed(2)} {state.unitName === 'custom' ? (state.customUnitName || 'Units') : (state.unitName || 'kWh')}
                      </span>
                    </div>
                    <div className={`flex justify-between items-center p-3 ${getCardBackground('bg-white/60', 'bg-gray-700/50')} rounded-lg`}>
                      <span className={`${theme.textSecondary} font-medium`}>Last Reset:</span>
                      <span className={`${theme.text} font-bold`}>
                        {state.lastResetDate
                          ? new Date(state.lastResetDate).toLocaleDateString()
                          : 'Never'
                        }
                      </span>
                    </div>
                  </div>
                </div>

                {/* Notification Settings */}
                <div className={`p-4 md:p-6 ${getCardBackground('bg-gradient-to-br from-indigo-50 to-purple-50', 'bg-gray-800/50')} rounded-xl border ${theme.border} shadow-sm w-full`}>
                  <div className="flex items-center mb-4">
                    <div className="p-2 rounded-lg bg-gradient-to-br from-indigo-400 to-purple-500 mr-3">
                      <HiExclamation className="h-5 w-5 text-white" />
                    </div>
                    <h3 className={`font-semibold ${theme.text} text-lg`}>Notification Settings</h3>
                  </div>

                  <div className="space-y-4">
                    {/* Enable/Disable Notifications */}
                    <div className="flex items-center justify-between">
                      <div>
                        <label className={`text-sm font-semibold ${theme.text}`}>
                          🔔 Daily Usage Reminders
                        </label>
                        <p className={`text-xs ${theme.textSecondary} opacity-80 mt-3`}>
                          Get reminded to record your electricity usage every day
                        </p>
                      </div>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          checked={notificationsEnabled}
                          onChange={(e) => setNotificationsEnabled(e.target.checked)}
                          className="sr-only peer"
                          aria-label="Enable daily usage reminder notifications"
                        />
                        <div className={`w-11 h-6 ${currentTheme === 'dark' ? 'bg-gray-600' : 'bg-gray-200'} peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-indigo-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600`}></div>
                      </label>
                    </div>

                    {/* Notification Time */}
                    {notificationsEnabled && (
                      <div>
                        <label htmlFor="notificationTime" className={`block text-sm font-semibold ${theme.text} mb-3`}>
                          ⏰ Reminder Time
                        </label>
                        <input
                          type="time"
                          id="notificationTime"
                          value={notificationTime}
                          onChange={(e) => setNotificationTime(e.target.value)}
                          className={`w-full px-4 py-4 border-4 border-indigo-300 rounded-xl focus:ring-4 focus:ring-indigo-500 focus:border-indigo-600 ${theme.card} ${theme.text} font-bold shadow-lg hover:shadow-xl transition-all duration-200 hover:border-indigo-400 min-w-0`}
                        />
                        <p className={`mt-3 text-xs ${theme.textSecondary} opacity-80 font-medium`}>
                          ⏰ You'll receive a notification at this time every day
                        </p>
                      </div>
                    )}

                    {/* Test Notification Button */}
                    {notificationsEnabled && (
                      <div className="pt-4 border-t border-gray-200 dark:border-gray-600">
                        <button
                          type="button"
                          onClick={handleTestNotification}
                          disabled={isTestingNotification}
                          className={`w-full px-4 py-3 bg-gradient-to-r from-indigo-500 to-purple-600 text-white rounded-lg font-semibold hover:opacity-90 transition-all duration-300 focus:ring-4 focus:ring-indigo-300 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-xl`}
                        >
                          <div className="flex items-center justify-center gap-2">
                            {isTestingNotification ? (
                              <>
                                <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                                Sending Test...
                              </>
                            ) : (
                              <>
                                🔔 Test Notification
                              </>
                            )}
                          </div>
                        </button>
                        <p className={`mt-2 text-xs ${theme.textSecondary} opacity-80 text-center`}>
                          Send a test notification to verify everything is working
                        </p>

                        {/* Manual Test Button for debugging */}
                        <button
                          type="button"
                          onClick={handleManualTest}
                          className={`mt-2 w-full px-4 py-2 text-sm font-medium text-white bg-red-500 hover:bg-red-600 rounded-lg transition-colors duration-200`}
                        >
                          🔧 Manual Browser Test
                        </button>
                        <p className={`mt-1 text-xs ${theme.textSecondary} opacity-80 text-center`}>
                          Direct browser notification test (debugging)
                        </p>

                        {/* Timing Test Button */}
                        <button
                          type="button"
                          onClick={handleTimingTest}
                          className={`mt-2 w-full px-4 py-2 text-sm font-medium text-white bg-yellow-500 hover:bg-yellow-600 rounded-lg transition-colors duration-200`}
                        >
                          🕐 Test Timing Logic
                        </button>
                        <p className={`mt-1 text-xs ${theme.textSecondary} opacity-80 text-center`}>
                          Check if current time matches notification window
                        </p>

                        {/* Quick Test Time Button */}
                        <button
                          type="button"
                          onClick={handleSetTestTime}
                          className={`mt-2 w-full px-4 py-2 text-sm font-medium text-white bg-green-500 hover:bg-green-600 rounded-lg transition-colors duration-200`}
                        >
                          ⏰ Set Time +2 Min
                        </button>
                        <p className={`mt-1 text-xs ${theme.textSecondary} opacity-80 text-center`}>
                          Set notification time to 2 minutes from now for testing
                        </p>

                        {/* Set to NOW Button */}
                        <button
                          type="button"
                          onClick={handleSetNowTime}
                          className={`mt-2 w-full px-4 py-2 text-sm font-medium text-white bg-purple-500 hover:bg-purple-600 rounded-lg transition-colors duration-200`}
                        >
                          🚀 Set Time to NOW
                        </button>
                        <p className={`mt-1 text-xs ${theme.textSecondary} opacity-80 text-center`}>
                          Set notification time to current time (immediate test)
                        </p>

                        {/* Manual Check Button */}
                        <button
                          type="button"
                          onClick={handleManualCheck}
                          className={`mt-2 w-full px-4 py-2 text-sm font-medium text-white bg-orange-500 hover:bg-orange-600 rounded-lg transition-colors duration-200`}
                        >
                          🔍 Manual Check NOW
                        </button>
                        <p className={`mt-1 text-xs ${theme.textSecondary} opacity-80 text-center`}>
                          Manually run the notification check logic right now
                        </p>
                      </div>
                    )}
                  </div>
                </div>

                {/* Save Button */}
                <button
                  type="submit"
                  disabled={isSaving}
                  className={`w-full bg-gradient-to-r ${theme.gradient} text-white py-4 px-6 rounded-xl font-semibold hover:opacity-90 transition-all duration-300 focus:ring-4 focus:ring-opacity-50 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98]`}
                >
                  <div className="flex items-center justify-center gap-2">
                    {isSaving ? (
                      <>
                        <div className="animate-spin rounded-full h-5 w-5 border-2 border-white border-t-transparent"></div>
                        Saving Settings...
                      </>
                    ) : (
                      <>
                        <HiCog className="h-5 w-5" />
                        Save Settings
                      </>
                    )}
                  </div>
                </button>
              </form>
            </div>
          )}

          {/* Appearance Settings */}
          {activeSection === 'appearance' && (
            <div className="space-y-6 pb-8 md:pb-6">
              <ThemeSelector />
            </div>
          )}

          {/* Reset Options */}
          {activeSection === 'reset' && (
            <div className="space-y-6">
              <ResetOptions />
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default Settings
